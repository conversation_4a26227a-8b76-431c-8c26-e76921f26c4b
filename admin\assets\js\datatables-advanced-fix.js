
// إصلاح نهائي ومتقدم لمشاكل DataTables
$.fn.dataTable.ext.errMode = "none";

// متغير لتتبع الجداول المهيأة
window.initializedTables = window.initializedTables || {};

function destroyExistingTable(selector) {
    try {
        if ($.fn.DataTable.isDataTable(selector)) {
            $(selector).DataTable().destroy();
            $(selector).empty();
            console.log("Destroyed existing DataTable: " + selector);
            delete window.initializedTables[selector];
        }
    } catch (error) {
        console.warn("Error destroying table:", error);
    }
}

function initSafeDataTable(selector, options = {}) {
    try {
        // التحقق من وجود العنصر
        if (!$(selector).length) {
            console.warn("Element not found: " + selector);
            return null;
        }

        // التحقق من عدم التهيئة المسبقة
        if (window.initializedTables[selector]) {
            console.log("Table already initialized: " + selector);
            return window.initializedTables[selector];
        }

        // تدمير أي جدول موجود
        destroyExistingTable(selector);

        // الإعدادات الافتراضية
        const defaultOptions = {
            responsive: true,
            pageLength: 25,
            autoWidth: false,
            processing: false,
            serverSide: false,
            stateSave: false,
            destroy: true,
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                info: "عرض _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                infoEmpty: "عرض 0 إلى 0 من أصل 0 مدخل",
                lengthMenu: "عرض _MENU_ مدخلات",
                search: "البحث:",
                zeroRecords: "لم يتم العثور على نتائج مطابقة",
                paginate: {
                    first: "الأول",
                    last: "الأخير",
                    next: "التالي",
                    previous: "السابق"
                }
            },
            dom: "<\"row\"<\"col-sm-12 col-md-6\"l><\"col-sm-12 col-md-6\"f>>rtip"
        };

        // دمج الإعدادات
        const finalOptions = $.extend(true, {}, defaultOptions, options);

        // تهيئة الجدول
        const table = $(selector).DataTable(finalOptions);

        // حفظ مرجع الجدول
        window.initializedTables[selector] = table;

        console.log("DataTable initialized successfully: " + selector);
        return table;

    } catch (error) {
        console.error("DataTable Error for " + selector + ":", error);
        return null;
    }
}

// تهيئة تلقائية للجداول الشائعة
$(document).ready(function() {
    // انتظار قصير للتأكد من تحميل DOM
    setTimeout(function() {
        // قائمة الجداول الشائعة
        const commonTables = [
            "#usersTable",
            "#instructorsTable",
            "#studentsTable",
            "#coursesTable",
            "#sessionsTable",
            "#requestsTable",
            "#paymentsTable",
            "#reportsTable"
        ];

        commonTables.forEach(function(tableId) {
            if ($(tableId).length && !window.initializedTables[tableId]) {
                initSafeDataTable(tableId);
            }
        });
    }, 500);
});

// تنظيف عند إغلاق الصفحة
$(window).on("beforeunload", function() {
    Object.keys(window.initializedTables).forEach(function(selector) {
        try {
            if ($.fn.DataTable.isDataTable(selector)) {
                $(selector).DataTable().destroy();
            }
        } catch (e) {
            console.warn("Error destroying DataTable on unload:", e);
        }
    });
    window.initializedTables = {};
});

// دالة لإعادة تهيئة جدول معين
function reinitializeTable(selector, options = {}) {
    destroyExistingTable(selector);
    return initSafeDataTable(selector, options);
}

console.log("Advanced DataTables Fix Loaded Successfully");
