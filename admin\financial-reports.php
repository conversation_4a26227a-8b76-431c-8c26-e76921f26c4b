<?php
session_start();
require_once '../includes/database_manager_clean.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'التقارير المالية';
$breadcrumbs = [
    ['title' => 'التقارير المالية']
];

// معاملات التصفية
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // بداية الشهر الحالي
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // اليوم الحالي
$instructor_filter = $_GET['instructor'] ?? '';
$course_filter = $_GET['course'] ?? '';

// تهيئة المتغيرات
$financial_stats = [];
$daily_revenue = [];
$top_courses = [];
$top_instructors = [];
$recent_transactions = [];
$instructors = [];
$courses = [];

try {
    $conn = DatabaseManager::getInstance()->getConnection();

    // جلب إعدادات العمولة
    $stmt = $conn->query("SELECT setting_value FROM system_settings WHERE setting_key = 'platform_commission'");
    $platform_commission = $stmt ? (float)$stmt->fetchColumn() : 30;
    
    // الإحصائيات العامة
    $stmt = $conn->prepare("
        SELECT
            COUNT(DISTINCT ce.id) as total_enrollments,
            COUNT(DISTINCT CASE WHEN ce.payment_status = 'completed' THEN ce.id END) as paid_enrollments,
            COUNT(DISTINCT CASE WHEN c.course_type = 'free' THEN ce.id END) as free_enrollments,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price END), 0) as total_revenue,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price * (? / 100) END), 0) as platform_earnings,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price * ((100 - ?) / 100) END), 0) as instructor_earnings,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'pending' THEN c.price END), 0) as pending_payments,
            COALESCE(AVG(CASE WHEN ce.payment_status = 'completed' THEN c.price END), 0) as avg_course_price
        FROM course_enrollments ce
        JOIN courses c ON ce.course_id = c.id
        WHERE ce.enrolled_at BETWEEN ? AND DATE_ADD(?, INTERVAL 1 DAY)
    ");
    $stmt->execute([$platform_commission, $platform_commission, $date_from, $date_to]);
    $financial_stats = $stmt->fetch();
    
    // الإيرادات اليومية للرسم البياني
    $stmt = $conn->prepare("
        SELECT
            DATE(ce.enrolled_at) as date,
            COUNT(*) as enrollments,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price END), 0) as daily_revenue,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price * (? / 100) END), 0) as daily_commission
        FROM course_enrollments ce
        JOIN courses c ON ce.course_id = c.id
        WHERE ce.enrolled_at BETWEEN ? AND DATE_ADD(?, INTERVAL 1 DAY)
        GROUP BY DATE(ce.enrolled_at)
        ORDER BY date ASC
    ");
    $stmt->execute([$platform_commission, $date_from, $date_to]);
    $daily_revenue = $stmt->fetchAll();
    
    // أفضل الكورسات من ناحية الإيرادات
    $where_conditions = ["ce.enrolled_at BETWEEN ? AND DATE_ADD(?, INTERVAL 1 DAY)"];
    $params = [$date_from, $date_to];
    
    if ($instructor_filter) {
        $where_conditions[] = "c.instructor_id = ?";
        $params[] = $instructor_filter;
    }
    
    if ($course_filter) {
        $where_conditions[] = "c.id = ?";
        $params[] = $course_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $conn->prepare("
        SELECT 
            c.id,
            c.title,
            c.price,
            u.name as instructor_name,
            COUNT(ce.id) as total_enrollments,
            COUNT(CASE WHEN ce.payment_status = 'completed' THEN 1 END) as paid_enrollments,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price END), 0) as course_revenue,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price * (? / 100) END), 0) as platform_share,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price * ((100 - ?) / 100) END), 0) as instructor_share
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND $where_clause
        WHERE c.course_type = 'paid'
        GROUP BY c.id
        HAVING course_revenue > 0
        ORDER BY course_revenue DESC
        LIMIT 10
    ");
    $params_with_commission = array_merge([$platform_commission, $platform_commission], $params);
    $stmt->execute($params_with_commission);
    $top_courses = $stmt->fetchAll();
    
    // أفضل المدربين من ناحية الإيرادات
    $stmt = $conn->prepare("
        SELECT 
            u.id,
            u.name,
            u.email,
            COUNT(DISTINCT c.id) as courses_count,
            COUNT(ce.id) as total_enrollments,
            COUNT(CASE WHEN ce.payment_status = 'completed' THEN 1 END) as paid_enrollments,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price END), 0) as total_revenue,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price * ((100 - ?) / 100) END), 0) as instructor_earnings,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN c.price * (? / 100) END), 0) as platform_earnings
        FROM users u
        JOIN courses c ON u.id = c.instructor_id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND $where_clause
        WHERE u.role = 'instructor' AND c.course_type = 'paid'
        GROUP BY u.id
        HAVING total_revenue > 0
        ORDER BY total_revenue DESC
        LIMIT 10
    ");
    $stmt->execute($params_with_commission);
    $top_instructors = $stmt->fetchAll();
    
    // المعاملات الأخيرة
    $stmt = $conn->prepare("
        SELECT 
            ce.*,
            c.title as course_title,
            c.price as course_price,
            u.name as student_name,
            u.email as student_email,
            i.name as instructor_name
        FROM course_enrollments ce
        JOIN courses c ON ce.course_id = c.id
        JOIN users u ON ce.student_id = u.id
        JOIN users i ON c.instructor_id = i.id
        WHERE ce.enrolled_at BETWEEN ? AND DATE_ADD(?, INTERVAL 1 DAY)
        AND c.course_type = 'paid'
        ORDER BY ce.enrolled_at DESC
        LIMIT 20
    ");
    $stmt->execute([$date_from, $date_to]);
    $recent_transactions = $stmt->fetchAll();
    
    // جلب قائمة المدربين للفلترة
    $instructors_stmt = $conn->query("
        SELECT u.id, u.name 
        FROM users u 
        JOIN courses c ON u.id = c.instructor_id 
        WHERE u.role = 'instructor' AND c.course_type = 'paid'
        GROUP BY u.id 
        ORDER BY u.name
    ");
    $instructors = $instructors_stmt->fetchAll();
    
    // جلب قائمة الكورسات للفلترة
    $courses_stmt = $conn->query("
        SELECT c.id, c.title 
        FROM courses c 
        WHERE c.course_type = 'paid' 
        ORDER BY c.title
    ");
    $courses = $courses_stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب البيانات المالية';
}

include 'includes/header.php';
?>

<!-- الإحصائيات المالية -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-success mb-1"><?php echo number_format($financial_stats['total_revenue'], 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">إجمالي الإيرادات</p>
                    <small class="text-muted">
                        <i class="fas fa-chart-line me-1"></i>
                        <?php echo $financial_stats['paid_enrollments']; ?> عملية دفع
                    </small>
                </div>
                <div class="text-success">
                    <i class="fas fa-money-bill-wave fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-warning mb-1"><?php echo number_format($financial_stats['platform_earnings'], 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">عمولة المنصة</p>
                    <small class="text-muted">
                        <i class="fas fa-percentage me-1"></i>
                        <?php echo $platform_commission; ?>% عمولة
                    </small>
                </div>
                <div class="text-warning">
                    <i class="fas fa-percentage fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-info mb-1"><?php echo number_format($financial_stats['instructor_earnings'], 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">أرباح المدربين</p>
                    <small class="text-muted">
                        <i class="fas fa-user-tie me-1"></i>
                        <?php echo 100 - $platform_commission; ?>% للمدربين
                    </small>
                </div>
                <div class="text-info">
                    <i class="fas fa-user-tie fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card danger">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-danger mb-1"><?php echo number_format($financial_stats['pending_payments'], 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">مدفوعات معلقة</p>
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        في انتظار التأكيد
                    </small>
                </div>
                <div class="text-danger">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أدوات التصفية -->
<div class="card-admin mb-4" data-aos="fade-up">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="<?php echo $date_from; ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="<?php echo $date_to; ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">المدرب</label>
                <select name="instructor" class="form-select">
                    <option value="">جميع المدربين</option>
                    <?php foreach ($instructors as $instructor): ?>
                    <option value="<?php echo $instructor['id']; ?>" 
                            <?php echo $instructor_filter == $instructor['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($instructor['name']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الكورس</label>
                <select name="course" class="form-select">
                    <option value="">جميع الكورسات</option>
                    <?php foreach ($courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>" 
                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-admin-primary">
                    <i class="fas fa-filter me-1"></i>
                    تطبيق الفلتر
                </button>
                <a href="financial-reports.php" class="btn btn-outline-secondary">
                    <i class="fas fa-redo me-1"></i>
                    إعادة تعيين
                </a>
                <button type="button" class="btn btn-success" onclick="exportData('excel', 'export-financial.php', 'financial-report.xlsx')">
                    <i class="fas fa-file-excel me-1"></i>
                    تصدير Excel
                </button>
            </div>
        </form>
    </div>
</div>

<div class="row">
    <!-- الرسم البياني للإيرادات -->
    <div class="col-lg-8 mb-4" data-aos="fade-up">
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    الإيرادات اليومية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- توزيع الإيرادات -->
    <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="100">
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع الإيرادات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="distributionChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- أفضل الكورسات -->
    <div class="col-lg-6 mb-4" data-aos="fade-up">
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    أفضل الكورسات (الإيرادات)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($top_courses)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد بيانات للفترة المحددة</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الكورس</th>
                                <th>المدرب</th>
                                <th>التسجيلات</th>
                                <th>الإيرادات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_courses as $index => $course): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary me-2"><?php echo $index + 1; ?></span>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($course['title']); ?></div>
                                            <small class="text-muted"><?php echo number_format($course['price'], 2); ?> ر.س</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <small><?php echo htmlspecialchars($course['instructor_name']); ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $course['paid_enrollments']; ?></span>
                                </td>
                                <td>
                                    <strong class="text-success"><?php echo number_format($course['course_revenue'], 2); ?> ر.س</strong>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- أفضل المدربين -->
    <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="100">
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    أفضل المدربين (الإيرادات)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($top_instructors)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد بيانات للفترة المحددة</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المدرب</th>
                                <th>الكورسات</th>
                                <th>التسجيلات</th>
                                <th>الأرباح</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_instructors as $index => $instructor): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-warning me-2"><?php echo $index + 1; ?></span>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($instructor['name']); ?></div>
                                            <small class="text-muted"><?php echo htmlspecialchars($instructor['email']); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo $instructor['courses_count']; ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $instructor['paid_enrollments']; ?></span>
                                </td>
                                <td>
                                    <strong class="text-success"><?php echo number_format($instructor['instructor_earnings'], 2); ?> ر.س</strong>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- المعاملات الأخيرة -->
<div class="card-admin" data-aos="fade-up">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            المعاملات الأخيرة
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($recent_transactions)): ?>
        <div class="text-center py-5">
            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد معاملات</h5>
            <p class="text-muted">لا توجد معاملات مالية في الفترة المحددة</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>التاريخ</th>
                        <th>الطالب</th>
                        <th>الكورس</th>
                        <th>المدرب</th>
                        <th>المبلغ</th>
                        <th>عمولة المنصة</th>
                        <th>ربح المدرب</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_transactions as $transaction): ?>
                    <tr>
                        <td>
                            <small><?php echo date('Y-m-d H:i', strtotime($transaction['enrolled_at'])); ?></small>
                        </td>
                        <td>
                            <div>
                                <div class="fw-bold"><?php echo htmlspecialchars($transaction['student_name']); ?></div>
                                <small class="text-muted"><?php echo htmlspecialchars($transaction['student_email']); ?></small>
                            </div>
                        </td>
                        <td>
                            <div class="fw-bold"><?php echo htmlspecialchars($transaction['course_title']); ?></div>
                        </td>
                        <td>
                            <small><?php echo htmlspecialchars($transaction['instructor_name']); ?></small>
                        </td>
                        <td>
                            <strong class="text-primary"><?php echo number_format($transaction['course_price'], 2); ?> ر.س</strong>
                        </td>
                        <td>
                            <span class="text-warning"><?php echo number_format($transaction['course_price'] * ($platform_commission / 100), 2); ?> ر.س</span>
                        </td>
                        <td>
                            <span class="text-success"><?php echo number_format($transaction['course_price'] * ((100 - $platform_commission) / 100), 2); ?> ر.س</span>
                        </td>
                        <td>
                            <?php
                            $status_badges = [
                                'completed' => 'bg-success',
                                'pending' => 'bg-warning',
                                'failed' => 'bg-danger'
                            ];
                            $status_names = [
                                'completed' => 'مكتمل',
                                'pending' => 'معلق',
                                'failed' => 'فاشل'
                            ];
                            ?>
                            <span class="badge <?php echo $status_badges[$transaction['payment_status']] ?? 'bg-secondary'; ?>">
                                <?php echo $status_names[$transaction['payment_status']] ?? $transaction['payment_status']; ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// بيانات الرسم البياني
const dailyRevenueData = <?php echo json_encode($daily_revenue); ?>;

// رسم بياني للإيرادات اليومية
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: dailyRevenueData.map(item => item.date),
        datasets: [{
            label: 'الإيرادات اليومية',
            data: dailyRevenueData.map(item => item.daily_revenue),
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'عمولة المنصة',
            data: dailyRevenueData.map(item => item.daily_commission),
            borderColor: 'rgb(245, 158, 11)',
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString('ar-SA') + ' ر.س';
                    }
                }
            }
        }
    }
});

// رسم بياني دائري لتوزيع الإيرادات
const distributionCtx = document.getElementById('distributionChart').getContext('2d');
new Chart(distributionCtx, {
    type: 'doughnut',
    data: {
        labels: ['عمولة المنصة', 'أرباح المدربين'],
        datasets: [{
            data: [<?php echo $financial_stats['platform_earnings']; ?>, <?php echo $financial_stats['instructor_earnings']; ?>],
            backgroundColor: [
                'rgb(245, 158, 11)',
                'rgb(34, 197, 94)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((value / total) * 100).toFixed(1);
                        return context.label + ': ' + value.toLocaleString('ar-SA') + ' ر.س (' + percentage + '%)';
                    }
                }
            }
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
