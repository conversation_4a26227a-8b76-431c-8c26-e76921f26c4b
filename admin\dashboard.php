<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'لوحة التحكم الرئيسية';
$breadcrumbs = [
    ['title' => 'لوحة التحكم']
];

// جلب الإحصائيات من قاعدة البيانات
require_once '../includes/database_manager_clean.php';

try {
    // إحصائيات المستخدمين
    $stats['total_users'] = $dbClean->count('users');
    $stats['total_students'] = $dbClean->count('users', ['role' => 'student']);
    $stats['total_instructors'] = $dbClean->count('users', ['role' => 'instructor']);
    $stats['total_admins'] = $dbClean->count('users', ['role' => 'admin']);

    // إحصائيات الكورسات
    $stats['total_courses'] = $dbClean->count('courses');
    $stats['active_courses'] = $dbClean->count('courses', ['status' => 'published']);
    $stats['draft_courses'] = $dbClean->count('courses', ['status' => 'draft']);
    $stats['pending_courses'] = $dbClean->count('courses', ['status' => 'pending']);

    // إحصائيات التسجيلات
    $stats['total_enrollments'] = $dbClean->count('course_enrollments');
    $stats['active_enrollments'] = $dbClean->count('course_enrollments', ['status' => 'active']);
    $stats['completed_enrollments'] = $dbClean->count('course_enrollments', ['status' => 'completed']);

    // إحصائيات الواجبات والاختبارات
    $stats['total_assignments'] = $dbClean->count('assignments');
    $stats['total_quizzes'] = $dbClean->count('quizzes');
    $stats['total_submissions'] = $dbClean->count('assignment_submissions');
    $stats['total_quiz_attempts'] = $dbClean->count('quiz_attempts');

    // إحصائيات الوجبات
    $stats['total_meals'] = $dbClean->count('meals');
    $stats['published_meals'] = $dbClean->count('meals', ['status' => 'published']);
    $stats['meal_categories'] = $dbClean->count('meal_categories');

    // إحصائيات الملفات
    $stats['total_files'] = $dbClean->count('file_uploads');
    $stats['total_videos'] = $dbClean->count('file_uploads', ['category' => 'video']);
    $stats['total_documents'] = $dbClean->count('file_uploads', ['category' => 'document']);

    // إحصائيات المدفوعات
    $stats['total_payments'] = $dbClean->count('payments');
    $stats['completed_payments'] = $dbClean->count('payments', ['status' => 'completed']);

    // حساب الإيرادات
    $revenueQuery = "SELECT
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
        SUM(CASE WHEN status = 'completed' AND MONTH(payment_date) = MONTH(CURRENT_DATE()) AND YEAR(payment_date) = YEAR(CURRENT_DATE()) THEN amount ELSE 0 END) as monthly_revenue,
        SUM(CASE WHEN status = 'completed' THEN commission_amount ELSE 0 END) as platform_commission
        FROM payments";

    $revenueData = $dbClean->query($revenueQuery);
    if (!empty($revenueData)) {
        $stats['total_revenue'] = $revenueData[0]['total_revenue'] ?: 0;
        $stats['monthly_revenue'] = $revenueData[0]['monthly_revenue'] ?: 0;
        $stats['platform_commission'] = $revenueData[0]['platform_commission'] ?: 0;
    } else {
        $stats['total_revenue'] = 0;
        $stats['monthly_revenue'] = 0;
        $stats['platform_commission'] = 0;
    }

    // طلبات الانضمام المعلقة
    $stats['pending_requests'] = $dbClean->count('course_enrollments', ['status' => 'pending']);

    // إحصائيات النشاط
    $stats['total_activities'] = $dbClean->count('activity_logs');
    $stats['today_activities'] = $dbClean->count('activity_logs', ['DATE(created_at)' => date('Y-m-d')]);

} catch (Exception $e) {
    error_log("Dashboard stats error: " . $e->getMessage());
    // قيم افتراضية في حالة الخطأ
    $stats = [
        'total_users' => 0,
        'total_students' => 0,
        'total_instructors' => 0,
        'total_admins' => 0,
        'total_courses' => 0,
        'active_courses' => 0,
        'draft_courses' => 0,
        'pending_courses' => 0,
        'total_enrollments' => 0,
        'active_enrollments' => 0,
        'completed_enrollments' => 0,
        'total_assignments' => 0,
        'total_quizzes' => 0,
        'total_submissions' => 0,
        'total_quiz_attempts' => 0,
        'total_meals' => 0,
        'published_meals' => 0,
        'meal_categories' => 0,
        'total_files' => 0,
        'total_videos' => 0,
        'total_documents' => 0,
        'total_payments' => 0,
        'completed_payments' => 0,
        'total_revenue' => 0,
        'monthly_revenue' => 0,
        'platform_commission' => 0,
        'pending_requests' => 0,
        'total_activities' => 0,
        'today_activities' => 0
    ];
}

$recent_activities = [];
$top_courses = [];
$recent_users = [];
$error = null;

// متغيرات إضافية للتوافق مع الكود القديم (سيتم تحديثها من قاعدة البيانات)
$session_count = 0;
$student_count = 0;
$instructor_count = 0;
$course_count = 0;
$platform_commission = 30;
$pending_requests = [];
$upcoming_sessions = [];
$recent_instructors = [];
$recent_paid_courses = [];
$course_revenue_stats = [];

// جلب الإحصائيات الشاملة
try {
    // إحصائيات المستخدمين
    $stmt = $conn->query("
        SELECT
            COUNT(*) as total_users,
            COUNT(CASE WHEN role = 'student' THEN 1 END) as total_students,
            COUNT(CASE WHEN role = 'instructor' THEN 1 END) as total_instructors,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_month
        FROM users
    ");
    $user_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats = array_merge($stats, $user_stats);

    // إحصائيات الكورسات
    $stmt = $conn->query("
        SELECT
            COUNT(*) as total_courses,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_courses,
            COUNT(CASE WHEN course_type = 'free' THEN 1 END) as free_courses,
            COUNT(CASE WHEN course_type = 'paid' THEN 1 END) as paid_courses,
            AVG(CASE WHEN course_type = 'paid' THEN price END) as avg_price
        FROM courses
    ");
    $course_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats = array_merge($stats, $course_stats);

    // إحصائيات الجلسات
    $stmt = $conn->query("
        SELECT
            COUNT(*) as total_sessions,
            COUNT(CASE WHEN start_time > NOW() THEN 1 END) as upcoming_sessions,
            COUNT(CASE WHEN start_time < NOW() AND end_time > NOW() THEN 1 END) as live_sessions,
            COUNT(CASE WHEN end_time < NOW() THEN 1 END) as completed_sessions
        FROM sessions
    ");
    $session_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats = array_merge($stats, $session_stats);

    // إحصائيات الواجبات والاختبارات
    try {
        $stmt = $conn->query("
            SELECT
                COALESCE((SELECT COUNT(*) FROM assignments), 0) as total_assignments,
                COALESCE((SELECT COUNT(*) FROM quizzes), 0) as total_quizzes,
                COALESCE((SELECT COUNT(*) FROM certificates), 0) as total_certificates,
                COALESCE((SELECT COUNT(*) FROM join_requests WHERE status = 'pending'), 0) as pending_requests
        ");
        $other_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats = array_merge($stats, $other_stats);
    } catch (PDOException $e) {
        // إذا لم تكن الجداول موجودة، استخدم قيم افتراضية
        $stats['total_assignments'] = 0;
        $stats['total_quizzes'] = 0;
        $stats['total_certificates'] = 0;
        $stats['pending_requests'] = 0;
    }

    // تحديث المتغيرات للتوافق مع الكود القديم
    $session_count = $stats['total_sessions'];
    $student_count = $stats['total_students'];
    $instructor_count = $stats['total_instructors'];
    $course_count = $stats['total_courses'];

    // جلب إعدادات العمولة
    $platform_commission = (float)getSystemSetting('platform_commission', 30);

    // الإيرادات والعمولات
    $stmt = $conn->prepare("
        SELECT
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN ce.payment_amount END), 0) as total_revenue,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' AND ce.enrolled_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN ce.payment_amount END), 0) as monthly_revenue,
            COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN ce.payment_amount * (? / 100) END), 0) as platform_commission_amount
        FROM course_enrollments ce
        JOIN courses c ON ce.course_id = c.id
        WHERE c.course_type = 'paid'
    ");
    $stmt->execute([$platform_commission]);
    $revenue_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats = array_merge($stats, $revenue_stats);

    // إحصائيات الكورسات المدفوعة والمجانية
    $stmt = $conn->query("
        SELECT
            COUNT(CASE WHEN course_type = 'free' THEN 1 END) as free_courses,
            COUNT(CASE WHEN course_type = 'paid' THEN 1 END) as paid_courses,
            COALESCE(SUM(CASE WHEN course_type = 'paid' THEN price END), 0) as total_revenue,
            COALESCE(AVG(CASE WHEN course_type = 'paid' THEN price END), 0) as avg_price
        FROM courses
    ");
    $course_revenue_stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // جلب طلبات الانضمام المعلقة
    try {
        $stmt = $conn->query("
            SELECT
                jr.name as student_name,
                jr.email as student_email,
                jr.course_interest as instructor_name,
                jr.created_at as request_date
            FROM join_requests jr
            WHERE jr.status = 'pending'
            ORDER BY jr.created_at DESC
            LIMIT 5
        ");
        $pending_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // إذا لم يكن جدول join_requests موجود، استخدم بيانات بديلة
        $pending_requests = [];
    }

    // الجلسات القادمة
    try {
        $stmt = $conn->query("
            SELECT s.*, c.title as course_title, u.name as instructor_name
            FROM sessions s
            JOIN courses c ON s.course_id = c.id
            JOIN users u ON c.instructor_id = u.id
            WHERE s.start_time > NOW()
            AND s.status = 'scheduled'
            ORDER BY s.start_time ASC
            LIMIT 5
        ");
        $upcoming_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $upcoming_sessions = [];
    }

    // آخر المدربين المضافين
    try {
        $stmt = $conn->query("
            SELECT u.*,
            (SELECT COUNT(*) FROM courses WHERE instructor_id = u.id) as course_count,
            COALESCE((SELECT COUNT(DISTINCT e.student_id)
             FROM courses c
             LEFT JOIN course_enrollments e ON c.id = e.course_id
             WHERE c.instructor_id = u.id), 0) as student_count
            FROM users u
            WHERE u.role = 'instructor'
            ORDER BY u.created_at DESC
            LIMIT 5
        ");
        $recent_instructors = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $recent_instructors = [];
    }

    // أحدث الكورسات المدفوعة
    try {
        $stmt = $conn->query("
            SELECT c.*, u.name as instructor_name
            FROM courses c
            JOIN users u ON c.instructor_id = u.id
            WHERE c.course_type = 'paid'
            ORDER BY c.created_at DESC
            LIMIT 5
        ");
        $recent_paid_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $recent_paid_courses = [];
    }

    // النشاطات الأخيرة
    $stmt = $conn->query("
        SELECT 'user_registration' as type, u.name as title, u.created_at as date, u.role as extra
        FROM users u
        WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

        UNION ALL

        SELECT 'course_creation' as type, c.title as title, c.created_at as date, u.name as extra
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        WHERE c.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

        UNION ALL

        SELECT 'enrollment' as type, c.title as title, ce.enrolled_at as date, u.name as extra
        FROM course_enrollments ce
        JOIN courses c ON ce.course_id = c.id
        JOIN users u ON ce.student_id = u.id
        WHERE ce.enrolled_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

        ORDER BY date DESC
        LIMIT 10
    ");
    $recent_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // أفضل الكورسات
    $stmt = $conn->query("
        SELECT c.*, u.name as instructor_name,
               COUNT(ce.id) as enrollment_count,
               COALESCE(SUM(ce.payment_amount), 0) as revenue,
               COALESCE(AVG(cr.rating), 0) as avg_rating
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id
        LEFT JOIN course_reviews cr ON c.id = cr.course_id
        GROUP BY c.id
        ORDER BY enrollment_count DESC, revenue DESC
        LIMIT 5
    ");
    $top_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // المستخدمون الجدد
    $stmt = $conn->query("
        SELECT u.*,
               CASE
                   WHEN u.role = 'instructor' THEN (SELECT COUNT(*) FROM courses WHERE instructor_id = u.id)
                   WHEN u.role = 'student' THEN (SELECT COUNT(*) FROM course_enrollments WHERE student_id = u.id)
                   ELSE 0
               END as activity_count
        FROM users u
        WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ORDER BY u.created_at DESC
        LIMIT 5
    ");
    $recent_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب البيانات';
}

// جلب البيانات الحديثة للعرض
try {
    // أحدث المستخدمين المسجلين
    $recent_users = $dbClean->getAll('users', 'id, name, email, role, created_at', 'created_at DESC', 5);

    // أحدث الكورسات
    $recent_courses = $dbClean->query("
        SELECT c.*, u.name as instructor_name
        FROM courses c
        LEFT JOIN users u ON c.instructor_id = u.id
        ORDER BY c.created_at DESC
        LIMIT 5
    ");

    // أحدث التسجيلات
    $recent_enrollments = $dbClean->query("
        SELECT ce.*, c.title as course_title, u.name as student_name
        FROM course_enrollments ce
        LEFT JOIN courses c ON ce.course_id = c.id
        LEFT JOIN users u ON ce.student_id = u.id
        ORDER BY ce.enrolled_at DESC
        LIMIT 5
    ");

    // أحدث المدفوعات
    $recent_payments = $dbClean->query("
        SELECT p.*, u.name as user_name, c.title as course_title
        FROM payments p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN courses c ON p.course_id = c.id
        ORDER BY p.payment_date DESC
        LIMIT 5
    ");

    // أحدث الأنشطة
    $recent_activities = $dbClean->query("
        SELECT al.*, u.name as user_name
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ORDER BY al.created_at DESC
        LIMIT 10
    ");

    // إحصائيات الكورسات حسب الفئة
    $courses_by_category = $dbClean->query("
        SELECT c.name as category_name, COUNT(co.id) as courses_count
        FROM categories c
        LEFT JOIN courses co ON c.id = co.category_id
        GROUP BY c.id, c.name
        ORDER BY courses_count DESC
        LIMIT 10
    ");

    // أفضل المدربين (حسب عدد الطلاب)
    $top_instructors = $dbClean->query("
        SELECT u.name, u.email, COUNT(DISTINCT ce.student_id) as students_count,
               COUNT(DISTINCT c.id) as courses_count
        FROM users u
        LEFT JOIN courses c ON u.id = c.instructor_id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id
        WHERE u.role = 'instructor'
        GROUP BY u.id, u.name, u.email
        ORDER BY students_count DESC
        LIMIT 5
    ");

    // إحصائيات الوجبات حسب النوع
    $meals_by_type = $dbClean->query("
        SELECT meal_type, COUNT(*) as count
        FROM meals
        WHERE status = 'published'
        GROUP BY meal_type
        ORDER BY count DESC
    ");

} catch (Exception $e) {
    error_log("Dashboard data fetch error: " . $e->getMessage());
    $recent_users = [];
    $recent_courses = [];
    $recent_enrollments = [];
    $recent_payments = [];
    $recent_activities = [];
    $courses_by_category = [];
    $top_instructors = [];
    $meals_by_type = [];
}

include 'includes/header.php';
?>

<!-- ترحيب بالمدير -->
<div class="row mb-4" data-aos="fade-up">
    <div class="col-12">
        <div class="card-admin">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-primary mb-2">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            مرحباً بك، <?php echo htmlspecialchars($_SESSION['name']); ?>
                        </h3>
                        <p class="text-muted mb-0">لوحة التحكم الرئيسية - نظام إدارة التعلم الإلكتروني</p>
                    </div>
                    <div class="text-end">
                        <div class="mb-2">
                            <small class="text-muted">آخر تسجيل دخول:</small>
                            <strong class="d-block"><?php echo date('Y-m-d H:i'); ?></strong>
                        </div>
                        <span class="badge bg-success">النظام نشط</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات الرئيسية -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-primary mb-1"><?php echo number_format($stats['total_users']); ?></h3>
                    <p class="text-muted mb-0">إجمالي المستخدمين</p>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>
                        +<?php echo $stats['new_users_month']; ?> هذا الشهر
                    </small>
                </div>
                <div class="text-primary">
                    <i class="fas fa-users fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-success mb-1"><?php echo number_format($stats['total_students']); ?></h3>
                    <p class="text-muted mb-0">الطلاب</p>
                    <small class="text-muted">
                        <i class="fas fa-graduation-cap me-1"></i>
                        متعلمين نشطين
                    </small>
                </div>
                <div class="text-success">
                    <i class="fas fa-user-graduate fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-warning mb-1"><?php echo number_format($stats['total_instructors']); ?></h3>
                    <p class="text-muted mb-0">المدربين</p>
                    <small class="text-muted">
                        <i class="fas fa-chalkboard-teacher me-1"></i>
                        خبراء معتمدين
                    </small>
                </div>
                <div class="text-warning">
                    <i class="fas fa-chalkboard-teacher fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="400">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-info mb-1"><?php echo number_format($stats['total_courses']); ?></h3>
                    <p class="text-muted mb-0">الكورسات</p>
                    <small class="text-success">
                        <i class="fas fa-check-circle me-1"></i>
                        <?php echo $stats['active_courses']; ?> نشط
                    </small>
                </div>
                <div class="text-info">
                    <i class="fas fa-book fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- إحصائيات إضافية -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="500">
            <div class="stats-card danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-danger mb-1"><?php echo number_format($stats['total_enrollments']); ?></h3>
                        <p class="text-muted mb-0">التسجيلات</p>
                        <small class="text-success">
                            <i class="fas fa-user-plus me-1"></i>
                            <?php echo $stats['active_enrollments']; ?> نشط
                        </small>
                    </div>
                    <div class="text-danger">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="600">
            <div class="stats-card secondary">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-secondary mb-1"><?php echo number_format($stats['total_meals']); ?></h3>
                        <p class="text-muted mb-0">الوجبات</p>
                        <small class="text-info">
                            <i class="fas fa-utensils me-1"></i>
                            <?php echo $stats['published_meals']; ?> منشورة
                        </small>
                    </div>
                    <div class="text-secondary">
                        <i class="fas fa-utensils fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="700">
            <div class="stats-card dark">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-dark mb-1"><?php echo number_format($stats['total_assignments'] + $stats['total_quizzes']); ?></h3>
                        <p class="text-muted mb-0">الواجبات والاختبارات</p>
                        <small class="text-warning">
                            <i class="fas fa-tasks me-1"></i>
                            <?php echo $stats['total_submissions']; ?> تسليم
                        </small>
                    </div>
                    <div class="text-dark">
                        <i class="fas fa-tasks fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="800">
            <div class="stats-card light">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-primary mb-1"><?php echo number_format($stats['total_revenue'], 0); ?></h3>
                        <p class="text-muted mb-0">الإيرادات (ريال)</p>
                        <small class="text-success">
                            <i class="fas fa-chart-line me-1"></i>
                            <?php echo number_format($stats['monthly_revenue'], 0); ?> هذا الشهر
                        </small>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الملفات والأنشطة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="900">
            <div class="stats-card info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-info mb-1"><?php echo number_format($stats['total_files']); ?></h3>
                        <p class="text-muted mb-0">الملفات المرفوعة</p>
                        <small class="text-primary">
                            <i class="fas fa-video me-1"></i>
                            <?php echo $stats['total_videos']; ?> فيديو
                        </small>
                    </div>
                    <div class="text-info">
                        <i class="fas fa-file fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="1000">
            <div class="stats-card success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-success mb-1"><?php echo number_format($stats['total_payments']); ?></h3>
                        <p class="text-muted mb-0">المدفوعات</p>
                        <small class="text-success">
                            <i class="fas fa-check-circle me-1"></i>
                            <?php echo $stats['completed_payments']; ?> مكتملة
                        </small>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-credit-card fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="1100">
            <div class="stats-card warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-warning mb-1"><?php echo number_format($stats['total_activities']); ?></h3>
                        <p class="text-muted mb-0">الأنشطة</p>
                        <small class="text-warning">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo $stats['today_activities']; ?> اليوم
                        </small>
                    </div>
                    <div class="text-warning">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="1200">
            <div class="stats-card danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="text-danger mb-1"><?php echo number_format($stats['pending_requests']); ?></h3>
                        <p class="text-muted mb-0">طلبات معلقة</p>
                        <small class="text-danger">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            تحتاج مراجعة
                        </small>
                    </div>
                    <div class="text-danger">
                        <i class="fas fa-hourglass-half fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- البيانات الحديثة من قاعدة البيانات -->
<div class="row mb-4">
    <!-- أحدث المستخدمين -->
    <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="100">
        <div class="card shadow-sm border-0">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    أحدث المستخدمين المسجلين
                </h6>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($recent_users)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>تاريخ التسجيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <span class="avatar-title bg-primary rounded-circle">
                                                    <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                                                </span>
                                            </div>
                                            <?php echo htmlspecialchars($user['name']); ?>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php
                                            echo $user['role'] === 'admin' ? 'danger' :
                                                ($user['role'] === 'instructor' ? 'warning' : 'info');
                                        ?>">
                                            <?php
                                            $roles = ['admin' => 'مدير', 'instructor' => 'مدرب', 'student' => 'طالب'];
                                            echo $roles[$user['role']] ?? $user['role'];
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?>
                                        </small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مستخدمين جدد</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- أحدث الكورسات -->
    <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="200">
        <div class="card shadow-sm border-0">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-book me-2"></i>
                    أحدث الكورسات
                </h6>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($recent_courses)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>عنوان الكورس</th>
                                    <th>المدرب</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_courses as $course): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($course['title']); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars(substr($course['short_description'] ?? '', 0, 50)); ?>...
                                            </small>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($course['instructor_name'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <?php if ($course['is_free']): ?>
                                            <span class="badge bg-success">مجاني</span>
                                        <?php else: ?>
                                            <strong><?php echo number_format($course['price'], 2); ?> ريال</strong>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php
                                            echo $course['status'] === 'published' ? 'success' :
                                                ($course['status'] === 'draft' ? 'warning' : 'secondary');
                                        ?>">
                                            <?php
                                            $statuses = ['published' => 'منشور', 'draft' => 'مسودة', 'pending' => 'معلق'];
                                            echo $statuses[$course['status']] ?? $course['status'];
                                            ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-book fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد كورسات جديدة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card animate-on-scroll">
                <div class="stats-icon">
                    <i class="fas fa-video"></i>
                </div>
                <div class="stats-number text-warning"><?php echo number_format($session_count); ?></div>
                <div class="stats-label">إجمالي الجلسات</div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        مجدولة ومكتملة
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card animate-on-scroll">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-number text-primary">
                    <?php echo number_format(($student_count + $instructor_count) * 0.85); ?>
                </div>
                <div class="stats-label">المستخدمين النشطين</div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-pulse me-1"></i>
                        آخر 30 يوم
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card animate-on-scroll">
                <div class="stats-icon">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="stats-number text-success">
                    <?php echo number_format($course_count * 0.6); ?>
                </div>
                <div class="stats-label">الشهادات الصادرة</div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-award me-1"></i>
                        مكتملة
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card animate-on-scroll">
                <div class="stats-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stats-number text-warning">4.8</div>
                <div class="stats-label">متوسط التقييم</div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-thumbs-up me-1"></i>
                        من 5 نجوم
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الإيرادات والكورسات المدفوعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-enhanced animate-on-scroll">
                <div class="card-header-custom d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        إحصائيات الإيرادات والكورسات المدفوعة
                    </h5>
                    <div>
                        <a href="commission-settings.php" class="btn btn-warning btn-sm me-2">
                            <i class="fas fa-cog me-1"></i>
                            إعدادات العمولة
                        </a>
                        <a href="courses-revenue.php" class="btn btn-info btn-sm">
                            <i class="fas fa-chart-line me-1"></i>
                            تقرير مفصل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card bg-success text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-gift"></i>
                                </div>
                                <div class="stats-number"><?php echo isset($course_revenue_stats['free_courses']) ? $course_revenue_stats['free_courses'] : 0; ?></div>
                                <div class="stats-label">كورسات مجانية</div>
                                <div class="mt-2">
                                    <small class="opacity-75">
                                        <i class="fas fa-heart me-1"></i>
                                        متاح للجميع
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card bg-warning text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stats-number"><?php echo isset($course_revenue_stats['paid_courses']) ? $course_revenue_stats['paid_courses'] : 0; ?></div>
                                <div class="stats-label">كورسات مدفوعة</div>
                                <div class="mt-2">
                                    <small class="opacity-75">
                                        <i class="fas fa-crown me-1"></i>
                                        مميزة
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card bg-primary text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="stats-number"><?php echo number_format(isset($course_revenue_stats['total_revenue']) ? $course_revenue_stats['total_revenue'] : 0, 0); ?></div>
                                <div class="stats-label">إجمالي الإيرادات (ريال)</div>
                                <div class="mt-2">
                                    <small class="opacity-75">
                                        <i class="fas fa-chart-line me-1"></i>
                                        قيمة الكورسات
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card bg-info text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="stats-number"><?php echo $platform_commission; ?>%</div>
                                <div class="stats-label">عمولة المنصة</div>
                                <div class="mt-2">
                                    <small class="opacity-75">
                                        <i class="fas fa-cog me-1"></i>
                                        قابلة للتعديل
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل إضافية -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-calculator text-primary me-2"></i>
                                        حساب الأرباح
                                    </h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <p class="mb-1"><strong>أرباح المنصة:</strong></p>
                                            <h5 class="text-warning">
                                                <?php
                                                $platform_earnings = isset($course_revenue_stats['total_revenue']) ?
                                                    ($course_revenue_stats['total_revenue'] * ($platform_commission / 100)) : 0;
                                                echo number_format($platform_earnings, 0);
                                                ?> ريال
                                            </h5>
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-1"><strong>أرباح المدربين:</strong></p>
                                            <h5 class="text-success">
                                                <?php
                                                $instructor_earnings = isset($course_revenue_stats['total_revenue']) ?
                                                    ($course_revenue_stats['total_revenue'] * ((100 - $platform_commission) / 100)) : 0;
                                                echo number_format($instructor_earnings, 0);
                                                ?> ريال
                                            </h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-chart-bar text-info me-2"></i>
                                        متوسط الأسعار
                                    </h6>
                                    <p class="mb-1"><strong>متوسط سعر الكورس المدفوع:</strong></p>
                                    <h5 class="text-primary">
                                        <?php echo number_format(isset($course_revenue_stats['avg_price']) ? $course_revenue_stats['avg_price'] : 0, 0); ?> ريال
                                    </h5>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        يشمل جميع الكورسات المدفوعة
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- طلبات الانضمام الجديدة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-enhanced animate-on-scroll">
                <div class="card-header-custom d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-clock me-2"></i>
                        طلبات الانضمام الجديدة
                    </h5>
                    <a href="manage_join_requests.php" class="btn btn-light btn-sm">
                        <i class="fas fa-eye me-1"></i>
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <?php if (!empty($pending_requests)): ?>
                        <div class="table-responsive">
                            <table class="table table-enhanced data-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-user me-1"></i>الطالب</th>
                                        <th><i class="fas fa-chalkboard-teacher me-1"></i>المدرب</th>
                                        <th><i class="fas fa-calendar me-1"></i>تاريخ الطلب</th>
                                        <th class="no-sort"><i class="fas fa-cogs me-1"></i>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (is_array($pending_requests)): ?>
                                    <?php foreach ($pending_requests as $request): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($request['student_name']); ?></div>
                                                        <small class="text-muted"><?php echo htmlspecialchars($request['student_email']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo htmlspecialchars($request['instructor_name']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-muted">
                                                    <?php echo date('Y-m-d', strtotime($request['request_date'])); ?>
                                                </span>
                                                <br>
                                                <small class="text-primary">
                                                    <?php echo date('H:i', strtotime($request['request_date'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <a href="manage_join_requests.php" class="btn btn-primary-custom btn-sm">
                                                    <i class="fas fa-edit me-1"></i>
                                                    إدارة
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد طلبات انضمام جديدة</h6>
                            <p class="text-muted mb-0">سيتم عرض الطلبات الجديدة هنا عند وصولها</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الكورسات المدفوعة الحديثة -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        الكورسات المدفوعة الحديثة
                    </h5>
                    <a href="courses-revenue.php" class="btn btn-sm btn-warning">عرض الكل</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_paid_courses)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد كورسات مدفوعة</h6>
                            <p class="text-muted mb-0">سيتم عرض الكورسات المدفوعة هنا</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الكورس</th>
                                        <th>المدرب</th>
                                        <th>السعر</th>
                                        <th>العمولة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_paid_courses as $course): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($course['title']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        <?php echo date('Y-m-d', strtotime($course['created_at'])); ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo htmlspecialchars($course['instructor_name']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    <?php echo number_format($course['price'], 0); ?> ريال
                                                </strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <small class="text-warning">
                                                        منصة: <?php echo number_format($course['price'] * ($platform_commission / 100), 0); ?> ريال
                                                    </small>
                                                    <br>
                                                    <small class="text-success">
                                                        مدرب: <?php echo number_format($course['price'] * ((100 - $platform_commission) / 100), 0); ?> ريال
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- الجلسات القادمة -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">الجلسات القادمة</h5>
                    <a href="manage-sessions.php" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="card-body">
                    <?php if (empty($upcoming_sessions)): ?>
                        <p class="text-muted">لا توجد جلسات قادمة</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>الكورس</th>
                                        <th>المدرب</th>
                                        <th>الموعد</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($upcoming_sessions as $session): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($session['course_title']); ?></td>
                                            <td><?php echo htmlspecialchars($session['instructor_name']); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($session['start_time'])); ?></td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $session['status']; ?></span>
                                            </td>
                                            <td>
                                                <a href="session-details.php?id=<?php echo $session['id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- آخر المدربين -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آخر المدربين المضافين</h5>
                    <a href="manage-instructors.php" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_instructors)): ?>
                        <p class="text-muted">لا يوجد مدربين</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الكورسات</th>
                                        <th>الطلاب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_instructors as $instructor): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($instructor['name']); ?></td>
                                            <td><?php echo htmlspecialchars($instructor['email']); ?></td>
                                            <td><?php echo $instructor['course_count']; ?></td>
                                            <td><?php echo $instructor['student_count']; ?></td>
                                            <td>
                                                <a href="instructor-details.php?id=<?php echo $instructor['id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">روابط سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-2">
                            <a href="manage-instructors.php" class="btn btn-lg btn-primary w-100">
                                <i class="fas fa-chalkboard-teacher mb-2"></i>
                                <br>
                                إدارة المدربين
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="manage-courses.php" class="btn btn-lg btn-success w-100">
                                <i class="fas fa-book mb-2"></i>
                                <br>
                                إدارة الكورسات
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="commission-settings.php" class="btn btn-lg btn-warning w-100">
                                <i class="fas fa-percentage mb-2"></i>
                                <br>
                                إعدادات العمولة
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="courses-revenue.php" class="btn btn-lg btn-info w-100">
                                <i class="fas fa-money-bill-wave mb-2"></i>
                                <br>
                                إيرادات الكورسات
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="manage-sessions.php" class="btn btn-lg btn-secondary w-100">
                                <i class="fas fa-video mb-2"></i>
                                <br>
                                إدارة الجلسات
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="reports.php" class="btn btn-lg btn-dark w-100">
                                <i class="fas fa-chart-bar mb-2"></i>
                                <br>
                                التقارير والإحصائيات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة DataTables
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        new DataTable(table, {
            pageLength: 5,
            searching: false,
            paging: false,
            info: false,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            }
        });
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
