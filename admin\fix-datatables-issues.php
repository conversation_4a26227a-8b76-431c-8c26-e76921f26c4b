<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

function logMessage($message, $type = 'info') {
    $color = $type === 'success' ? 'green' : ($type === 'error' ? 'red' : ($type === 'warning' ? 'orange' : 'blue'));
    echo "<div style='color: $color; margin: 5px 0;'>$message</div>";
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل DataTables</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h4><i class="fas fa-table me-2"></i>إصلاح مشاكل DataTables</h4>
            </div>
            <div class="card-body">
                <?php
                echo "<h5>🔧 بدء إصلاح مشاكل DataTables...</h5>";
                
                // 1. فحص ملفات JavaScript
                echo "<h6>📁 فحص ملفات JavaScript</h6>";
                
                $js_files = [
                    'assets/js/datatables-fix.js' => 'ملف إصلاح DataTables',
                    '../assets/js/common.js' => 'ملف JavaScript العام'
                ];
                
                foreach ($js_files as $file => $description) {
                    if (file_exists($file)) {
                        logMessage("✅ $description موجود: $file", 'success');
                    } else {
                        logMessage("❌ $description مفقود: $file", 'error');
                    }
                }
                
                // 2. إنشاء ملف إصلاح DataTables محدث
                echo "<h6>🛠️ إنشاء ملف إصلاح DataTables محدث</h6>";
                
                $datatables_fix_content = '
/**
 * إصلاح شامل لمشاكل DataTables
 * يحل مشكلة "Cannot reinitialise DataTable"
 */

// منع إعادة تهيئة DataTables
$.fn.dataTable.ext.errMode = "none";

// دالة آمنة لتهيئة DataTable
function initSafeDataTable(selector, options = {}) {
    try {
        // التحقق من وجود العنصر
        if (!$(selector).length) {
            console.warn("Element not found: " + selector);
            return null;
        }
        
        // تدمير DataTable الموجود إن وجد
        if ($.fn.DataTable.isDataTable(selector)) {
            $(selector).DataTable().destroy();
            console.log("Destroyed existing DataTable: " + selector);
        }
        
        // الإعدادات الافتراضية
        const defaultOptions = {
            responsive: true,
            pageLength: 25,
            autoWidth: false,
            processing: false,
            serverSide: false,
            stateSave: false,
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                info: "عرض _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                infoEmpty: "عرض 0 إلى 0 من أصل 0 مدخل",
                infoFiltered: "(مرشح من _MAX_ إجمالي المدخلات)",
                lengthMenu: "عرض _MENU_ مدخلات",
                loadingRecords: "جاري التحميل...",
                processing: "جاري المعالجة...",
                search: "البحث:",
                zeroRecords: "لم يتم العثور على نتائج مطابقة",
                paginate: {
                    first: "الأول",
                    last: "الأخير", 
                    next: "التالي",
                    previous: "السابق"
                }
            }
        };
        
        // دمج الإعدادات
        const finalOptions = $.extend(true, {}, defaultOptions, options);
        
        // إنشاء DataTable جديد
        const table = $(selector).DataTable(finalOptions);
        console.log("DataTable initialized successfully: " + selector);
        
        return table;
        
    } catch (error) {
        console.error("Error initializing DataTable:", error);
        return null;
    }
}

// تهيئة تلقائية عند تحميل الصفحة
$(document).ready(function() {
    // تأخير قصير للتأكد من تحميل جميع العناصر
    setTimeout(function() {
        
        // جدول المستخدمين
        if ($("#usersTable").length) {
            initSafeDataTable("#usersTable", {
                order: [[4, "desc"]],
                columnDefs: [
                    { orderable: false, targets: [-1] }
                ]
            });
        }
        
        // جدول المدربين
        if ($("#instructorsTable").length) {
            initSafeDataTable("#instructorsTable", {
                order: [[7, "desc"]],
                columnDefs: [
                    { orderable: false, targets: [8] }
                ]
            });
        }
        
        // جدول الطلاب
        if ($("#studentsTable").length) {
            initSafeDataTable("#studentsTable", {
                order: [[4, "desc"]],
                columnDefs: [
                    { orderable: false, targets: [5] }
                ]
            });
        }
        
        // جدول الكورسات
        if ($("#coursesTable").length) {
            initSafeDataTable("#coursesTable", {
                order: [[5, "desc"]],
                columnDefs: [
                    { orderable: false, targets: [-1] }
                ]
            });
        }
        
        // جدول طلبات الانضمام
        if ($("#requestsTable").length) {
            initSafeDataTable("#requestsTable", {
                order: [[6, "desc"]],
                columnDefs: [
                    { orderable: false, targets: [7] }
                ]
            });
        }
        
        // جداول أخرى
        $(".data-table, .datatable").each(function() {
            const tableId = "#" + $(this).attr("id");
            if ($(this).attr("id") && !$.fn.DataTable.isDataTable(tableId)) {
                initSafeDataTable(tableId);
            }
        });
        
    }, 300);
});

// تنظيف عند مغادرة الصفحة
$(window).on("beforeunload", function() {
    $(".dataTable").each(function() {
        if ($.fn.DataTable.isDataTable(this)) {
            try {
                $(this).DataTable().destroy();
            } catch (e) {
                console.warn("Error destroying DataTable:", e);
            }
        }
    });
});

console.log("DataTables Fix Script Loaded Successfully");
';
                
                // كتابة الملف
                $fix_file = 'assets/js/datatables-fix-updated.js';
                if (!is_dir('assets/js')) {
                    mkdir('assets/js', 0755, true);
                }
                
                if (file_put_contents($fix_file, $datatables_fix_content)) {
                    logMessage("✅ تم إنشاء ملف إصلاح DataTables المحدث: $fix_file", 'success');
                } else {
                    logMessage("❌ فشل في إنشاء ملف إصلاح DataTables", 'error');
                }
                
                // 3. فحص قاعدة البيانات
                echo "<h6>🗄️ فحص قاعدة البيانات</h6>";
                
                try {
                    // فحص جدول course_enrollments
                    if (tableExists('course_enrollments')) {
                        $required_columns = ['payment_amount', 'payment_status', 'enrollment_type'];
                        foreach ($required_columns as $column) {
                            if (columnExists('course_enrollments', $column)) {
                                logMessage("✅ عمود $column موجود في جدول course_enrollments", 'success');
                            } else {
                                logMessage("❌ عمود $column مفقود في جدول course_enrollments", 'error');
                            }
                        }

                        // إحصائيات سريعة
                        $enrollments_count = countRecords('course_enrollments');
                        logMessage("📊 عدد التسجيلات: " . number_format($enrollments_count), 'info');

                        $paid_count = countRecords('course_enrollments', 'payment_amount > 0');
                        logMessage("💰 عدد التسجيلات المدفوعة: " . number_format($paid_count), 'info');
                    } else {
                        logMessage("❌ جدول course_enrollments غير موجود", 'error');
                    }
                    
                } catch (PDOException $e) {
                    logMessage("❌ خطأ في قاعدة البيانات: " . $e->getMessage(), 'error');
                }
                
                // 4. اختبار DataTables
                echo "<h6>🧪 اختبار DataTables</h6>";
                ?>
                
                <div class="mt-3">
                    <h6>جدول اختبار DataTables:</h6>
                    <table id="testTable" class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>اختبار 1</td>
                                <td><EMAIL></td>
                                <td>2025-01-01</td>
                                <td><span class="badge bg-success">نشط</span></td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>اختبار 2</td>
                                <td><EMAIL></td>
                                <td>2025-01-02</td>
                                <td><span class="badge bg-warning">معلق</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <?php
                logMessage("🎉 تم إكمال فحص وإصلاح مشاكل DataTables!", 'success');
                ?>
                
                <div class="mt-4">
                    <a href="financial-reports.php" class="btn btn-primary">
                        <i class="fas fa-chart-line me-2"></i>اختبار التقارير المالية
                    </a>
                    <a href="manage-users.php" class="btn btn-secondary">
                        <i class="fas fa-users me-2"></i>اختبار إدارة المستخدمين
                    </a>
                    <a href="manage-instructors.php" class="btn btn-warning">
                        <i class="fas fa-chalkboard-teacher me-2"></i>اختبار إدارة المدربين
                    </a>
                    <a href="dashboard.php" class="btn btn-success">
                        <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript للاختبار -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="assets/js/datatables-fix-updated.js"></script>
    
    <script>
    $(document).ready(function() {
        // اختبار تهيئة جدول الاختبار
        setTimeout(function() {
            if ($('#testTable').length) {
                try {
                    initSafeDataTable('#testTable', {
                        pageLength: 5,
                        searching: true,
                        ordering: true
                    });
                    console.log('✅ تم تهيئة جدول الاختبار بنجاح');
                    
                    // إضافة رسالة نجاح
                    $('<div class="alert alert-success mt-3"><i class="fas fa-check me-2"></i>تم تهيئة جدول الاختبار بنجاح! DataTables يعمل بشكل صحيح.</div>')
                        .insertAfter('#testTable');
                        
                } catch (error) {
                    console.error('❌ خطأ في تهيئة جدول الاختبار:', error);
                    
                    // إضافة رسالة خطأ
                    $('<div class="alert alert-danger mt-3"><i class="fas fa-times me-2"></i>فشل في تهيئة جدول الاختبار: ' + error.message + '</div>')
                        .insertAfter('#testTable');
                }
            }
        }, 500);
    });
    </script>
</body>
</html>
