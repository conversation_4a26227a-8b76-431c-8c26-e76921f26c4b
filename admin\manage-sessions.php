<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$course_id = (int)($_GET['course_id'] ?? 0);
$success_message = '';
$error_message = '';

// إذا لم يتم تحديد كورس، عرض قائمة الكورسات
if (!$course_id) {
    $courses = fetchAll("
        SELECT c.*, u.name as instructor_name,
               (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as sessions_count,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id) as enrolled_students
        FROM courses c
        LEFT JOIN users u ON c.instructor_id = u.id
        WHERE c.status = 'active'
        ORDER BY c.created_at DESC
    ");

    $pageTitle = 'إدارة الجلسات';
    include 'includes/header.php';
    ?>

    <!-- عنوان الصفحة -->
    <div class="card-admin mb-4">
        <div class="card-body text-center">
            <div class="mb-3">
                <i class="fas fa-video fa-4x text-primary"></i>
            </div>
            <h3 class="mb-2">إدارة الجلسات</h3>
            <p class="text-muted">اختر كورس من القائمة أدناه لإدارة جلساته وإضافة جلسات جديدة</p>
        </div>
    </div>

    <!-- قائمة الكورسات -->
    <div class="card-admin">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-book me-2"></i>
                الكورسات المتاحة (<?php echo count($courses); ?>)
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($courses)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-book fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد كورسات</h5>
                    <p class="text-muted">لا توجد كورسات نشطة في النظام</p>
                    <a href="manage-courses-new.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة كورس جديد
                    </a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($courses as $course): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex align-items-start mb-3">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-book"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($course['title']); ?></h6>
                                            <small class="text-muted">المدرب: <?php echo htmlspecialchars($course['instructor_name'] ?? 'غير محدد'); ?></small>
                                        </div>
                                    </div>

                                    <div class="row text-center mb-3">
                                        <div class="col-4">
                                            <div class="text-primary fw-bold"><?php echo $course['sessions_count']; ?></div>
                                            <small class="text-muted">جلسة</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-success fw-bold"><?php echo $course['enrolled_students']; ?></div>
                                            <small class="text-muted">طالب</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-info fw-bold"><?php echo number_format($course['price'] ?? 0, 0); ?></div>
                                            <small class="text-muted">ر.س</small>
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <a href="manage-sessions.php?course_id=<?php echo $course['id']; ?>"
                                           class="btn btn-primary">
                                            <i class="fas fa-video me-2"></i>إدارة الجلسات
                                        </a>
                                        <div class="btn-group" role="group">
                                            <a href="course-details.php?id=<?php echo $course['id']; ?>"
                                               class="btn btn-outline-info btn-sm">
                                                <i class="fas fa-eye me-1"></i>التفاصيل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>
    <?php exit; ?>

<?php
}

// معالجة إضافة جلسة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_session'])) {
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $session_date = $_POST['session_date'] ?? '';
    $start_time = $_POST['start_time'] ?? '';
    $end_time = $_POST['end_time'] ?? '';
    $session_type = $_POST['session_type'] ?? 'online';
    $meeting_link = trim($_POST['meeting_link'] ?? '');

    if (empty($title) || empty($session_date) || empty($start_time) || empty($end_time)) {
        $error_message = 'جميع الحقول المطلوبة يجب ملؤها';
    } else {
        $session_data = [
            'course_id' => $course_id,
            'title' => $title,
            'description' => $description,
            'session_date' => $session_date,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'session_type' => $session_type,
            'meeting_link' => $meeting_link,
            'status' => 'scheduled',
            'created_at' => date('Y-m-d H:i:s')
        ];

        if (insertRecord('sessions', $session_data)) {
            $success_message = 'تم إضافة الجلسة بنجاح';
        } else {
            $error_message = 'حدث خطأ أثناء إضافة الجلسة';
        }
    }
}

// جلب معلومات الكورس
$course = fetchOne("
    SELECT c.*, u.name as instructor_name,
    (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id) as enrolled_students
    FROM courses c
    LEFT JOIN users u ON c.instructor_id = u.id
    WHERE c.id = ?
", [$course_id]);

if (!$course) {
    header('Location: manage-courses-new.php');
    exit;
}

// جلب إحصائيات الجلسات
$stats = fetchOne("
    SELECT
        COUNT(*) as total_sessions,
        SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled_sessions,
        SUM(CASE WHEN status = 'live' THEN 1 ELSE 0 END) as live_sessions,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_sessions,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_sessions
    FROM sessions WHERE course_id = ?
", [$course_id]);

// جلب قائمة الجلسات
$sessions = fetchAll("
    SELECT s.*,
    (SELECT COUNT(*) FROM session_attendance WHERE session_id = s.id AND status = 'present') as attendance_count
    FROM sessions s
    WHERE s.course_id = ?
    ORDER BY s.session_date ASC, s.start_time ASC
", [$course_id]);

$pageTitle = 'إدارة جلسات: ' . $course['title'];
include 'includes/header.php';
?>

<!-- رسائل النجاح والخطأ -->
<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- معلومات الكورس -->
<div class="card-admin mb-4">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-book me-2"></i>
                    <?php echo htmlspecialchars($course['title']); ?>
                </h5>
                <small class="text-muted">المدرب: <?php echo htmlspecialchars($course['instructor_name']); ?> | الطلاب: <?php echo $course['enrolled_students']; ?></small>
            </div>
            <div>
                <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-outline-info btn-sm">
                    <i class="fas fa-eye me-2"></i>تفاصيل الكورس
                </a>
                <a href="manage-courses-new.php" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-arrow-right me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>
</div>

    <!-- إحصائيات الجلسات -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="admin-card text-center">
                <div class="card-body">
                    <div class="stat-icon bg-primary text-white mb-2">
                        <i class="fas fa-video"></i>
                    </div>
                    <h4 class="mb-0"><?php echo $stats['total_sessions']; ?></h4>
                    <small class="text-muted">إجمالي الجلسات</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="admin-card text-center">
                <div class="card-body">
                    <div class="stat-icon bg-info text-white mb-2">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4 class="mb-0"><?php echo $stats['scheduled_sessions']; ?></h4>
                    <small class="text-muted">مجدولة</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="admin-card text-center">
                <div class="card-body">
                    <div class="stat-icon bg-danger text-white mb-2">
                        <i class="fas fa-broadcast-tower"></i>
                    </div>
                    <h4 class="mb-0"><?php echo $stats['live_sessions']; ?></h4>
                    <small class="text-muted">مباشرة</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="admin-card text-center">
                <div class="card-body">
                    <div class="stat-icon bg-success text-white mb-2">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h4 class="mb-0"><?php echo $stats['completed_sessions']; ?></h4>
                    <small class="text-muted">مكتملة</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="admin-card text-center">
                <div class="card-body">
                    <div class="stat-icon bg-secondary text-white mb-2">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <h4 class="mb-0"><?php echo $stats['cancelled_sessions']; ?></h4>
                    <small class="text-muted">ملغية</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="admin-card text-center">
                <div class="card-body">
                    <div class="stat-icon bg-warning text-white mb-2">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <h4 class="mb-0"><?php echo $stats['total_sessions'] > 0 ? round(($stats['completed_sessions'] / $stats['total_sessions']) * 100) : 0; ?>%</h4>
                    <small class="text-muted">نسبة الإكمال</small>
                </div>
            </div>
        </div>
    </div>

<div class="row">
    <div class="col-lg-4">
        <!-- نموذج إضافة جلسة جديدة -->
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة جلسة جديدة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="addSessionForm">
                    <input type="hidden" name="add_session" value="1">

                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان الجلسة <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الجلسة</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="session_date" class="form-label">تاريخ الجلسة <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="session_date" name="session_date" required>
                    </div>

                    <div class="row">
                        <div class="col-6 mb-3">
                            <label for="start_time" class="form-label">وقت البداية <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="start_time" name="start_time" required>
                        </div>
                        <div class="col-6 mb-3">
                            <label for="end_time" class="form-label">وقت النهاية <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" id="end_time" name="end_time" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="session_type" class="form-label">نوع الجلسة</label>
                        <select class="form-select" id="session_type" name="session_type">
                            <option value="online">أونلاين</option>
                            <option value="offline">حضوري</option>
                            <option value="hybrid">مختلط</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="meeting_link" class="form-label">رابط الاجتماع</label>
                        <input type="url" class="form-control" id="meeting_link" name="meeting_link"
                               placeholder="https://zoom.us/j/...">
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-plus me-2"></i>إضافة الجلسة
                    </button>
                </form>
            </div>
        </div>
    </div>
        
        <div class="col-lg-8">
            <!-- قائمة الجلسات -->
            <div class="admin-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الجلسات</h6>
                        <small class="text-muted">إدارة وتتبع جميع جلسات الكورس</small>
                    </div>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" id="statusFilter" style="width: auto;">
                            <option value="">جميع الحالات</option>
                            <option value="scheduled">مجدولة</option>
                            <option value="live">مباشرة</option>
                            <option value="completed">مكتملة</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                    </div>
                </div>
                
                <div class="card-body">
                    <?php if (empty($sessions)): ?>
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-video fa-4x text-muted"></i>
                            </div>
                            <h5 class="text-muted">لا توجد جلسات في هذا الكورس</h5>
                            <p class="text-muted">ابدأ بإضافة جلسة جديدة للكورس</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="sessionsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>الجلسة</th>
                                        <th>التاريخ والوقت</th>
                                        <th>النوع</th>
                                        <th>الحضور</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sessions as $session): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-0"><?php echo htmlspecialchars($session['title']); ?></h6>
                                                    <?php if ($session['description']): ?>
                                                        <small class="text-muted"><?php echo htmlspecialchars(substr($session['description'], 0, 50)) . (strlen($session['description']) > 50 ? '...' : ''); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo date('Y-m-d', strtotime($session['session_date'])); ?></strong>
                                                    <br><small class="text-muted">
                                                        <?php echo date('H:i', strtotime($session['start_time'])); ?> -
                                                        <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $session['session_type']; ?></span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2"><?php echo $session['attendance_count']; ?>/<?php echo $course['enrolled_students']; ?></span>
                                                    <?php 
                                                    $attendance_percentage = $course['enrolled_students'] > 0 ? 
                                                        ($session['attendance_count'] / $course['enrolled_students']) * 100 : 0;
                                                    ?>
                                                    <div class="progress" style="width: 60px; height: 8px;">
                                                        <div class="progress-bar bg-<?php echo $attendance_percentage >= 80 ? 'success' : ($attendance_percentage >= 60 ? 'warning' : 'danger'); ?>" 
                                                             style="width: <?php echo $attendance_percentage; ?>%"></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $status_colors = [
                                                    'scheduled' => 'primary',
                                                    'live' => 'danger',
                                                    'completed' => 'success',
                                                    'cancelled' => 'secondary'
                                                ];
                                                $status_text = [
                                                    'scheduled' => 'مجدولة',
                                                    'live' => 'مباشرة',
                                                    'completed' => 'مكتملة',
                                                    'cancelled' => 'ملغية'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_colors[$session['status']] ?? 'secondary'; ?>">
                                                    <?php echo $status_text[$session['status']] ?? $session['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-info" onclick="viewSessionDetails(<?php echo $session['id']; ?>)" 
                                                            data-bs-toggle="tooltip" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    
                                                    <?php if ($session['meeting_link']): ?>
                                                        <a href="<?php echo htmlspecialchars($session['meeting_link']); ?>" target="_blank"
                                                           class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="الانضمام للاجتماع">
                                                            <i class="fas fa-external-link-alt"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($session['status'] === 'scheduled'): ?>
                                                        <button class="btn btn-sm btn-outline-success" onclick="updateSessionStatus(<?php echo $session['id']; ?>, 'live')"
                                                                data-bs-toggle="tooltip" title="بدء الجلسة">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    <?php elseif ($session['status'] === 'live'): ?>
                                                        <button class="btn btn-sm btn-outline-warning" onclick="updateSessionStatus(<?php echo $session['id']; ?>, 'completed')"
                                                                data-bs-toggle="tooltip" title="إنهاء الجلسة">
                                                            <i class="fas fa-stop"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteSession(<?php echo $session['id']; ?>, '<?php echo htmlspecialchars($session['title']); ?>')"
                                                            data-bs-toggle="tooltip" title="حذف الجلسة">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 20px;
}

.table th {
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 6px !important;
    margin: 0 1px;
}

.progress {
    background-color: #e9ecef;
}
</style>

<script>
$(document).ready(function() {
    // تهيئة DataTables مع فحص عدم إعادة التهيئة
    if (!$.fn.DataTable.isDataTable('#sessionsTable')) {
        const table = $('#sessionsTable').DataTable({
            order: [[1, 'asc']],
            pageLength: 10,
            responsive: true,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
            },
            columnDefs: [
                { orderable: false, targets: [5] }
            ]
        });
    }

    // فلتر الحالة
    $('#statusFilter').on('change', function() {
        const status = this.value;
        if (status) {
            table.column(4).search(status).draw();
        } else {
            table.column(4).search('').draw();
        }
    });
});

// عرض تفاصيل الجلسة
function viewSessionDetails(sessionId) {
    showLoading('جاري جلب تفاصيل الجلسة...');
    
    $.get('ajax/get-session-details.php', { session_id: sessionId })
    .done(function(response) {
        hideLoading();
        const data = JSON.parse(response);
        if (data.success) {
            const session = data.session;
            Swal.fire({
                title: session.title,
                html: `
                    <div class="text-start">
                        <p><strong>الوصف:</strong> ${session.description || 'لا يوجد وصف'}</p>
                        <p><strong>التاريخ:</strong> ${session.session_date}</p>
                        <p><strong>المدة:</strong> ${session.duration} دقيقة</p>
                        <p><strong>الحالة:</strong> <span class="badge bg-primary">${session.status}</span></p>
                        ${session.meeting_link ? `<p><strong>رابط الاجتماع:</strong> <a href="${session.meeting_link}" target="_blank">انضم للاجتماع</a></p>` : ''}
                        <p><strong>عدد الحضور:</strong> ${session.attendance_count} طالب</p>
                    </div>
                `,
                showConfirmButton: false,
                showCloseButton: true,
                width: 500
            });
        } else {
            Swal.fire('خطأ', data.message || 'حدث خطأ في جلب تفاصيل الجلسة', 'error');
        }
    })
    .fail(function() {
        hideLoading();
        Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
    });
}

// تحديث حالة الجلسة
function updateSessionStatus(sessionId, newStatus) {
    const statusText = {
        'live': 'بدء',
        'completed': 'إنهاء',
        'cancelled': 'إلغاء'
    };
    
    Swal.fire({
        title: `${statusText[newStatus]} الجلسة`,
        text: `هل أنت متأكد من ${statusText[newStatus]} هذه الجلسة؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: `نعم، ${statusText[newStatus]}`,
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            showLoading(`جاري ${statusText[newStatus]} الجلسة...`);
            
            $.post('ajax/update-session-status.php', {
                session_id: sessionId,
                status: newStatus
            })
            .done(function(response) {
                hideLoading();
                const data = JSON.parse(response);
                if (data.success) {
                    showToast(`تم ${statusText[newStatus]} الجلسة بنجاح`, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    Swal.fire('خطأ', data.message || `حدث خطأ أثناء ${statusText[newStatus]} الجلسة`, 'error');
                }
            })
            .fail(function() {
                hideLoading();
                Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

// حذف الجلسة
function deleteSession(sessionId, sessionTitle) {
    Swal.fire({
        title: 'حذف الجلسة',
        html: `
            <p>هل أنت متأكد من حذف الجلسة <strong>${sessionTitle}</strong>؟</p>
            <div class="alert alert-warning text-start">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> سيتم حذف جميع سجلات الحضور المرتبطة بهذه الجلسة
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف الجلسة',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            showLoading('جاري حذف الجلسة...');
            
            $.post('ajax/delete-session.php', {
                session_id: sessionId
            })
            .done(function(response) {
                hideLoading();
                const data = JSON.parse(response);
                if (data.success) {
                    Swal.fire({
                        title: 'تم الحذف!',
                        text: 'تم حذف الجلسة بنجاح',
                        icon: 'success'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ', data.message || 'حدث خطأ أثناء حذف الجلسة', 'error');
                }
            })
            .fail(function() {
                hideLoading();
                Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

// التحقق من صحة النموذج
document.getElementById('addSessionForm').addEventListener('submit', function(e) {
    const sessionDate = new Date(document.getElementById('session_date').value);
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    const now = new Date();

    if (sessionDate <= now) {
        e.preventDefault();
        alert('تاريخ الجلسة يجب أن يكون في المستقبل');
        return false;
    }

    if (startTime >= endTime) {
        e.preventDefault();
        alert('وقت النهاية يجب أن يكون بعد وقت البداية');
        return false;
    }
});

// حذف الجلسة
function deleteSession(sessionId, sessionTitle) {
    if (confirm('هل أنت متأكد من حذف الجلسة: ' + sessionTitle + '؟\n\nسيتم حذف جميع سجلات الحضور المرتبطة بهذه الجلسة.')) {
        window.location.href = 'delete-session.php?id=' + sessionId + '&course_id=<?php echo $course_id; ?>';
    }
}
</script>

<?php include 'includes/footer.php'; ?>
