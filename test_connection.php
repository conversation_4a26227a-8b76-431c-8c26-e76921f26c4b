<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Database Connection Test
 * =======================
 */

echo "<h2>🔍 اختبار اتصال قاعدة البيانات</h2>";

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_name = 'zoom_learning_system';
$db_user = 'root';
$db_pass = '';

echo "<div style='font-family: Arial; padding: 20px; background: #f8f9fa; border-radius: 10px; margin: 20px;'>";

// 1. فحص امتداد PDO
echo "<h3>1️⃣ فحص امتداد PDO:</h3>";
if (extension_loaded('pdo_mysql')) {
    echo "✅ <span style='color: green;'>PDO MySQL متوفر</span><br>";
} else {
    echo "❌ <span style='color: red;'>PDO MySQL غير متوفر</span><br>";
    exit;
}

// 2. فحص خدمة MySQL
echo "<h3>2️⃣ فحص خدمة MySQL:</h3>";
$connection = @fsockopen($db_host, 3306, $errno, $errstr, 5);
if ($connection) {
    echo "✅ <span style='color: green;'>خدمة MySQL تعمل على المنفذ 3306</span><br>";
    fclose($connection);
} else {
    echo "❌ <span style='color: red;'>خدمة MySQL لا تعمل - تأكد من تشغيل XAMPP</span><br>";
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>🔧 الحل:</strong><br>";
    echo "1. افتح XAMPP Control Panel<br>";
    echo "2. اضغط على 'Start' بجانب MySQL<br>";
    echo "3. تأكد من ظهور 'Running' باللون الأخضر<br>";
    echo "</div>";
    exit;
}

// 3. محاولة الاتصال بقاعدة البيانات
echo "<h3>3️⃣ اختبار الاتصال بقاعدة البيانات:</h3>";
try {
    $dsn = "mysql:host=$db_host;charset=utf8mb4";
    $pdo = new PDO($dsn, $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ <span style='color: green;'>تم الاتصال بخادم MySQL بنجاح</span><br>";
    
    // 4. فحص وجود قاعدة البيانات
    echo "<h3>4️⃣ فحص قاعدة البيانات:</h3>";
    $stmt = $pdo->query("SHOW DATABASES LIKE '$db_name'");
    if ($stmt->rowCount() > 0) {
        echo "✅ <span style='color: green;'>قاعدة البيانات '$db_name' موجودة</span><br>";
        
        // الاتصال بقاعدة البيانات
        $pdo->exec("USE $db_name");
        
        // 5. فحص الجداول
        echo "<h3>5️⃣ فحص الجداول:</h3>";
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (count($tables) > 0) {
            echo "✅ <span style='color: green;'>تم العثور على " . count($tables) . " جدول</span><br>";
            echo "<details style='margin: 10px 0;'>";
            echo "<summary>عرض الجداول</summary>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>$table</li>";
            }
            echo "</ul>";
            echo "</details>";
        } else {
            echo "⚠️ <span style='color: orange;'>قاعدة البيانات فارغة - تحتاج لتشغيل ملف SQL</span><br>";
            echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>🔧 الحل:</strong><br>";
            echo "1. افتح phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a><br>";
            echo "2. اختر قاعدة البيانات '$db_name'<br>";
            echo "3. اذهب لتبويب 'Import'<br>";
            echo "4. ارفع ملف 'database/schema_complete.sql'<br>";
            echo "</div>";
        }
        
    } else {
        echo "⚠️ <span style='color: orange;'>قاعدة البيانات '$db_name' غير موجودة</span><br>";
        
        // محاولة إنشاء قاعدة البيانات
        echo "<h3>6️⃣ إنشاء قاعدة البيانات:</h3>";
        try {
            $pdo->exec("CREATE DATABASE $db_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "✅ <span style='color: green;'>تم إنشاء قاعدة البيانات '$db_name' بنجاح</span><br>";
            
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>📝 الخطوة التالية:</strong><br>";
            echo "1. افتح phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a><br>";
            echo "2. اختر قاعدة البيانات '$db_name'<br>";
            echo "3. اذهب لتبويب 'Import'<br>";
            echo "4. ارفع ملف 'database/schema_complete.sql'<br>";
            echo "5. اضغط 'Go' لتنفيذ الملف<br>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "❌ <span style='color: red;'>فشل في إنشاء قاعدة البيانات: " . $e->getMessage() . "</span><br>";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ <span style='color: red;'>فشل الاتصال: " . $e->getMessage() . "</span><br>";
    
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>🔐 مشكلة في بيانات الدخول:</strong><br>";
        echo "تحقق من اسم المستخدم وكلمة المرور في إعدادات MySQL<br>";
        echo "</div>";
    }
}

echo "</div>";

// معلومات إضافية
echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 10px; margin: 20px;'>";
echo "<h3>📋 معلومات النظام:</h3>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "<br>";
echo "</div>";

// روابط مفيدة
echo "<div style='background: #cff4fc; padding: 15px; border-radius: 10px; margin: 20px;'>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<a href='http://localhost/phpmyadmin' target='_blank' style='display: inline-block; padding: 8px 15px; background: #0d6efd; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>phpMyAdmin</a>";
echo "<a href='http://localhost/dashboard' target='_blank' style='display: inline-block; padding: 8px 15px; background: #198754; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>XAMPP Dashboard</a>";
echo "<a href='visitor_homepage.php' style='display: inline-block; padding: 8px 15px; background: #6f42c1; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

h2 {
    color: white;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 30px;
}

h3 {
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 5px;
}

details {
    cursor: pointer;
}

summary {
    font-weight: bold;
    padding: 5px;
    background: #f8f9fa;
    border-radius: 3px;
}

a {
    color: inherit;
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}
</style>
