<?php
/**
 * ملف التكوين الرئيسي المحسن
 * Enhanced Main Configuration File
 * ===============================
 */

// منع الوصول المباشر
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

/**
 * إعدادات البيئة
 */
define('ENVIRONMENT', $_ENV['APP_ENV'] ?? 'development'); // development, staging, production
define('DEBUG_MODE', ENVIRONMENT === 'development');
define('MAINTENANCE_MODE', false);

/**
 * إعدادات قاعدة البيانات المحسنة - مع فحص التعريف المسبق
 */
if (!defined('DB_HOST')) define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
if (!defined('DB_NAME')) define('DB_NAME', $_ENV['DB_NAME'] ?? 'zoom_learning_system');
if (!defined('DB_USER')) define('DB_USER', $_ENV['DB_USER'] ?? 'root');
if (!defined('DB_PASS')) define('DB_PASS', $_ENV['DB_PASS'] ?? '');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATION', 'utf8mb4_unicode_ci');
define('DB_PREFIX', 'zls_');

// إعدادات اتصال قاعدة البيانات
define('DB_CONNECTION_TIMEOUT', 30);
define('DB_QUERY_TIMEOUT', 60);
define('DB_MAX_CONNECTIONS', 100);

/**
 * إعدادات الموقع المحسنة
 */
define('SITE_NAME', $_ENV['SITE_NAME'] ?? 'منصة التعلم الإلكتروني');
define('SITE_TAGLINE', 'تعلم مهارات المستقبل مع أفضل المدربين');
define('SITE_DESCRIPTION', 'منصة تعليمية متقدمة تقدم كورسات عالية الجودة في مختلف المجالات');
define('SITE_KEYWORDS', 'تعلم إلكتروني, كورسات أونلاين, تدريب, شهادات معتمدة');
define('SITE_AUTHOR', 'فريق منصة التعلم الإلكتروني');
define('SITE_URL', $_ENV['SITE_URL'] ?? 'http://localhost/Zoom');
define('SITE_EMAIL', $_ENV['SITE_EMAIL'] ?? '<EMAIL>');
define('SITE_PHONE', $_ENV['SITE_PHONE'] ?? '+966-50-000-0000');
define('SITE_ADDRESS', 'المملكة العربية السعودية');
define('SITE_LOGO', '/assets/images/logo.png');
define('SITE_FAVICON', '/assets/images/favicon.ico');
define('SITE_VERSION', '2.0.0');

// إعدادات اللغة والمنطقة
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DEFAULT_CURRENCY', 'USD');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);
define('RTL_LANGUAGES', ['ar']);

/**
 * إعدادات Zoom المحسنة
 */
define('ZOOM_API_KEY', $_ENV['ZOOM_API_KEY'] ?? 'YOUR_ZOOM_API_KEY');
define('ZOOM_API_SECRET', $_ENV['ZOOM_API_SECRET'] ?? 'YOUR_ZOOM_API_SECRET');
define('ZOOM_BASE_URL', 'https://api.zoom.us/v2');
define('ZOOM_JWT_EXPIRE', 3600); // ساعة واحدة
define('ZOOM_WEBHOOK_SECRET', $_ENV['ZOOM_WEBHOOK_SECRET'] ?? 'your-webhook-secret');

/**
 * إعدادات الدفع المحسنة
 */
// Stripe
define('STRIPE_PUBLISHABLE_KEY', $_ENV['STRIPE_PUBLISHABLE_KEY'] ?? 'pk_test_your_stripe_publishable_key');
define('STRIPE_SECRET_KEY', $_ENV['STRIPE_SECRET_KEY'] ?? 'sk_test_your_stripe_secret_key');
define('STRIPE_WEBHOOK_SECRET', $_ENV['STRIPE_WEBHOOK_SECRET'] ?? 'whsec_your_webhook_secret');

// PayPal
define('PAYPAL_CLIENT_ID', $_ENV['PAYPAL_CLIENT_ID'] ?? 'your_paypal_client_id');
define('PAYPAL_CLIENT_SECRET', $_ENV['PAYPAL_CLIENT_SECRET'] ?? 'your_paypal_client_secret');
define('PAYPAL_MODE', ENVIRONMENT === 'production' ? 'live' : 'sandbox');

// إعدادات العمولة
define('PLATFORM_COMMISSION_RATE', 30); // 30%
define('MIN_COMMISSION_RATE', 15);
define('MAX_COMMISSION_RATE', 50);

/**
 * إعدادات البريد الإلكتروني المحسنة
 */
define('MAIL_DRIVER', $_ENV['MAIL_DRIVER'] ?? 'smtp'); // smtp, sendmail, mail
define('SMTP_HOST', $_ENV['SMTP_HOST'] ?? 'smtp.gmail.com');
define('SMTP_PORT', $_ENV['SMTP_PORT'] ?? 587);
define('SMTP_ENCRYPTION', $_ENV['SMTP_ENCRYPTION'] ?? 'tls'); // tls, ssl
define('SMTP_USERNAME', $_ENV['SMTP_USERNAME'] ?? '<EMAIL>');
define('SMTP_PASSWORD', $_ENV['SMTP_PASSWORD'] ?? 'your-app-password');
define('MAIL_FROM_ADDRESS', $_ENV['MAIL_FROM_ADDRESS'] ?? SITE_EMAIL);
define('MAIL_FROM_NAME', $_ENV['MAIL_FROM_NAME'] ?? SITE_NAME);

/**
 * إعدادات الأمان المحسنة
 */
define('ENCRYPTION_KEY', $_ENV['ENCRYPTION_KEY'] ?? hash('sha256', 'zoom-learning-encryption-key-2024'));
define('JWT_SECRET', $_ENV['JWT_SECRET'] ?? hash('sha256', 'zoom-learning-jwt-secret-2024'));
define('JWT_EXPIRE', 3600); // ساعة واحدة
define('CSRF_TOKEN_EXPIRE', 1800); // 30 دقيقة
define('SESSION_LIFETIME', 7200); // ساعتان
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_DURATION', 900); // 15 دقيقة

// إعدادات كلمات المرور
define('PASSWORD_REQUIRE_UPPERCASE', true);
define('PASSWORD_REQUIRE_LOWERCASE', true);
define('PASSWORD_REQUIRE_NUMBERS', true);
define('PASSWORD_REQUIRE_SYMBOLS', false);

/**
 * إعدادات رفع الملفات المحسنة
 */
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_VIDEO_TYPES', ['mp4', 'avi', 'mov', 'wmv', 'flv']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx']);
define('ALLOWED_AUDIO_TYPES', ['mp3', 'wav', 'ogg']);

// مسارات الرفع
define('UPLOAD_COURSES_PATH', UPLOAD_PATH . 'courses/');
define('UPLOAD_PROFILES_PATH', UPLOAD_PATH . 'profiles/');
define('UPLOAD_VIDEOS_PATH', UPLOAD_PATH . 'videos/');
define('UPLOAD_DOCUMENTS_PATH', UPLOAD_PATH . 'documents/');

/**
 * إعدادات PWA
 */
define('PWA_NAME', SITE_NAME);
define('PWA_SHORT_NAME', 'التعلم');
define('PWA_DESCRIPTION', SITE_DESCRIPTION);
define('PWA_THEME_COLOR', '#667eea');
define('PWA_BACKGROUND_COLOR', '#ffffff');
define('PWA_DISPLAY', 'standalone');
define('PWA_ORIENTATION', 'portrait-primary');

/**
 * إعدادات API
 */
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 1000); // طلب في الساعة
define('API_RATE_LIMIT_WINDOW', 3600); // ساعة واحدة
define('API_TIMEOUT', 30); // ثانية

/**
 * إعدادات التحليلات
 */
define('GOOGLE_ANALYTICS_ID', $_ENV['GOOGLE_ANALYTICS_ID'] ?? '');
define('FACEBOOK_PIXEL_ID', $_ENV['FACEBOOK_PIXEL_ID'] ?? '');
define('ENABLE_ANALYTICS', !empty(GOOGLE_ANALYTICS_ID));

/**
 * إعدادات وسائل التواصل الاجتماعي
 */
define('SOCIAL_FACEBOOK', $_ENV['SOCIAL_FACEBOOK'] ?? '');
define('SOCIAL_TWITTER', $_ENV['SOCIAL_TWITTER'] ?? '');
define('SOCIAL_LINKEDIN', $_ENV['SOCIAL_LINKEDIN'] ?? '');
define('SOCIAL_YOUTUBE', $_ENV['SOCIAL_YOUTUBE'] ?? '');
define('SOCIAL_INSTAGRAM', $_ENV['SOCIAL_INSTAGRAM'] ?? '');

/**
 * إعدادات الإشعارات
 */
define('NOTIFICATIONS_ENABLED', true);
define('EMAIL_NOTIFICATIONS_ENABLED', true);
define('PUSH_NOTIFICATIONS_ENABLED', true);
define('SMS_NOTIFICATIONS_ENABLED', false);

// إعدادات Firebase للإشعارات
define('FIREBASE_SERVER_KEY', $_ENV['FIREBASE_SERVER_KEY'] ?? '');
define('FIREBASE_SENDER_ID', $_ENV['FIREBASE_SENDER_ID'] ?? '');

/**
 * إعدادات المحتوى
 */
define('POSTS_PER_PAGE', 12);
define('COURSES_PER_PAGE', 12);
define('COMMENTS_PER_PAGE', 20);
define('SEARCH_RESULTS_PER_PAGE', 20);

/**
 * إعدادات الأداء
 */
define('ENABLE_COMPRESSION', true);
define('ENABLE_CACHING', true);
define('ENABLE_MINIFICATION', ENVIRONMENT === 'production');
define('ENABLE_CDN', false);
define('CDN_URL', $_ENV['CDN_URL'] ?? '');

/**
 * إعدادات السجلات
 */
define('LOG_LEVEL', DEBUG_MODE ? 'debug' : 'error');
define('LOG_PATH', 'logs/');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('LOG_MAX_FILES', 30);

/**
 * إعدادات التطوير
 */
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', LOG_PATH . 'php_errors.log');
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', LOG_PATH . 'php_errors.log');
}

// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// تعيين الترميز
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

/**
 * دوال مساعدة للتكوين
 */

/**
 * الحصول على قيمة تكوين
 */
function getConfig($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

/**
 * التحقق من تفعيل ميزة
 */
function isFeatureEnabled($feature) {
    return getConfig($feature, false) === true;
}

/**
 * الحصول على URL كامل
 */
function getFullUrl($path = '') {
    return rtrim(SITE_URL, '/') . '/' . ltrim($path, '/');
}

/**
 * الحصول على مسار الرفع
 */
function getUploadPath($type = '') {
    $paths = [
        'courses' => UPLOAD_COURSES_PATH,
        'profiles' => UPLOAD_PROFILES_PATH,
        'videos' => UPLOAD_VIDEOS_PATH,
        'documents' => UPLOAD_DOCUMENTS_PATH
    ];
    
    return $paths[$type] ?? UPLOAD_PATH;
}

/**
 * التحقق من البيئة
 */
function isProduction() {
    return ENVIRONMENT === 'production';
}

function isDevelopment() {
    return ENVIRONMENT === 'development';
}

function isStaging() {
    return ENVIRONMENT === 'staging';
}

/**
 * إعدادات إضافية حسب البيئة
 */
if (isProduction()) {
    // إعدادات الإنتاج
    ini_set('session.cookie_secure', 1);
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_strict_mode', 1);
} elseif (isDevelopment()) {
    // إعدادات التطوير
    ini_set('memory_limit', '512M');
    ini_set('max_execution_time', 300);
}

/**
 * تحميل متغيرات البيئة من ملف .env إذا كان موجوداً
 */
if (file_exists(__DIR__ . '/../.env')) {
    $envFile = file_get_contents(__DIR__ . '/../.env');
    $lines = explode("\n", $envFile);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, '"\'');
            
            if (!array_key_exists($key, $_ENV)) {
                $_ENV[$key] = $value;
                putenv("$key=$value");
            }
        }
    }
}

/**
 * دالة للحصول على إعدادات النظام من قاعدة البيانات
 */
function getSystemSetting($key, $default = null) {
    static $settings = null;
    
    if ($settings === null) {
        $settings = [];
        try {
            global $conn;
            if (isset($conn)) {
                $stmt = $conn->query("SELECT setting_key, setting_value FROM system_settings");
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $settings[$row['setting_key']] = $row['setting_value'];
                }
            }
        } catch (Exception $e) {
            // في حالة عدم وجود الجدول أو خطأ في الاتصال
        }
    }
    
    return $settings[$key] ?? $default;
}

/**
 * دالة لحفظ إعدادات النظام في قاعدة البيانات
 */
function setSystemSetting($key, $value) {
    try {
        global $conn;
        if (isset($conn)) {
            $stmt = $conn->prepare("
                INSERT INTO system_settings (setting_key, setting_value, updated_at) 
                VALUES (?, ?, NOW()) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                updated_at = NOW()
            ");
            return $stmt->execute([$key, $value]);
        }
    } catch (Exception $e) {
        error_log("Error saving system setting: " . $e->getMessage());
        return false;
    }
    return false;
}

// التحقق من الإعدادات المطلوبة
$requiredSettings = [
    'SITE_NAME', 'SITE_URL', 'SITE_EMAIL'
];

foreach ($requiredSettings as $setting) {
    if (!defined($setting) || empty(constant($setting))) {
        if (DEBUG_MODE) {
            die("Required setting '$setting' is not configured.");
        } else {
            error_log("Required setting '$setting' is not configured.");
        }
    }
}
?>
