/* 
 * ملف CSS محسن للتجاوب مع جميع الأجهزة
 * Responsive CSS for All Devices
 * ========================================
 */

/* المتغيرات العامة */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --font-family-ar: 'Cairo', 'Tajawal', sans-serif;
    --font-family-en: 'Roboto', 'Open Sans', sans-serif;
    
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
    
    --container-max-width: 1200px;
    --sidebar-width: 280px;
    --header-height: 70px;
}

/* إعدادات أساسية للتجاوب */
* {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-ar);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f5f7fa;
    overflow-x: hidden;
}

/* الحاويات المتجاوبة */
.container-fluid {
    width: 100%;
    padding: 0 15px;
    margin: 0 auto;
}

.container {
    max-width: var(--container-max-width);
    width: 100%;
    padding: 0 15px;
    margin: 0 auto;
}

/* الشبكة المتجاوبة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

/* أعمدة متجاوبة */
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* الهيدر المتجاوب */
.header-responsive {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: var(--box-shadow);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
}

.logo-responsive {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.2rem;
}

.logo-responsive img {
    height: 40px;
    margin-left: 10px;
}

/* القائمة الجانبية المتجاوبة */
.sidebar-responsive {
    position: fixed;
    top: var(--header-height);
    right: -var(--sidebar-width);
    width: var(--sidebar-width);
    height: calc(100vh - var(--header-height));
    background: white;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow-y: auto;
    z-index: 999;
}

.sidebar-responsive.active {
    right: 0;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* المحتوى الرئيسي */
.main-content-responsive {
    margin-top: var(--header-height);
    padding: 20px;
    min-height: calc(100vh - var(--header-height));
}

/* البطاقات المتجاوبة */
.card-responsive {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    overflow: hidden;
    transition: var(--transition);
}

.card-responsive:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header-responsive {
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.card-body-responsive {
    padding: 20px;
}

.card-footer-responsive {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* الأزرار المتجاوبة */
.btn-responsive {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    min-height: 44px; /* للمس السهل على الجوال */
    gap: 8px;
}

.btn-primary-responsive {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-primary-responsive:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* النماذج المتجاوبة */
.form-group-responsive {
    margin-bottom: 20px;
}

.form-label-responsive {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
}

.form-control-responsive {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    font-size: 16px; /* لمنع التكبير على iOS */
    transition: var(--transition);
    background: white;
}

.form-control-responsive:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* الجداول المتجاوبة */
.table-responsive-custom {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-mobile {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table-mobile th,
.table-mobile td {
    padding: 12px 16px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.table-mobile th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    font-weight: 600;
    color: var(--dark-color);
}

/* الإحصائيات المتجاوبة */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card-responsive {
    background: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card-responsive::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.stat-icon-responsive {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    color: white;
}

.stat-number-responsive {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.stat-label-responsive {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 10px;
}

/* التنبيهات المتجاوبة */
.alert-responsive {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert-success-responsive {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger-responsive {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning-responsive {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info-responsive {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* التحميل والتقدم */
.progress-responsive {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar-responsive {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transition: width 0.6s ease;
}

/* الشارات */
.badge-responsive {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary-responsive {
    background-color: var(--primary-color);
    color: white;
}

.badge-success-responsive {
    background-color: var(--success-color);
    color: white;
}

.badge-warning-responsive {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.badge-danger-responsive {
    background-color: var(--danger-color);
    color: white;
}

/* الأدوات المساعدة */
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-left { text-align: left !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-end { justify-content: flex-end !important; }

.align-items-center { align-items: center !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.m-4 { margin: 1.5rem !important; }
.m-5 { margin: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

/* الأنيميشن */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.animate-fadeIn {
    animation: fadeIn 0.6s ease-out;
}

.animate-slideInRight {
    animation: slideInRight 0.4s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* تأثيرات التحويم */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(0,0,0,0.15);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.02);
}

/* الظلال */
.shadow-sm { box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
.shadow { box-shadow: var(--box-shadow); }
.shadow-lg { box-shadow: 0 4px 20px rgba(0,0,0,0.15); }
.shadow-xl { box-shadow: 0 8px 40px rgba(0,0,0,0.2); }

/* الحدود */
.border { border: 1px solid #dee2e6; }
.border-top { border-top: 1px solid #dee2e6; }
.border-bottom { border-bottom: 1px solid #dee2e6; }
.border-left { border-left: 1px solid #dee2e6; }
.border-right { border-right: 1px solid #dee2e6; }

.rounded { border-radius: var(--border-radius); }
.rounded-lg { border-radius: 12px; }
.rounded-xl { border-radius: 16px; }
.rounded-full { border-radius: 50%; }

/* الألوان */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-light { color: var(--light-color) !important; }
.text-dark { color: var(--dark-color) !important; }
.text-muted { color: #6c757d !important; }
.text-white { color: white !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-light { background-color: var(--light-color) !important; }
.bg-dark { background-color: var(--dark-color) !important; }
.bg-white { background-color: white !important; }

/* التدرجات */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
}

/* ========================================
 * Media Queries للتجاوب مع الأجهزة المختلفة
 * ======================================== */

/* الشاشات الكبيرة جداً (1400px وأكثر) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }

    .stats-grid {
        grid-template-columns: repeat(6, 1fr);
    }

    .stat-number-responsive {
        font-size: 3rem;
    }

    .card-responsive {
        margin-bottom: 25px;
    }
}

/* الشاشات الكبيرة (1200px - 1399px) */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        max-width: 1140px;
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .sidebar-responsive {
        position: relative;
        right: 0;
        width: 250px;
        height: auto;
        box-shadow: none;
        border-left: 1px solid #dee2e6;
    }

    .main-content-responsive {
        margin-right: 250px;
    }
}

/* الشاشات المتوسطة الكبيرة (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        max-width: 960px;
    }

    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
    .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
    .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-lg-12 { flex: 0 0 100%; max-width: 100%; }

    .card-body-responsive {
        padding: 18px;
    }
}

/* الشاشات المتوسطة (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        max-width: 720px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }

    .header-responsive {
        padding: 0 15px;
    }

    .logo-responsive {
        font-size: 1.1rem;
    }

    .logo-responsive img {
        height: 35px;
    }

    .card-body-responsive {
        padding: 16px;
    }

    .stat-number-responsive {
        font-size: 2.2rem;
    }

    .btn-responsive {
        padding: 10px 20px;
        font-size: 13px;
    }

    /* إخفاء بعض العناصر في الشاشات المتوسطة */
    .d-md-none {
        display: none !important;
    }

    .d-md-block {
        display: block !important;
    }

    .d-md-flex {
        display: flex !important;
    }
}

/* الشاشات الصغيرة (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        max-width: 540px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
    .col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }

    .header-responsive {
        height: 60px;
        padding: 0 12px;
    }

    :root {
        --header-height: 60px;
    }

    .logo-responsive {
        font-size: 1rem;
    }

    .logo-responsive img {
        height: 30px;
    }

    .main-content-responsive {
        padding: 15px;
    }

    .card-body-responsive {
        padding: 14px;
    }

    .card-header-responsive {
        padding: 12px 14px;
        font-size: 14px;
    }

    .stat-card-responsive {
        padding: 20px 15px;
    }

    .stat-number-responsive {
        font-size: 2rem;
    }

    .stat-icon-responsive {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .btn-responsive {
        padding: 10px 16px;
        font-size: 13px;
        min-height: 40px;
    }

    .form-control-responsive {
        padding: 10px 14px;
        font-size: 15px;
    }

    /* الجداول في الشاشات الصغيرة */
    .table-mobile {
        font-size: 13px;
    }

    .table-mobile th,
    .table-mobile td {
        padding: 8px 12px;
    }

    /* إخفاء بعض العناصر في الشاشات الصغيرة */
    .d-sm-none {
        display: none !important;
    }

    .d-sm-block {
        display: block !important;
    }

    .d-sm-flex {
        display: flex !important;
    }
}

/* الشاشات الصغيرة جداً (أقل من 576px) - الجوال */
@media (max-width: 575px) {
    html {
        font-size: 14px;
    }

    .container {
        padding: 0 10px;
    }

    .row {
        margin: 0 -10px;
    }

    .col {
        padding: 0 10px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    /* جميع الأعمدة تصبح عرض كامل في الجوال */
    .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
    .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .header-responsive {
        height: 55px;
        padding: 0 10px;
    }

    :root {
        --header-height: 55px;
        --sidebar-width: 100%;
    }

    .logo-responsive {
        font-size: 0.9rem;
    }

    .logo-responsive img {
        height: 28px;
        margin-left: 8px;
    }

    .sidebar-responsive {
        width: 100%;
        right: -100%;
    }

    .main-content-responsive {
        padding: 12px 10px;
    }

    .card-responsive {
        margin-bottom: 15px;
        border-radius: 6px;
    }

    .card-body-responsive {
        padding: 12px;
    }

    .card-header-responsive {
        padding: 10px 12px;
        font-size: 13px;
    }

    .card-footer-responsive {
        padding: 10px 12px;
    }

    .stat-card-responsive {
        padding: 18px 12px;
    }

    .stat-number-responsive {
        font-size: 1.8rem;
    }

    .stat-icon-responsive {
        width: 45px;
        height: 45px;
        font-size: 18px;
        margin-bottom: 12px;
    }

    .stat-label-responsive {
        font-size: 12px;
    }

    .btn-responsive {
        width: 100%;
        padding: 12px 16px;
        font-size: 14px;
        margin-bottom: 8px;
        min-height: 44px;
    }

    .btn-group-mobile .btn-responsive {
        width: auto;
        flex: 1;
        margin-bottom: 0;
        margin-left: 4px;
    }

    .btn-group-mobile .btn-responsive:first-child {
        margin-left: 0;
    }

    .form-control-responsive {
        padding: 12px 14px;
        font-size: 16px; /* لمنع التكبير في iOS */
        border-radius: 6px;
    }

    .form-group-responsive {
        margin-bottom: 16px;
    }

    .form-label-responsive {
        font-size: 13px;
        margin-bottom: 6px;
    }

    /* الجداول المتجاوبة للجوال */
    .table-responsive-custom {
        border-radius: 6px;
        overflow: hidden;
    }

    .table-mobile {
        font-size: 12px;
    }

    .table-mobile th,
    .table-mobile td {
        padding: 6px 8px;
        white-space: nowrap;
    }

    /* تحويل الجداول إلى بطاقات في الجوال */
    .table-mobile-cards {
        display: none;
    }

    .table-mobile-cards .card-responsive {
        margin-bottom: 10px;
    }

    .table-mobile-cards .card-body-responsive {
        padding: 10px;
    }

    /* إخفاء الجدول العادي وإظهار البطاقات */
    .table-responsive-custom {
        display: none;
    }

    .table-mobile-cards {
        display: block;
    }

    /* التنبيهات في الجوال */
    .alert-responsive {
        padding: 12px 14px;
        font-size: 13px;
        border-radius: 6px;
    }

    /* الشارات في الجوال */
    .badge-responsive {
        padding: 3px 8px;
        font-size: 10px;
    }

    /* التقدم في الجوال */
    .progress-responsive {
        height: 6px;
        border-radius: 3px;
    }

    /* النصوص في الجوال */
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.6rem; }
    h3 { font-size: 1.4rem; }
    h4 { font-size: 1.2rem; }
    h5 { font-size: 1.1rem; }
    h6 { font-size: 1rem; }

    /* المسافات في الجوال */
    .mb-mobile-1 { margin-bottom: 0.25rem !important; }
    .mb-mobile-2 { margin-bottom: 0.5rem !important; }
    .mb-mobile-3 { margin-bottom: 0.75rem !important; }
    .mb-mobile-4 { margin-bottom: 1rem !important; }
    .mb-mobile-5 { margin-bottom: 1.25rem !important; }

    .mt-mobile-1 { margin-top: 0.25rem !important; }
    .mt-mobile-2 { margin-top: 0.5rem !important; }
    .mt-mobile-3 { margin-top: 0.75rem !important; }
    .mt-mobile-4 { margin-top: 1rem !important; }
    .mt-mobile-5 { margin-top: 1.25rem !important; }

    .p-mobile-1 { padding: 0.25rem !important; }
    .p-mobile-2 { padding: 0.5rem !important; }
    .p-mobile-3 { padding: 0.75rem !important; }
    .p-mobile-4 { padding: 1rem !important; }
    .p-mobile-5 { padding: 1.25rem !important; }

    /* إخفاء العناصر في الجوال */
    .d-mobile-none {
        display: none !important;
    }

    .d-mobile-block {
        display: block !important;
    }

    .d-mobile-flex {
        display: flex !important;
    }

    /* النصوص المتجاوبة */
    .text-mobile-center {
        text-align: center !important;
    }

    .text-mobile-right {
        text-align: right !important;
    }

    .text-mobile-left {
        text-align: left !important;
    }
}
