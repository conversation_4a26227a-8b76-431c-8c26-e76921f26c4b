<?php
/**
 * نظام البحث المتقدم
 * Advanced Search System
 * ======================
 */

require_once 'config/config.php';

class SearchSystem {
    private $conn;
    private $searchIndex = [];
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->createSearchTables();
    }
    
    /**
     * إنشاء جداول البحث
     */
    private function createSearchTables() {
        try {
            // جدول فهرس البحث
            $sql = "CREATE TABLE IF NOT EXISTS search_index (
                id INT AUTO_INCREMENT PRIMARY KEY,
                content_type ENUM('course', 'instructor', 'video', 'assignment') NOT NULL,
                content_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                keywords TEXT NULL,
                tags JSON NULL,
                category_id INT NULL,
                instructor_id INT NULL,
                language VARCHAR(10) DEFAULT 'ar',
                search_weight DECIMAL(3,2) DEFAULT 1.00,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_content_type (content_type),
                INDEX idx_content_id (content_id),
                INDEX idx_category (category_id),
                INDEX idx_instructor (instructor_id),
                INDEX idx_active (is_active),
                INDEX idx_language (language),
                FULLTEXT idx_search (title, content, keywords),
                UNIQUE KEY unique_content (content_type, content_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // جدول إحصائيات البحث
            $sql = "CREATE TABLE IF NOT EXISTS search_analytics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                search_query VARCHAR(255) NOT NULL,
                user_id INT NULL,
                results_count INT DEFAULT 0,
                clicked_result_id INT NULL,
                clicked_result_type VARCHAR(50) NULL,
                search_filters JSON NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_query (search_query),
                INDEX idx_user (user_id),
                INDEX idx_created (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // جدول الكلمات المفتاحية الشائعة
            $sql = "CREATE TABLE IF NOT EXISTS popular_keywords (
                id INT AUTO_INCREMENT PRIMARY KEY,
                keyword VARCHAR(100) NOT NULL UNIQUE,
                search_count INT DEFAULT 1,
                last_searched TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_keyword (keyword),
                INDEX idx_count (search_count),
                INDEX idx_last_searched (last_searched)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
        } catch (PDOException $e) {
            error_log("Error creating search tables: " . $e->getMessage());
        }
    }
    
    /**
     * البحث المتقدم
     */
    public function search($query, $filters = [], $options = []) {
        try {
            // تنظيف وتحضير الاستعلام
            $cleanQuery = $this->cleanSearchQuery($query);
            if (empty($cleanQuery)) {
                return $this->getEmptySearchResult();
            }
            
            // تسجيل البحث في الإحصائيات
            $this->logSearch($query, $filters, $options);
            
            // تحديث الكلمات المفتاحية الشائعة
            $this->updatePopularKeywords($cleanQuery);
            
            // بناء استعلام البحث
            $searchQuery = $this->buildSearchQuery($cleanQuery, $filters, $options);
            
            // تنفيذ البحث
            $results = $this->executeSearch($searchQuery, $options);
            
            // معالجة النتائج
            $processedResults = $this->processSearchResults($results, $cleanQuery);
            
            // إضافة اقتراحات
            $suggestions = $this->getSuggestions($cleanQuery, $filters);
            
            return [
                'success' => true,
                'query' => $query,
                'clean_query' => $cleanQuery,
                'total_results' => count($processedResults),
                'results' => $processedResults,
                'suggestions' => $suggestions,
                'filters_applied' => $filters,
                'search_time' => microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true))
            ];
            
        } catch (Exception $e) {
            error_log("Search Error: " . $e->getMessage());
            return $this->getErrorSearchResult($e->getMessage());
        }
    }
    
    /**
     * البحث السريع (للاقتراحات التلقائية)
     */
    public function quickSearch($query, $limit = 10) {
        try {
            $cleanQuery = $this->cleanSearchQuery($query);
            if (strlen($cleanQuery) < 2) {
                return [];
            }
            
            $stmt = $this->conn->prepare("
                SELECT 
                    content_type,
                    content_id,
                    title,
                    SUBSTRING(content, 1, 100) as preview,
                    search_weight,
                    MATCH(title, content, keywords) AGAINST(? IN NATURAL LANGUAGE MODE) as relevance
                FROM search_index 
                WHERE is_active = TRUE 
                AND MATCH(title, content, keywords) AGAINST(? IN NATURAL LANGUAGE MODE)
                ORDER BY relevance DESC, search_weight DESC
                LIMIT ?
            ");
            
            $stmt->execute([$cleanQuery, $cleanQuery, $limit]);
            $results = $stmt->fetchAll();
            
            return array_map(function($result) {
                return [
                    'type' => $result['content_type'],
                    'id' => $result['content_id'],
                    'title' => $result['title'],
                    'preview' => $result['preview'],
                    'relevance' => $result['relevance']
                ];
            }, $results);
            
        } catch (Exception $e) {
            error_log("Quick Search Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * البحث بالفلاتر المتقدمة
     */
    public function advancedSearch($params) {
        $filters = [];
        $options = [
            'page' => $params['page'] ?? 1,
            'per_page' => $params['per_page'] ?? 20,
            'sort_by' => $params['sort_by'] ?? 'relevance',
            'sort_order' => $params['sort_order'] ?? 'DESC'
        ];
        
        // فلاتر المحتوى
        if (!empty($params['content_type'])) {
            $filters['content_type'] = $params['content_type'];
        }
        
        if (!empty($params['category_id'])) {
            $filters['category_id'] = $params['category_id'];
        }
        
        if (!empty($params['instructor_id'])) {
            $filters['instructor_id'] = $params['instructor_id'];
        }
        
        if (!empty($params['language'])) {
            $filters['language'] = $params['language'];
        }
        
        // فلاتر الكورسات
        if (!empty($params['course_level'])) {
            $filters['course_level'] = $params['course_level'];
        }
        
        if (!empty($params['course_type'])) {
            $filters['course_type'] = $params['course_type'];
        }
        
        if (!empty($params['price_range'])) {
            $filters['price_range'] = $params['price_range'];
        }
        
        if (!empty($params['rating_min'])) {
            $filters['rating_min'] = $params['rating_min'];
        }
        
        return $this->search($params['query'] ?? '', $filters, $options);
    }
    
    /**
     * تنظيف استعلام البحث
     */
    private function cleanSearchQuery($query) {
        // إزالة الأحرف الخاصة والمسافات الزائدة
        $cleaned = trim(preg_replace('/[^\p{L}\p{N}\s\-_]/u', ' ', $query));
        
        // إزالة المسافات المتعددة
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        
        // إزالة الكلمات القصيرة جداً (أقل من حرفين)
        $words = explode(' ', $cleaned);
        $words = array_filter($words, function($word) {
            return mb_strlen($word, 'UTF-8') >= 2;
        });
        
        return implode(' ', $words);
    }
    
    /**
     * بناء استعلام البحث
     */
    private function buildSearchQuery($query, $filters, $options) {
        $sql = "
            SELECT DISTINCT
                si.content_type,
                si.content_id,
                si.title,
                si.content,
                si.keywords,
                si.tags,
                si.search_weight,
                MATCH(si.title, si.content, si.keywords) AGAINST(? IN NATURAL LANGUAGE MODE) as relevance_score
        ";
        
        // إضافة معلومات إضافية حسب نوع المحتوى
        $sql .= ",
            CASE si.content_type
                WHEN 'course' THEN (
                    SELECT JSON_OBJECT(
                        'instructor_name', u.name,
                        'category_name', cat.name,
                        'price', c.price,
                        'level', c.level,
                        'rating', c.rating,
                        'students_count', c.total_students
                    )
                    FROM courses c
                    LEFT JOIN users u ON c.instructor_id = u.id
                    LEFT JOIN categories cat ON c.category_id = cat.id
                    WHERE c.id = si.content_id
                )
                WHEN 'instructor' THEN (
                    SELECT JSON_OBJECT(
                        'email', u.email,
                        'specialization', u.specialization,
                        'courses_count', (SELECT COUNT(*) FROM courses WHERE instructor_id = u.id)
                    )
                    FROM users u
                    WHERE u.id = si.content_id
                )
                ELSE NULL
            END as additional_info
        ";
        
        $sql .= " FROM search_index si WHERE si.is_active = TRUE";
        
        $params = [$query];
        
        // إضافة شرط البحث النصي
        $sql .= " AND MATCH(si.title, si.content, si.keywords) AGAINST(? IN NATURAL LANGUAGE MODE)";
        $params[] = $query;
        
        // إضافة الفلاتر
        if (!empty($filters['content_type'])) {
            $sql .= " AND si.content_type = ?";
            $params[] = $filters['content_type'];
        }
        
        if (!empty($filters['category_id'])) {
            $sql .= " AND si.category_id = ?";
            $params[] = $filters['category_id'];
        }
        
        if (!empty($filters['instructor_id'])) {
            $sql .= " AND si.instructor_id = ?";
            $params[] = $filters['instructor_id'];
        }
        
        if (!empty($filters['language'])) {
            $sql .= " AND si.language = ?";
            $params[] = $filters['language'];
        }
        
        // فلاتر خاصة بالكورسات
        if (!empty($filters['course_level']) || !empty($filters['course_type']) || !empty($filters['price_range'])) {
            $sql .= " AND si.content_type = 'course'";
            
            if (!empty($filters['course_level'])) {
                $sql .= " AND EXISTS (SELECT 1 FROM courses c WHERE c.id = si.content_id AND c.level = ?)";
                $params[] = $filters['course_level'];
            }
            
            if (!empty($filters['course_type'])) {
                $sql .= " AND EXISTS (SELECT 1 FROM courses c WHERE c.id = si.content_id AND c.course_type = ?)";
                $params[] = $filters['course_type'];
            }
            
            if (!empty($filters['price_range'])) {
                list($min_price, $max_price) = explode('-', $filters['price_range']);
                $sql .= " AND EXISTS (SELECT 1 FROM courses c WHERE c.id = si.content_id AND c.price BETWEEN ? AND ?)";
                $params[] = $min_price;
                $params[] = $max_price;
            }
        }
        
        // ترتيب النتائج
        $sortBy = $options['sort_by'] ?? 'relevance';
        $sortOrder = $options['sort_order'] ?? 'DESC';
        
        switch ($sortBy) {
            case 'relevance':
                $sql .= " ORDER BY relevance_score DESC, si.search_weight DESC";
                break;
            case 'title':
                $sql .= " ORDER BY si.title " . $sortOrder;
                break;
            case 'date':
                $sql .= " ORDER BY si.updated_at " . $sortOrder;
                break;
            default:
                $sql .= " ORDER BY relevance_score DESC";
        }
        
        // تحديد عدد النتائج
        $page = max(1, $options['page'] ?? 1);
        $perPage = min(100, max(1, $options['per_page'] ?? 20));
        $offset = ($page - 1) * $perPage;
        
        $sql .= " LIMIT ? OFFSET ?";
        $params[] = $perPage;
        $params[] = $offset;
        
        return ['sql' => $sql, 'params' => $params];
    }
    
    /**
     * تنفيذ البحث
     */
    private function executeSearch($searchQuery, $options) {
        try {
            $stmt = $this->conn->prepare($searchQuery['sql']);
            $stmt->execute($searchQuery['params']);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Search Execution Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * معالجة نتائج البحث
     */
    private function processSearchResults($results, $query) {
        $processed = [];
        
        foreach ($results as $result) {
            $item = [
                'type' => $result['content_type'],
                'id' => $result['content_id'],
                'title' => $this->highlightSearchTerms($result['title'], $query),
                'content' => $this->getContentPreview($result['content'], $query),
                'keywords' => $result['keywords'],
                'tags' => json_decode($result['tags'], true),
                'relevance' => $result['relevance_score'],
                'weight' => $result['search_weight'],
                'url' => $this->generateContentUrl($result['content_type'], $result['content_id'])
            ];
            
            // إضافة معلومات إضافية
            if ($result['additional_info']) {
                $item['additional_info'] = json_decode($result['additional_info'], true);
            }
            
            $processed[] = $item;
        }
        
        return $processed;
    }
    
    /**
     * تمييز مصطلحات البحث في النص
     */
    private function highlightSearchTerms($text, $query) {
        $words = explode(' ', $query);
        
        foreach ($words as $word) {
            if (strlen($word) >= 2) {
                $text = preg_replace(
                    '/(' . preg_quote($word, '/') . ')/iu',
                    '<mark>$1</mark>',
                    $text
                );
            }
        }
        
        return $text;
    }
    
    /**
     * الحصول على معاينة المحتوى
     */
    private function getContentPreview($content, $query, $length = 200) {
        // البحث عن أول ظهور لكلمة البحث
        $words = explode(' ', $query);
        $position = 0;
        
        foreach ($words as $word) {
            $pos = mb_stripos($content, $word, 0, 'UTF-8');
            if ($pos !== false) {
                $position = max(0, $pos - 50);
                break;
            }
        }
        
        // استخراج المعاينة
        $preview = mb_substr($content, $position, $length, 'UTF-8');
        
        // إضافة نقاط في البداية والنهاية إذا لزم الأمر
        if ($position > 0) {
            $preview = '...' . $preview;
        }
        
        if (mb_strlen($content, 'UTF-8') > $position + $length) {
            $preview .= '...';
        }
        
        return $this->highlightSearchTerms($preview, $query);
    }
    
    /**
     * إنشاء رابط المحتوى
     */
    private function generateContentUrl($type, $id) {
        switch ($type) {
            case 'course':
                return "/course-details.php?id={$id}";
            case 'instructor':
                return "/instructor-profile.php?id={$id}";
            case 'video':
                return "/video-view.php?id={$id}";
            case 'assignment':
                return "/assignment-view.php?id={$id}";
            default:
                return "#";
        }
    }
    
    /**
     * الحصول على اقتراحات البحث
     */
    private function getSuggestions($query, $filters) {
        try {
            // اقتراحات من الكلمات المفتاحية الشائعة
            $stmt = $this->conn->prepare("
                SELECT keyword, search_count 
                FROM popular_keywords 
                WHERE keyword LIKE ? 
                ORDER BY search_count DESC 
                LIMIT 5
            ");
            $stmt->execute(["%{$query}%"]);
            $keywordSuggestions = $stmt->fetchAll();
            
            // اقتراحات من العناوين المشابهة
            $stmt = $this->conn->prepare("
                SELECT DISTINCT title 
                FROM search_index 
                WHERE title LIKE ? AND is_active = TRUE 
                ORDER BY search_weight DESC 
                LIMIT 5
            ");
            $stmt->execute(["%{$query}%"]);
            $titleSuggestions = $stmt->fetchAll();
            
            return [
                'keywords' => array_column($keywordSuggestions, 'keyword'),
                'titles' => array_column($titleSuggestions, 'title')
            ];
            
        } catch (Exception $e) {
            error_log("Suggestions Error: " . $e->getMessage());
            return ['keywords' => [], 'titles' => []];
        }
    }
    
    /**
     * تسجيل البحث في الإحصائيات
     */
    private function logSearch($query, $filters, $options) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO search_analytics 
                (search_query, user_id, search_filters, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $query,
                $_SESSION['user_id'] ?? null,
                json_encode($filters),
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (PDOException $e) {
            error_log("Search Logging Error: " . $e->getMessage());
        }
    }
    
    /**
     * تحديث الكلمات المفتاحية الشائعة
     */
    private function updatePopularKeywords($query) {
        try {
            $words = explode(' ', $query);
            
            foreach ($words as $word) {
                if (strlen($word) >= 3) {
                    $stmt = $this->conn->prepare("
                        INSERT INTO popular_keywords (keyword, search_count) 
                        VALUES (?, 1) 
                        ON DUPLICATE KEY UPDATE 
                        search_count = search_count + 1, 
                        last_searched = NOW()
                    ");
                    $stmt->execute([$word]);
                }
            }
        } catch (PDOException $e) {
            error_log("Popular Keywords Update Error: " . $e->getMessage());
        }
    }
    
    /**
     * فهرسة المحتوى للبحث
     */
    public function indexContent($contentType, $contentId, $data) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO search_index 
                (content_type, content_id, title, content, keywords, tags, category_id, instructor_id, language, search_weight) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                title = VALUES(title),
                content = VALUES(content),
                keywords = VALUES(keywords),
                tags = VALUES(tags),
                category_id = VALUES(category_id),
                instructor_id = VALUES(instructor_id),
                language = VALUES(language),
                search_weight = VALUES(search_weight),
                updated_at = NOW()
            ");
            
            $stmt->execute([
                $contentType,
                $contentId,
                $data['title'],
                $data['content'],
                $data['keywords'] ?? '',
                json_encode($data['tags'] ?? []),
                $data['category_id'] ?? null,
                $data['instructor_id'] ?? null,
                $data['language'] ?? 'ar',
                $data['search_weight'] ?? 1.0
            ]);
            
            return true;
        } catch (PDOException $e) {
            error_log("Content Indexing Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف المحتوى من الفهرس
     */
    public function removeFromIndex($contentType, $contentId) {
        try {
            $stmt = $this->conn->prepare("
                DELETE FROM search_index 
                WHERE content_type = ? AND content_id = ?
            ");
            $stmt->execute([$contentType, $contentId]);
            return true;
        } catch (PDOException $e) {
            error_log("Content Removal Error: " . $e->getMessage());
            return false;
        }
    }
    
    // دوال مساعدة للنتائج
    private function getEmptySearchResult() {
        return [
            'success' => true,
            'query' => '',
            'total_results' => 0,
            'results' => [],
            'suggestions' => ['keywords' => [], 'titles' => []],
            'message' => 'يرجى إدخال كلمة بحث'
        ];
    }
    
    private function getErrorSearchResult($error) {
        return [
            'success' => false,
            'error' => $error,
            'total_results' => 0,
            'results' => [],
            'suggestions' => ['keywords' => [], 'titles' => []]
        ];
    }
}

// دوال مساعدة سريعة
function quickSearch($query, $limit = 10) {
    global $conn;
    
    try {
        $searchSystem = new SearchSystem($conn);
        return $searchSystem->quickSearch($query, $limit);
    } catch (Exception $e) {
        error_log("Quick Search Helper Error: " . $e->getMessage());
        return [];
    }
}

function indexCourse($courseId) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT c.*, u.name as instructor_name, cat.name as category_name
            FROM courses c
            LEFT JOIN users u ON c.instructor_id = u.id
            LEFT JOIN categories cat ON c.category_id = cat.id
            WHERE c.id = ?
        ");
        $stmt->execute([$courseId]);
        $course = $stmt->fetch();
        
        if ($course) {
            $searchSystem = new SearchSystem($conn);
            
            $data = [
                'title' => $course['title'],
                'content' => $course['description'] . ' ' . $course['short_description'],
                'keywords' => $course['title'] . ' ' . $course['instructor_name'] . ' ' . $course['category_name'],
                'tags' => [$course['level'], $course['course_type'], $course['language']],
                'category_id' => $course['category_id'],
                'instructor_id' => $course['instructor_id'],
                'language' => $course['language'] ?? 'ar',
                'search_weight' => $course['featured'] ? 1.5 : 1.0
            ];
            
            return $searchSystem->indexContent('course', $courseId, $data);
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Course Indexing Helper Error: " . $e->getMessage());
        return false;
    }
}
?>
