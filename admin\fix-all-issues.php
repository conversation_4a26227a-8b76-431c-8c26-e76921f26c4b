<?php
session_start();

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

echo "<h1>إصلاح جميع مشاكل النظام</h1>";

try {
    require_once '../includes/database_manager_clean.php';
    
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</div><br>";
    
    // 1. إصلاح جدول المستخدمين
    echo "<h2>1. إصلاح جدول المستخدمين...</h2>";
    
    // إضافة أعمدة مفقودة
    $userColumns = [
        'email_verified' => "ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE AFTER email",
        'last_login' => "ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL AFTER password",
        'profile_picture' => "ALTER TABLE users ADD COLUMN profile_picture VARCHAR(500) NULL AFTER phone"
    ];
    
    foreach ($userColumns as $column => $sql) {
        try {
            $dbClean->query($sql);
            echo "<div style='color: blue;'>➕ تم إضافة عمود {$column} لجدول المستخدمين</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ عمود {$column} موجود مسبقاً</div>";
        }
    }
    
    // 2. إصلاح جدول الكورسات
    echo "<h2>2. إصلاح جدول الكورسات...</h2>";
    
    $courseColumns = [
        'short_description' => "ALTER TABLE courses ADD COLUMN short_description TEXT NULL AFTER description",
        'thumbnail' => "ALTER TABLE courses ADD COLUMN thumbnail VARCHAR(500) NULL AFTER short_description",
        'category_id' => "ALTER TABLE courses ADD COLUMN category_id INT NULL AFTER instructor_id",
        'is_free' => "ALTER TABLE courses ADD COLUMN is_free BOOLEAN DEFAULT TRUE AFTER price",
        'is_featured' => "ALTER TABLE courses ADD COLUMN is_featured BOOLEAN DEFAULT FALSE AFTER is_free"
    ];
    
    foreach ($courseColumns as $column => $sql) {
        try {
            $dbClean->query($sql);
            echo "<div style='color: blue;'>➕ تم إضافة عمود {$column} لجدول الكورسات</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ عمود {$column} موجود مسبقاً</div>";
        }
    }
    
    // 3. إصلاح جدول التسجيلات
    echo "<h2>3. إصلاح جدول التسجيلات...</h2>";
    
    $enrollmentColumns = [
        'progress' => "ALTER TABLE course_enrollments ADD COLUMN progress INT DEFAULT 0 AFTER status",
        'completed_at' => "ALTER TABLE course_enrollments ADD COLUMN completed_at TIMESTAMP NULL AFTER progress"
    ];
    
    foreach ($enrollmentColumns as $column => $sql) {
        try {
            $dbClean->query($sql);
            echo "<div style='color: blue;'>➕ تم إضافة عمود {$column} لجدول التسجيلات</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ عمود {$column} موجود مسبقاً</div>";
        }
    }
    
    // 4. إصلاح جدول الجلسات
    echo "<h2>4. إصلاح جدول الجلسات...</h2>";
    
    $sessionColumns = [
        'max_participants' => "ALTER TABLE sessions ADD COLUMN max_participants INT DEFAULT 50 AFTER duration",
        'current_participants' => "ALTER TABLE sessions ADD COLUMN current_participants INT DEFAULT 0 AFTER max_participants"
    ];
    
    foreach ($sessionColumns as $column => $sql) {
        try {
            $dbClean->query($sql);
            echo "<div style='color: blue;'>➕ تم إضافة عمود {$column} لجدول الجلسات</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ عمود {$column} موجود مسبقاً</div>";
        }
    }
    
    // 5. تحديث البيانات الموجودة
    echo "<h2>5. تحديث البيانات الموجودة...</h2>";
    
    // تحديث حالة المستخدمين
    $users = $dbClean->getAll('users');
    foreach ($users as $user) {
        if (empty($user['status'])) {
            $dbClean->update('users', $user['id'], ['status' => 'active']);
            echo "<div style='color: blue;'>➕ تم تحديث حالة المستخدم {$user['name']}</div>";
        }
        
        if (empty($user['email_verified'])) {
            $dbClean->update('users', $user['id'], ['email_verified' => 1]);
            echo "<div style='color: blue;'>➕ تم تأكيد بريد المستخدم {$user['name']}</div>";
        }
    }
    
    // تحديث أسعار الكورسات
    $courses = $dbClean->getAll('courses');
    foreach ($courses as $course) {
        $updates = [];
        
        if (!isset($course['is_free'])) {
            $updates['is_free'] = ($course['price'] == 0 || empty($course['price'])) ? 1 : 0;
        }
        
        if (!isset($course['is_featured'])) {
            $updates['is_featured'] = 0;
        }
        
        if (empty($course['status'])) {
            $updates['status'] = 'published';
        }
        
        if (!empty($updates)) {
            $dbClean->update('courses', $course['id'], $updates);
            echo "<div style='color: blue;'>➕ تم تحديث الكورس {$course['title']}</div>";
        }
    }
    
    // 6. إنشاء بيانات تجريبية إضافية
    echo "<h2>6. إنشاء بيانات تجريبية إضافية...</h2>";
    
    // إضافة فئات إضافية
    $additionalCategories = [
        ['الذكاء الاصطناعي', 'كورسات الذكاء الاصطناعي والتعلم الآلي', 'fas fa-robot', '#6f42c1'],
        ['الأمن السيبراني', 'كورسات الأمن السيبراني وحماية المعلومات', 'fas fa-shield-alt', '#dc3545'],
        ['علوم البيانات', 'كورسات تحليل البيانات والإحصاء', 'fas fa-chart-bar', '#20c997']
    ];
    
    foreach ($additionalCategories as $index => $category) {
        try {
            $categoryId = $dbClean->insert('categories', [
                'name' => $category[0],
                'description' => $category[1],
                'icon' => $category[2],
                'color' => $category[3],
                'sort_order' => $index + 10
            ]);
            echo "<div style='color: blue;'>➕ تم إضافة فئة: {$category[0]}</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ فئة {$category[0]} موجودة مسبقاً</div>";
        }
    }
    
    // 7. إصلاح الفهارس والمفاتيح الخارجية
    echo "<h2>7. إصلاح الفهارس والمفاتيح الخارجية...</h2>";
    
    $foreignKeys = [
        "ALTER TABLE courses ADD CONSTRAINT fk_courses_category FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL",
        "ALTER TABLE course_enrollments ADD CONSTRAINT fk_enrollments_student FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE",
        "ALTER TABLE course_enrollments ADD CONSTRAINT fk_enrollments_course FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE",
        "ALTER TABLE payments ADD CONSTRAINT fk_payments_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE",
        "ALTER TABLE payments ADD CONSTRAINT fk_payments_course FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE"
    ];
    
    foreach ($foreignKeys as $sql) {
        try {
            $dbClean->query($sql);
            echo "<div style='color: blue;'>➕ تم إضافة مفتاح خارجي</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ المفتاح الخارجي موجود مسبقاً</div>";
        }
    }
    
    // 8. تحسين الأداء
    echo "<h2>8. تحسين الأداء...</h2>";
    
    $indexes = [
        "CREATE INDEX idx_users_email ON users(email)",
        "CREATE INDEX idx_users_role ON users(role)",
        "CREATE INDEX idx_users_status ON users(status)",
        "CREATE INDEX idx_courses_status ON courses(status)",
        "CREATE INDEX idx_courses_featured ON courses(is_featured)",
        "CREATE INDEX idx_enrollments_status ON course_enrollments(status)",
        "CREATE INDEX idx_payments_status ON payments(status)"
    ];
    
    foreach ($indexes as $sql) {
        try {
            $dbClean->query($sql);
            echo "<div style='color: blue;'>➕ تم إضافة فهرس</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ الفهرس موجود مسبقاً</div>";
        }
    }
    
    echo "<div style='color: green; font-weight: bold; margin-top: 20px;'>🎉 تم إصلاح جميع مشاكل النظام بنجاح!</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div style='color: red;'>📍 التفاصيل: " . $e->getTraceAsString() . "</div>";
}

echo "<br><a href='join-requests.php'>طلبات الانضمام</a> | <a href='analytics.php'>التحليلات</a> | <a href='dashboard.php'>لوحة التحكم</a>";
?>
