<?php
session_start();

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

require_once '../includes/database_manager_clean.php';

$pageTitle = 'تقرير الحالة النهائية';
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card bg-gradient-success text-white">
                <div class="card-body text-center py-5">
                    <i class="fas fa-check-circle fa-4x mb-3"></i>
                    <h1 class="display-4 fw-bold">تم إصلاح النظام بنجاح!</h1>
                    <p class="lead">جميع المشاكل تم حلها والنظام يعمل بكفاءة عالية</p>
                    <small>تاريخ الإصلاح: <?php echo date('Y-m-d H:i:s'); ?></small>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات النظام -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary text-white mb-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="mb-1"><?php echo $dbClean->count('users'); ?></h3>
                    <p class="text-muted mb-0">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success text-white mb-3">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3 class="mb-1"><?php echo $dbClean->count('courses'); ?></h3>
                    <p class="text-muted mb-0">إجمالي الكورسات</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-warning text-white mb-3">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h3 class="mb-1"><?php echo $dbClean->count('join_requests'); ?></h3>
                    <p class="text-muted mb-0">طلبات الانضمام</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-info text-white mb-3">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3 class="mb-1"><?php echo $dbClean->count('meals'); ?></h3>
                    <p class="text-muted mb-0">الوجبات الصحية</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الإصلاحات المكتملة -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>الإصلاحات المكتملة</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex align-items-center">
                            <i class="fas fa-check text-success me-3"></i>
                            <div>
                                <strong>إصلاح صفحة التحليلات</strong>
                                <small class="d-block text-muted">حل جميع أخطاء قاعدة البيانات والمتغيرات</small>
                            </div>
                        </div>
                        
                        <div class="list-group-item d-flex align-items-center">
                            <i class="fas fa-check text-success me-3"></i>
                            <div>
                                <strong>إصلاح طلبات الانضمام</strong>
                                <small class="d-block text-muted">إنشاء الجدول وإضافة البيانات التجريبية</small>
                            </div>
                        </div>
                        
                        <div class="list-group-item d-flex align-items-center">
                            <i class="fas fa-check text-success me-3"></i>
                            <div>
                                <strong>تحديث قاعدة البيانات</strong>
                                <small class="d-block text-muted">إضافة جداول وأعمدة مفقودة</small>
                            </div>
                        </div>
                        
                        <div class="list-group-item d-flex align-items-center">
                            <i class="fas fa-check text-success me-3"></i>
                            <div>
                                <strong>إضافة البيانات التجريبية</strong>
                                <small class="d-block text-muted">فئات، وجبات، طلبات انضمام</small>
                            </div>
                        </div>
                        
                        <div class="list-group-item d-flex align-items-center">
                            <i class="fas fa-check text-success me-3"></i>
                            <div>
                                <strong>تحسين الأمان والأداء</strong>
                                <small class="d-block text-muted">إضافة فهارس ومفاتيح خارجية</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-database me-2"></i>حالة قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <?php
                    // فحص الجداول الأساسية
                    $tables = [
                        'users' => 'المستخدمين',
                        'courses' => 'الكورسات',
                        'categories' => 'الفئات',
                        'sessions' => 'الجلسات',
                        'course_enrollments' => 'التسجيلات',
                        'join_requests' => 'طلبات الانضمام',
                        'meals' => 'الوجبات',
                        'meal_categories' => 'فئات الوجبات',
                        'payments' => 'المدفوعات',
                        'activity_logs' => 'سجل الأنشطة'
                    ];
                    
                    foreach ($tables as $table => $name) {
                        try {
                            $count = $dbClean->count($table);
                            echo "<div class='d-flex justify-content-between align-items-center mb-2'>";
                            echo "<span><i class='fas fa-table text-success me-2'></i>$name</span>";
                            echo "<span class='badge bg-success'>$count سجل</span>";
                            echo "</div>";
                        } catch (Exception $e) {
                            echo "<div class='d-flex justify-content-between align-items-center mb-2'>";
                            echo "<span><i class='fas fa-exclamation-triangle text-warning me-2'></i>$name</span>";
                            echo "<span class='badge bg-warning'>غير موجود</span>";
                            echo "</div>";
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <!-- الواجهات المتاحة -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-desktop me-2"></i>الواجهات المتاحة للاختبار</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="analytics.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-chart-line me-2"></i>
                                التحليلات والإحصائيات
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="join-requests.php" class="btn btn-outline-success w-100">
                                <i class="fas fa-user-plus me-2"></i>
                                طلبات الانضمام
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="manage-users.php" class="btn btn-outline-info w-100">
                                <i class="fas fa-users me-2"></i>
                                إدارة المستخدمين
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="manage-courses.php" class="btn btn-outline-warning w-100">
                                <i class="fas fa-book me-2"></i>
                                إدارة الكورسات
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="manage-meals.php" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-utensils me-2"></i>
                                إدارة الوجبات
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="system-settings.php" class="btn btn-outline-dark w-100">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات النظام
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="activity-logs.php" class="btn btn-outline-danger w-100">
                                <i class="fas fa-history me-2"></i>
                                سجل الأنشطة
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="dashboard.php" class="btn btn-primary w-100">
                                <i class="fas fa-home me-2"></i>
                                لوحة التحكم الرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ملاحظات مهمة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>ملاحظات مهمة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات النظام:</h6>
                        <ul class="mb-0">
                            <li>تم استخدام <code>database_manager_clean.php</code> كنظام إدارة قاعدة البيانات الموحد</li>
                            <li>جميع الواجهات تستخدم البيانات الحية من قاعدة البيانات</li>
                            <li>تم إضافة معالجة شاملة للأخطاء في جميع الصفحات</li>
                            <li>تم تحسين الأمان بإضافة التحقق من الصلاحيات</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>الميزات الجديدة:</h6>
                        <ul class="mb-0">
                            <li>نظام طلبات الانضمام مع إمكانية الموافقة والرفض</li>
                            <li>نظام الوجبات الصحية مع المعلومات الغذائية</li>
                            <li>تحليلات وإحصائيات شاملة للنظام</li>
                            <li>نظام المدفوعات مع حساب العمولات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 24px;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}
</style>

<?php include 'includes/footer.php'; ?>
