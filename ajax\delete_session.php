<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['session_id']) || !is_numeric($input['session_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الجلسة مطلوب']);
    exit;
}

$session_id = (int)$input['session_id'];

try {
    // التحقق من أن الجلسة تخص المدرب الحالي
    $stmt = $conn->prepare("
        SELECT s.id, s.title 
        FROM sessions s 
        JOIN courses c ON s.course_id = c.id 
        WHERE s.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$session_id, $_SESSION['user_id']]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'الجلسة غير موجودة أو لا تملك صلاحية لحذفها']);
        exit;
    }
    
    // حذف الجلسة
    $stmt = $conn->prepare("DELETE FROM sessions WHERE id = ?");
    $stmt->execute([$session_id]);
    
    // تسجيل النشاط
    try {
        $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, details, created_at) VALUES (?, ?, ?, NOW())");
        $stmt->execute([
            $_SESSION['user_id'], 
            'حذف جلسة', 
            "تم حذف الجلسة: " . $session['title']
        ]);
    } catch (PDOException $e) {
        // تجاهل أخطاء تسجيل النشاط
        error_log("Activity log error: " . $e->getMessage());
    }
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم حذف الجلسة بنجاح'
    ]);
    
} catch (PDOException $e) {
    error_log("Delete session error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ أثناء حذف الجلسة'
    ]);
}
?>
