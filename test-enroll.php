<?php
require_once 'config/database.php';
require_once 'includes/session_config.php';

echo "<h2>تسجيل في كورس</h2>";

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ لم يتم تسجيل الدخول</p>";
    echo "<p><a href='login.php'>تسجيل الدخول</a></p>";
    exit;
}

$student_id = $_SESSION['user_id'];
$course_id = $_GET['course_id'] ?? null;

if (!$course_id) {
    echo "<p style='color: red;'>❌ معرف الكورس مطلوب</p>";
    echo "<p><a href='check-student-enrollments.php'>العودة</a></p>";
    exit;
}

try {
    // جلب بيانات الكورس
    $stmt = $conn->prepare("
        SELECT c.*, u.username as instructor_name
        FROM courses c
        INNER JOIN users u ON c.instructor_id = u.id
        WHERE c.id = ?
    ");
    $stmt->execute([$course_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        echo "<p style='color: red;'>❌ الكورس غير موجود</p>";
        echo "<p><a href='check-student-enrollments.php'>العودة</a></p>";
        exit;
    }
    
    echo "<h3>بيانات الكورس:</h3>";
    echo "<ul>";
    echo "<li><strong>العنوان:</strong> " . htmlspecialchars($course['title']) . "</li>";
    echo "<li><strong>المدرب:</strong> " . htmlspecialchars($course['instructor_name']) . "</li>";
    echo "<li><strong>الوصف:</strong> " . htmlspecialchars($course['description'] ?? 'لا يوجد وصف') . "</li>";
    echo "<li><strong>الحالة:</strong> " . htmlspecialchars($course['status']) . "</li>";
    echo "</ul>";
    
    // التحقق من التسجيل السابق
    $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE student_id = ? AND course_id = ?");
    $stmt->execute([$student_id, $course_id]);
    $existing_enrollment = $stmt->fetch();
    
    if ($existing_enrollment) {
        echo "<p style='color: orange;'>⚠️ أنت مسجل في هذا الكورس بالفعل</p>";
        echo "<p><a href='student/courses.php'>عرض كورساتي</a></p>";
        exit;
    }
    
    // معالجة طلب التسجيل
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['enroll'])) {
        try {
            $stmt = $conn->prepare("
                INSERT INTO course_enrollments (course_id, student_id, status, progress, enrolled_at) 
                VALUES (?, ?, 'active', 0.00, NOW())
            ");
            $stmt->execute([$course_id, $student_id]);
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>✅ تم التسجيل بنجاح!</h4>";
            echo "<p>تم تسجيلك في الكورس: <strong>" . htmlspecialchars($course['title']) . "</strong></p>";
            echo "<p><a href='student/courses.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>عرض كورساتي</a></p>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في التسجيل: " . $e->getMessage() . "</p>";
        }
    } else {
        // عرض نموذج التسجيل
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>تأكيد التسجيل</h4>";
        echo "<p>هل تريد التسجيل في هذا الكورس؟</p>";
        echo "<form method='POST'>";
        echo "<button type='submit' name='enroll' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;'>نعم، سجلني</button>";
        echo "<a href='check-student-enrollments.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إلغاء</a>";
        echo "</form>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
