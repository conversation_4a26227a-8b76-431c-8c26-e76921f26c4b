<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'فيديوهات الجلسات';
$breadcrumbs = [
    ['title' => 'فيديوهات الجلسات']
];

// التحقق من رسالة النجاح
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $success_message = 'تم رفع الفيديو بنجاح!';
}

// معالجة رفع الفيديو
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'upload_video') {
        $session_id = $_POST['session_id'] ?? 0;
        $video_title = trim($_POST['video_title'] ?? '');
        $video_description = trim($_POST['video_description'] ?? '');

        $errors = [];

        // التحقق من البيانات
        if (empty($session_id)) {
            $errors[] = 'يجب اختيار جلسة';
        }

        if (empty($video_title)) {
            $errors[] = 'عنوان الفيديو مطلوب';
        }

        // التحقق من الملف المرفوع
        if (!isset($_FILES['video_file']) || $_FILES['video_file']['error'] !== UPLOAD_ERR_OK) {
            if (isset($_FILES['video_file'])) {
                switch ($_FILES['video_file']['error']) {
                    case UPLOAD_ERR_INI_SIZE:
                    case UPLOAD_ERR_FORM_SIZE:
                        $errors[] = 'حجم الملف كبير جداً';
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        $errors[] = 'تم رفع جزء من الملف فقط';
                        break;
                    case UPLOAD_ERR_NO_FILE:
                        $errors[] = 'لم يتم اختيار ملف';
                        break;
                    default:
                        $errors[] = 'حدث خطأ أثناء رفع الملف';
                }
            } else {
                $errors[] = 'يجب اختيار ملف فيديو';
            }
        } else {
            $file = $_FILES['video_file'];
            $allowed_extensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
            $max_size = 500 * 1024 * 1024; // 500 MB

            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

            if (!in_array($file_extension, $allowed_extensions)) {
                $errors[] = 'نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', $allowed_extensions);
            }

            if ($file['size'] > $max_size) {
                $errors[] = 'حجم الملف كبير جداً. الحد الأقصى 500 ميجابايت. حجم الملف الحالي: ' . round($file['size'] / (1024*1024), 2) . ' ميجابايت';
            }
        }

        if (empty($errors)) {
            try {
                // التحقق من أن الجلسة تنتمي للمدرب
                $stmt = $conn->prepare("
                    SELECT s.id, s.title, c.title as course_title
                    FROM sessions s
                    INNER JOIN courses c ON s.course_id = c.id
                    WHERE s.id = ? AND c.instructor_id = ?
                ");
                $stmt->execute([$session_id, $_SESSION['user_id']]);
                $session = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($session) {
                    // التأكد من وجود مجلد الرفع
                    $upload_dir = '../uploads/session_videos/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    // إنشاء اسم ملف فريد
                    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                    $file_name = 'session_' . $session_id . '_' . time() . '.' . $file_extension;
                    $upload_path = $upload_dir . $file_name;

                    // رفع الملف
                    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                        // التحقق من وجود حقول الفيديو في جدول الجلسات
                        $stmt = $conn->query("DESCRIBE sessions");
                        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

                        if (in_array('has_video', $columns)) {
                            // تحديث قاعدة البيانات مع حقول الفيديو
                            $stmt = $conn->prepare("
                                UPDATE sessions
                                SET video_file_path = ?,
                                    video_title = ?,
                                    video_description = ?,
                                    video_size = ?,
                                    has_video = 1,
                                    video_uploaded_at = NOW()
                                WHERE id = ?
                            ");
                            $stmt->execute([
                                'uploads/session_videos/' . $file_name,
                                $video_title,
                                $video_description,
                                $file['size'],
                                $session_id
                            ]);
                        } else {
                            // إضافة حقول الفيديو أولاً
                            $conn->exec("ALTER TABLE sessions ADD COLUMN IF NOT EXISTS video_file_path VARCHAR(500) DEFAULT NULL");
                            $conn->exec("ALTER TABLE sessions ADD COLUMN IF NOT EXISTS video_title VARCHAR(255) DEFAULT NULL");
                            $conn->exec("ALTER TABLE sessions ADD COLUMN IF NOT EXISTS video_description TEXT DEFAULT NULL");
                            $conn->exec("ALTER TABLE sessions ADD COLUMN IF NOT EXISTS video_size BIGINT DEFAULT NULL");
                            $conn->exec("ALTER TABLE sessions ADD COLUMN IF NOT EXISTS has_video TINYINT(1) DEFAULT 0");
                            $conn->exec("ALTER TABLE sessions ADD COLUMN IF NOT EXISTS video_uploaded_at TIMESTAMP NULL");

                            // ثم تحديث البيانات
                            $stmt = $conn->prepare("
                                UPDATE sessions
                                SET video_file_path = ?,
                                    video_title = ?,
                                    video_description = ?,
                                    video_size = ?,
                                    has_video = 1,
                                    video_uploaded_at = NOW()
                                WHERE id = ?
                            ");
                            $stmt->execute([
                                'uploads/session_videos/' . $file_name,
                                $video_title,
                                $video_description,
                                $file['size'],
                                $session_id
                            ]);
                        }

                        $success_message = 'تم رفع الفيديو بنجاح! اسم الملف: ' . $file_name;

                        // إعادة توجيه لتجنب إعادة الإرسال
                        header('Location: session-videos.php?success=1');
                        exit;
                    } else {
                        $error_message = 'فشل في رفع الملف. تأكد من صلاحيات المجلد.';
                    }
                } else {
                    $error_message = 'الجلسة غير موجودة أو لا تملك صلاحية للوصول إليها';
                }
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage();
            } catch (Exception $e) {
                $error_message = 'حدث خطأ غير متوقع: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'delete_video') {
        $session_id = $_POST['session_id'] ?? 0;
        
        try {
            // جلب معلومات الفيديو
            $stmt = $conn->prepare("
                SELECT s.video_file_path 
                FROM sessions s 
                INNER JOIN courses c ON s.course_id = c.id 
                WHERE s.id = ? AND c.instructor_id = ?
            ");
            $stmt->execute([$session_id, $_SESSION['user_id']]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($session && $session['video_file_path']) {
                // حذف الملف من الخادم
                $file_path = '../' . $session['video_file_path'];
                if (file_exists($file_path)) {
                    unlink($file_path);
                }
                
                // تحديث قاعدة البيانات
                $stmt = $conn->prepare("
                    UPDATE sessions 
                    SET video_file_path = NULL, 
                        video_title = NULL, 
                        video_description = NULL, 
                        video_size = NULL, 
                        video_duration = NULL, 
                        has_video = 0, 
                        video_uploaded_at = NULL 
                    WHERE id = ?
                ");
                $stmt->execute([$session_id]);
                
                $success_message = 'تم حذف الفيديو بنجاح';
            } else {
                $error_message = 'الفيديو غير موجود';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء حذف الفيديو';
        }
    }
}

// جلب الكورسات والجلسات
try {
    // التحقق من وجود الأعمدة الجديدة في جدول الجلسات
    $stmt = $conn->query("DESCRIBE sessions");
    $session_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    $has_video_fields = in_array('has_video', $session_columns) &&
                       in_array('video_title', $session_columns) &&
                       in_array('video_description', $session_columns) &&
                       in_array('video_file_path', $session_columns) &&
                       in_array('video_size', $session_columns) &&
                       in_array('video_uploaded_at', $session_columns);

    if ($has_video_fields) {
        // استعلام مع حقول الفيديو
        $stmt = $conn->prepare("
            SELECT
                c.id as course_id,
                c.title as course_title,
                s.id as session_id,
                s.title as session_title,
                s.description as session_description,
                s.start_time,
                s.duration,
                s.status,
                s.has_video,
                s.video_title,
                s.video_description,
                s.video_file_path,
                s.video_size,
                s.video_uploaded_at
            FROM courses c
            LEFT JOIN sessions s ON c.id = s.course_id
            WHERE c.instructor_id = ? AND c.status = 'active'
            ORDER BY c.title, s.start_time
        ");
    } else {
        // استعلام بدون حقول الفيديو
        $stmt = $conn->prepare("
            SELECT
                c.id as course_id,
                c.title as course_title,
                s.id as session_id,
                s.title as session_title,
                s.description as session_description,
                s.start_time,
                s.duration,
                s.status,
                0 as has_video,
                NULL as video_title,
                NULL as video_description,
                NULL as video_file_path,
                NULL as video_size,
                NULL as video_uploaded_at
            FROM courses c
            LEFT JOIN sessions s ON c.id = s.course_id
            WHERE c.instructor_id = ? AND c.status = 'active'
            ORDER BY c.title, s.start_time
        ");
    }

    $stmt->execute([$_SESSION['user_id']]);
    $sessions_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنظيم البيانات حسب الكورس
    $courses_sessions = [];
    foreach ($sessions_data as $row) {
        if ($row['session_id']) {
            $courses_sessions[$row['course_id']]['course_title'] = $row['course_title'];
            $courses_sessions[$row['course_id']]['sessions'][] = $row;
        }
    }

    // إضافة تحذير إذا لم تكن حقول الفيديو موجودة
    if (!$has_video_fields) {
        $warning_message = 'تحتاج إلى تحديث جدول الجلسات لدعم الفيديوهات. <a href="../update_sessions_table.php" class="alert-link">انقر هنا للتحديث</a>';
    }

} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
    $courses_sessions = [];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-video text-primary me-2"></i>
            فيديوهات الجلسات
        </h2>
        <p class="text-muted mb-0">رفع وإدارة فيديوهات الجلسات التعليمية</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadVideoModal">
            <i class="fas fa-upload me-1"></i>رفع فيديو جلسة
        </button>
        <a href="videos.php" class="btn btn-outline-secondary">
            <i class="fas fa-video me-1"></i>فيديوهات الكورسات
        </a>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($warning_message)): ?>
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $warning_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!empty($errors)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <ul class="mb-0">
        <?php foreach ($errors as $error): ?>
        <li><?php echo htmlspecialchars($error); ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- قائمة الكورسات والجلسات -->
<?php if (empty($courses_sessions)): ?>
<div class="card border-0 shadow-sm">
    <div class="card-body text-center py-5">
        <i class="fas fa-video text-muted" style="font-size: 3rem;"></i>
        <h5 class="mt-3 text-muted">لا توجد جلسات</h5>
        <p class="text-muted">ابدأ بإنشاء جلسات للكورسات ثم ارفع فيديوهات لها</p>
        <a href="sessions.php" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>إنشاء جلسة جديدة
        </a>
    </div>
</div>
<?php else: ?>
<?php foreach ($courses_sessions as $course_id => $course_data): ?>
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-graduation-cap me-2"></i>
            <?php echo htmlspecialchars($course_data['course_title']); ?>
        </h5>
    </div>
    <div class="card-body p-0">
        <?php foreach ($course_data['sessions'] as $session): ?>
        <div class="session-item border-bottom p-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-1"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                    <p class="text-muted mb-1 small">
                        <?php echo htmlspecialchars($session['session_description']); ?>
                    </p>
                    <div class="d-flex gap-3 small text-muted">
                        <span><i class="fas fa-calendar me-1"></i><?php echo date('Y-m-d H:i', strtotime($session['start_time'])); ?></span>
                        <span><i class="fas fa-clock me-1"></i><?php echo $session['duration']; ?> دقيقة</span>
                        <span class="badge bg-<?php echo $session['status'] === 'completed' ? 'success' : ($session['status'] === 'cancelled' ? 'danger' : 'warning'); ?>">
                            <?php
                            echo $session['status'] === 'completed' ? 'مكتملة' :
                                ($session['status'] === 'cancelled' ? 'ملغية' : 'مجدولة');
                            ?>
                        </span>
                    </div>
                </div>

                <div class="col-md-3">
                    <?php if ($session['has_video']): ?>
                    <div class="text-success">
                        <i class="fas fa-video me-1"></i>
                        <strong><?php echo htmlspecialchars($session['video_title']); ?></strong>
                        <br>
                        <small class="text-muted">
                            حجم الملف: <?php echo number_format($session['video_size'] / (1024*1024), 2); ?> ميجابايت
                            <br>
                            رُفع في: <?php echo date('Y-m-d H:i', strtotime($session['video_uploaded_at'])); ?>
                        </small>
                    </div>
                    <?php else: ?>
                    <div class="text-muted">
                        <i class="fas fa-video-slash me-1"></i>
                        لا يوجد فيديو
                    </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-3">
                    <div class="btn-group w-100" role="group">
                        <?php if ($session['has_video']): ?>
                        <button class="btn btn-sm btn-outline-primary"
                                onclick="previewVideo('<?php echo $session['session_id']; ?>')" title="معاينة">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger"
                                onclick="deleteVideo(<?php echo $session['session_id']; ?>)" title="حذف الفيديو">
                            <i class="fas fa-trash"></i>
                        </button>
                        <?php else: ?>
                        <button class="btn btn-sm btn-primary w-100"
                                onclick="uploadVideo(<?php echo $session['session_id']; ?>, '<?php echo htmlspecialchars($session['session_title']); ?>')">
                            <i class="fas fa-upload me-1"></i>رفع فيديو
                        </button>
                        <button class="btn btn-sm btn-success w-100 mt-1"
                                onclick="quickUpload(<?php echo $session['session_id']; ?>, '<?php echo htmlspecialchars($session['session_title']); ?>')">
                            <i class="fas fa-bolt me-1"></i>رفع سريع
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endforeach; ?>
<?php endif; ?>

<!-- Modal رفع فيديو -->
<div class="modal fade" id="uploadVideoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفع فيديو للجلسة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="uploadForm">
                <input type="hidden" name="action" value="upload_video">
                <input type="hidden" name="session_id" id="upload_session_id">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>الجلسة المختارة:</strong> <span id="selected_session_title"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <label for="video_title" class="form-label">عنوان الفيديو <span class="text-danger">*</span></label>
                            <input type="text" name="video_title" id="video_title" class="form-control" required>
                        </div>
                        <div class="col-12">
                            <label for="video_description" class="form-label">وصف الفيديو</label>
                            <textarea name="video_description" id="video_description" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="col-12">
                            <label for="video_file" class="form-label">ملف الفيديو <span class="text-danger">*</span></label>
                            <input type="file" name="video_file" id="video_file" class="form-control"
                                   accept="video/*" required onchange="validateFile()">
                            <div class="form-text">
                                <i class="fas fa-info-circle text-info me-1"></i>
                                الأنواع المدعومة: MP4, AVI, MOV, WMV, FLV, WebM | الحد الأقصى: 500 ميجابايت
                            </div>
                            <div id="file_info" class="mt-2"></div>
                        </div>
                        <div class="col-12">
                            <div class="progress" id="upload_progress" style="display: none;">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="upload_btn">
                        <i class="fas fa-upload me-1"></i>رفع الفيديو
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal رفع سريع -->
<div class="modal fade" id="quickUploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفع سريع للفيديو</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="quickUploadForm">
                <input type="hidden" name="action" value="upload_video">
                <input type="hidden" name="session_id" id="quick_session_id">
                <input type="hidden" name="video_title" id="quick_video_title">
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-bolt me-2"></i>
                        <strong>رفع سريع للجلسة:</strong> <span id="quick_session_title"></span>
                    </div>
                    <div class="mb-3">
                        <label for="quick_video_file" class="form-label">اختر ملف الفيديو <span class="text-danger">*</span></label>
                        <input type="file" name="video_file" id="quick_video_file" class="form-control"
                               accept="video/*" required onchange="validateQuickFile()">
                        <div class="form-text">
                            سيتم استخدام اسم الجلسة كعنوان للفيديو تلقائياً
                        </div>
                        <div id="quick_file_info" class="mt-2"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success" id="quick_upload_btn">
                        <i class="fas fa-bolt me-1"></i>رفع سريع
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal معاينة الفيديو -->
<div class="modal fade" id="previewVideoModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة الفيديو</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="videoPreviewContainer" class="text-center">
                    <!-- سيتم إدراج الفيديو هنا -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// رفع فيديو لجلسة محددة
function uploadVideo(sessionId, sessionTitle) {
    document.getElementById('upload_session_id').value = sessionId;
    document.getElementById('selected_session_title').textContent = sessionTitle;
    document.getElementById('video_title').value = 'فيديو جلسة: ' + sessionTitle;

    // إعادة تعيين النموذج
    document.getElementById('uploadForm').reset();
    document.getElementById('upload_session_id').value = sessionId;
    document.getElementById('video_title').value = 'فيديو جلسة: ' + sessionTitle;
    document.getElementById('file_info').innerHTML = '';

    // إظهار النموذج
    new bootstrap.Modal(document.getElementById('uploadVideoModal')).show();
}

// رفع سريع للفيديو
function quickUpload(sessionId, sessionTitle) {
    document.getElementById('quick_session_id').value = sessionId;
    document.getElementById('quick_video_title').value = 'فيديو جلسة: ' + sessionTitle;
    document.getElementById('quick_session_title').textContent = sessionTitle;

    // إعادة تعيين النموذج
    document.getElementById('quickUploadForm').reset();
    document.getElementById('quick_session_id').value = sessionId;
    document.getElementById('quick_video_title').value = 'فيديو جلسة: ' + sessionTitle;
    document.getElementById('quick_file_info').innerHTML = '';

    // إظهار النموذج
    new bootstrap.Modal(document.getElementById('quickUploadModal')).show();
}

// التحقق من صحة الملف
function validateFile() {
    const fileInput = document.getElementById('video_file');
    const fileInfo = document.getElementById('file_info');
    const uploadBtn = document.getElementById('upload_btn');

    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        const maxSize = 500 * 1024 * 1024; // 500 MB
        const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/x-flv', 'video/webm'];

        let isValid = true;
        let info = '<div class="alert alert-info small">';

        // معلومات الملف
        info += '<strong>اسم الملف:</strong> ' + file.name + '<br>';
        info += '<strong>الحجم:</strong> ' + (file.size / (1024*1024)).toFixed(2) + ' ميجابايت<br>';
        info += '<strong>النوع:</strong> ' + file.type + '<br>';

        // التحقق من النوع
        if (!allowedTypes.includes(file.type)) {
            info += '<span class="text-danger"><i class="fas fa-times me-1"></i>نوع الملف غير مدعوم</span><br>';
            isValid = false;
        } else {
            info += '<span class="text-success"><i class="fas fa-check me-1"></i>نوع الملف مدعوم</span><br>';
        }

        // التحقق من الحجم
        if (file.size > maxSize) {
            info += '<span class="text-danger"><i class="fas fa-times me-1"></i>حجم الملف كبير جداً</span>';
            isValid = false;
        } else {
            info += '<span class="text-success"><i class="fas fa-check me-1"></i>حجم الملف مناسب</span>';
        }

        info += '</div>';
        fileInfo.innerHTML = info;
        uploadBtn.disabled = !isValid;
    }
}

// معاينة الفيديو
function previewVideo(sessionId) {
    const container = document.getElementById('videoPreviewContainer');
    container.innerHTML = '<div class="spinner-border" role="status"><span class="visually-hidden">جاري التحميل...</span></div>';

    // جلب معلومات الفيديو
    fetch(`../ajax/get_session_video.php?session_id=${sessionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.video_url) {
                container.innerHTML = `
                    <video width="100%" height="500" controls>
                        <source src="${data.video_url}" type="video/mp4">
                        المتصفح لا يدعم تشغيل الفيديو
                    </video>
                    <div class="mt-3">
                        <h6>${data.video_title}</h6>
                        <p class="text-muted">${data.video_description || ''}</p>
                    </div>
                `;
            } else {
                container.innerHTML = '<div class="alert alert-danger">فشل في تحميل الفيديو</div>';
            }
        })
        .catch(error => {
            container.innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء تحميل الفيديو</div>';
        });

    new bootstrap.Modal(document.getElementById('previewVideoModal')).show();
}

// حذف فيديو
function deleteVideo(sessionId) {
    if (confirm('هل أنت متأكد من حذف هذا الفيديو؟ لا يمكن التراجع عن هذا الإجراء.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_video">
            <input type="hidden" name="session_id" value="${sessionId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// تقدم الرفع
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    const uploadBtn = document.getElementById('upload_btn');
    const progress = document.getElementById('upload_progress');

    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الرفع...';
    progress.style.display = 'block';

    // محاكاة تقدم الرفع (يمكن تحسينها باستخدام AJAX)
    let width = 0;
    const interval = setInterval(() => {
        width += Math.random() * 10;
        if (width >= 90) {
            clearInterval(interval);
        }
        progress.querySelector('.progress-bar').style.width = Math.min(width, 90) + '%';
    }, 200);
});
</script>

<?php include 'includes/footer.php'; ?>
