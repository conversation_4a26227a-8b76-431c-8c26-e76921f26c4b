<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// تم حذف التعريف المكرر - الدالة موجودة في simple_db.php

$error = null;
$success = null;

// جلب التخصصات المتاحة
$specializations = [];
try {
    $stmt = $conn->query("SELECT name FROM instructor_specializations WHERE is_active = TRUE ORDER BY name");
    $specializations = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    $specializations = ['البرمجة وتطوير المواقع', 'التصميم الجرافيكي', 'إدارة الأعمال', 'التسويق الرقمي', 'الذكاء الاصطناعي'];
}

// متغيرات النموذج
$form_data = [
    'name' => '', 'email' => '', 'phone' => '', 'bio' => '',
    'specialization' => '', 'experience_years' => '', 'education_level' => '',
    'university' => '', 'major' => '', 'certifications' => '', 'skills' => '',
    'languages' => '', 'hourly_rate' => '',
    'availability' => 'flexible', 'teaching_style' => '', 'preferred_subjects' => ''
];

// معالجة إضافة مدرب جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // جمع البيانات من النموذج
    foreach ($form_data as $key => $default) {
        $form_data[$key] = trim($_POST[$key] ?? $default);
    }
    $password = $_POST['password'] ?? '';

    // التحقق من المدخلات الأساسية
    if (empty($form_data['name']) || empty($form_data['email']) || empty($password)) {
        $error = 'الحقول الأساسية (الاسم، البريد الإلكتروني، كلمة المرور) مطلوبة';
    } elseif (!filter_var($form_data['email'], FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صالح';
    } elseif (strlen($password) < 6) {
        $error = 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';
    } else {
        try {
            // التحقق من عدم وجود البريد الإلكتروني مسبقاً
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$form_data['email']]);

            if ($stmt->rowCount() > 0) {
                $error = 'البريد الإلكتروني مستخدم بالفعل';
            } else {
                // إنشاء حساب المدرب مع البيانات الأساسية
                $instructor_data = [
                    'name' => $form_data['name'],
                    'email' => $form_data['email'],
                    'password' => password_hash($password, PASSWORD_DEFAULT),
                    'role' => 'instructor',
                    'status' => 'active',
                    'phone' => $form_data['phone'],
                    'bio' => $form_data['bio'],
                    'specialization' => $form_data['specialization'],
                    'email_verified' => 1
                ];

                // إضافة الأعمدة الإضافية إذا كانت موجودة
                if (columnExists('users', 'experience_years')) {
                    $instructor_data['experience_years'] = $form_data['experience_years'] ?: null;
                }
                if (columnExists('users', 'education_level')) {
                    $instructor_data['education_level'] = $form_data['education_level'] ?: null;
                }
                if (columnExists('users', 'university')) {
                    $instructor_data['university'] = $form_data['university'];
                }
                if (columnExists('users', 'major')) {
                    $instructor_data['major'] = $form_data['major'];
                }
                if (columnExists('users', 'certifications')) {
                    $instructor_data['certifications'] = $form_data['certifications'];
                }
                if (columnExists('users', 'skills')) {
                    $instructor_data['skills'] = $form_data['skills'];
                }
                if (columnExists('users', 'languages')) {
                    $instructor_data['languages'] = $form_data['languages'];
                }
                if (columnExists('users', 'hourly_rate')) {
                    $instructor_data['hourly_rate'] = $form_data['hourly_rate'] ?: null;
                }
                if (columnExists('users', 'availability')) {
                    $instructor_data['availability'] = $form_data['availability'];
                }
                if (columnExists('users', 'teaching_style')) {
                    $instructor_data['teaching_style'] = $form_data['teaching_style'];
                }
                if (columnExists('users', 'preferred_subjects')) {
                    $instructor_data['preferred_subjects'] = $form_data['preferred_subjects'];
                }

                $instructor_id = insertRecord('users', $instructor_data);
                if ($instructor_id) {
                    $success = 'تم إضافة المدرب بنجاح مع جميع البيانات';

                    // تسجيل النشاط
                    logUserActivity($_SESSION['user_id'], 'إضافة مدرب', "تم إضافة مدرب جديد: {$form_data['name']}");

                    // إرسال بريد إلكتروني للمدرب
                    $to = $form_data['email'];
                    $subject = 'مرحباً بك كمدرب في منصة التعلم';
                    $message = "
                        مرحباً {$form_data['name']}،\n\n
                        تم إنشاء حسابك كمدرب في منصة التعلم عن بعد.\n
                        بيانات تسجيل الدخول:\n
                        البريد الإلكتروني: {$form_data['email']}\n
                        كلمة المرور: $password\n\n
                        تخصصك: {$form_data['specialization']}\n
                        سعرك بالساعة: {$form_data['hourly_rate']} ريال\n\n
                        يرجى تسجيل الدخول وإكمال ملفك الشخصي.\n\n
                        مع أطيب التحيات،\n
                        فريق منصة التعلم
                    ";

                    $headers = 'From: <EMAIL>' . "\r\n" .
                        'Reply-To: <EMAIL>' . "\r\n" .
                        'X-Mailer: PHP/' . phpversion();

                    // محاولة إرسال البريد الإلكتروني
                    if (!mail($to, $subject, $message, $headers)) {
                        error_log("Failed to send email to instructor: {$form_data['email']}");
                    }

                    // إعادة توجيه إلى صفحة إدارة المدربين
                    header('Location: manage-instructors.php?success=' . urlencode('تم إضافة المدرب بنجاح مع جميع البيانات'));
                    exit;
                } else {
                    $error = 'حدث خطأ أثناء إضافة المدرب';
                }
            }
        } catch (PDOException $e) {
            error_log("Error adding instructor: " . $e->getMessage());
            $error = 'حدث خطأ أثناء إضافة المدرب: ' . $e->getMessage();
        }
    }
}

$pageTitle = 'إضافة مدرب جديد';
require_once '../includes/header.php';
?>

<style>
.form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}
.form-section h6 {
    color: #007bff;
    font-weight: bold;
    margin-bottom: 15px;
}
.card {
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
}
.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    padding: 20px;
}
.btn-generate {
    background: #28a745;
    border: none;
    color: white;
    transition: all 0.3s;
}
.btn-generate:hover {
    background: #218838;
    transform: translateY(-2px);
}
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}
.progress-bar {
    background: linear-gradient(90deg, #007bff, #28a745);
}
</style>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header text-center">
                    <h4 class="mb-0"><i class="fas fa-user-plus me-2"></i>إضافة مدرب جديد</h4>
                    <p class="mb-0 mt-2">املأ جميع البيانات لإنشاء ملف شخصي متكامل للمدرب</p>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- شريط التقدم -->
                    <div class="mb-4">
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>
                        </div>
                        <small class="text-muted">تقدم ملء النموذج: <span id="progressText">0%</span></small>
                    </div>

                    <form method="POST" class="needs-validation" novalidate id="instructorForm">

                        <!-- البيانات الأساسية -->
                        <div class="form-section">
                            <h6><i class="fas fa-user me-2"></i>البيانات الأساسية</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="name" name="name" required
                                           value="<?php echo htmlspecialchars($form_data['name']); ?>"
                                           placeholder="مثال: د. أحمد محمد علي">
                                    <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" id="email" name="email" required
                                           value="<?php echo htmlspecialchars($form_data['email']); ?>"
                                           placeholder="<EMAIL>">
                                    <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صالح</div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($form_data['phone']); ?>"
                                           placeholder="05xxxxxxxx">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">كلمة المرور *</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="password" name="password" required
                                               minlength="6" placeholder="كلمة مرور قوية">
                                        <button type="button" class="btn btn-generate" onclick="generatePassword()">
                                            <i class="fas fa-magic me-1"></i>توليد
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                                </div>
                            </div>

                        </div>

                        <!-- التخصص والخبرة -->
                        <div class="form-section">
                            <h6><i class="fas fa-graduation-cap me-2"></i>التخصص والخبرة</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="specialization" class="form-label">التخصص الرئيسي</label>
                                    <select class="form-select" id="specialization" name="specialization">
                                        <option value="">اختر التخصص</option>
                                        <?php foreach ($specializations as $spec): ?>
                                            <option value="<?php echo htmlspecialchars($spec); ?>"
                                                    <?php echo $form_data['specialization'] === $spec ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($spec); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="experience_years" class="form-label">سنوات الخبرة</label>
                                    <input type="number" class="form-control" id="experience_years" name="experience_years"
                                           value="<?php echo htmlspecialchars($form_data['experience_years']); ?>"
                                           min="0" max="50" placeholder="عدد السنوات">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="skills" class="form-label">المهارات</label>
                                <textarea class="form-control" id="skills" name="skills" rows="2"
                                          placeholder="مثال: PHP, JavaScript, Python, MySQL"><?php echo htmlspecialchars($form_data['skills']); ?></textarea>
                                <small class="text-muted">اكتب المهارات مفصولة بفواصل</small>
                            </div>
                        <!-- التعليم والمؤهلات -->
                        <div class="form-section">
                            <h6><i class="fas fa-university me-2"></i>التعليم والمؤهلات</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="education_level" class="form-label">المستوى التعليمي</label>
                                    <select class="form-select" id="education_level" name="education_level">
                                        <option value="">اختر المستوى</option>
                                        <option value="diploma" <?php echo $form_data['education_level'] === 'diploma' ? 'selected' : ''; ?>>دبلوم</option>
                                        <option value="bachelor" <?php echo $form_data['education_level'] === 'bachelor' ? 'selected' : ''; ?>>بكالوريوس</option>
                                        <option value="master" <?php echo $form_data['education_level'] === 'master' ? 'selected' : ''; ?>>ماجستير</option>
                                        <option value="phd" <?php echo $form_data['education_level'] === 'phd' ? 'selected' : ''; ?>>دكتوراه</option>
                                        <option value="certificate" <?php echo $form_data['education_level'] === 'certificate' ? 'selected' : ''; ?>>شهادة مهنية</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="university" class="form-label">الجامعة</label>
                                    <input type="text" class="form-control" id="university" name="university"
                                           value="<?php echo htmlspecialchars($form_data['university']); ?>"
                                           placeholder="مثال: جامعة الملك سعود">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="major" class="form-label">التخصص الأكاديمي</label>
                                    <input type="text" class="form-control" id="major" name="major"
                                           value="<?php echo htmlspecialchars($form_data['major']); ?>"
                                           placeholder="مثال: علوم الحاسوب">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="languages" class="form-label">اللغات</label>
                                    <input type="text" class="form-control" id="languages" name="languages"
                                           value="<?php echo htmlspecialchars($form_data['languages']); ?>"
                                           placeholder="مثال: العربية، الإنجليزية، الفرنسية">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="certifications" class="form-label">الشهادات والدورات</label>
                                <textarea class="form-control" id="certifications" name="certifications" rows="2"
                                          placeholder="اذكر الشهادات المهنية والدورات التدريبية"><?php echo htmlspecialchars($form_data['certifications']); ?></textarea>
                            </div>
                        </div>

                        <!-- معلومات التدريس -->
                        <div class="form-section">
                            <h6><i class="fas fa-chalkboard-teacher me-2"></i>معلومات التدريس</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="hourly_rate" class="form-label">السعر بالساعة (ريال)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="hourly_rate" name="hourly_rate"
                                               value="<?php echo htmlspecialchars($form_data['hourly_rate']); ?>"
                                               min="0" step="0.01" placeholder="150.00">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="availability" class="form-label">التوفر</label>
                                    <select class="form-select" id="availability" name="availability">
                                        <option value="flexible" <?php echo $form_data['availability'] === 'flexible' ? 'selected' : ''; ?>>مرن</option>
                                        <option value="full_time" <?php echo $form_data['availability'] === 'full_time' ? 'selected' : ''; ?>>دوام كامل</option>
                                        <option value="part_time" <?php echo $form_data['availability'] === 'part_time' ? 'selected' : ''; ?>>دوام جزئي</option>
                                        <option value="weekends" <?php echo $form_data['availability'] === 'weekends' ? 'selected' : ''; ?>>نهاية الأسبوع</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="preferred_subjects" class="form-label">المواد المفضلة للتدريس</label>
                                <textarea class="form-control" id="preferred_subjects" name="preferred_subjects" rows="2"
                                          placeholder="اذكر المواد أو المجالات التي تفضل تدريسها"><?php echo htmlspecialchars($form_data['preferred_subjects']); ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="teaching_style" class="form-label">أسلوب التدريس</label>
                                <textarea class="form-control" id="teaching_style" name="teaching_style" rows="2"
                                          placeholder="اوصف أسلوبك في التدريس والتفاعل مع الطلاب"><?php echo htmlspecialchars($form_data['teaching_style']); ?></textarea>
                            </div>
                        </div>

                        <!-- النبذة الشخصية -->
                        <div class="form-section">
                            <h6><i class="fas fa-user-edit me-2"></i>النبذة الشخصية</h6>
                            <div class="mb-3">
                                <label for="bio" class="form-label">نبذة شخصية</label>
                                <textarea class="form-control" id="bio" name="bio" rows="4"
                                          placeholder="اكتب نبذة مختصرة عن خبراتك وإنجازاتك المهنية"><?php echo htmlspecialchars($form_data['bio']); ?></textarea>
                                <small class="text-muted">هذه النبذة ستظهر في ملفك الشخصي للطلاب</small>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="manage-instructors.php" class="btn btn-secondary btn-lg me-md-2">
                                <i class="fas fa-arrow-left me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>إضافة المدرب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// توليد كلمة مرور قوية
function generatePassword() {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';

    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * charset.length);
        password += charset[randomIndex];
    }

    document.getElementById('password').value = password;
    updateProgress();
}

// تحديث شريط التقدم
function updateProgress() {
    const form = document.getElementById('instructorForm');
    const inputs = form.querySelectorAll('input, select, textarea');
    let filledInputs = 0;

    inputs.forEach(input => {
        if (input.value.trim() !== '') {
            filledInputs++;
        }
    });

    const progress = Math.round((filledInputs / inputs.length) * 100);
    document.getElementById('progressBar').style.width = progress + '%';
    document.getElementById('progressText').textContent = progress + '%';

    // تغيير لون الشريط حسب التقدم
    const progressBar = document.getElementById('progressBar');
    if (progress < 30) {
        progressBar.className = 'progress-bar bg-danger';
    } else if (progress < 70) {
        progressBar.className = 'progress-bar bg-warning';
    } else {
        progressBar.className = 'progress-bar bg-success';
    }
}

// التحقق من صحة النموذج
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('instructorForm');
    const inputs = form.querySelectorAll('input, select, textarea');

    // تحديث التقدم عند تغيير أي حقل
    inputs.forEach(input => {
        input.addEventListener('input', updateProgress);
        input.addEventListener('change', updateProgress);
    });

    // التحقق الأولي
    updateProgress();

    // التحقق من صحة النموذج عند الإرسال
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();

            // التمرير إلى أول حقل خاطئ
            const firstInvalid = form.querySelector(':invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalid.focus();
            }
        }
        form.classList.add('was-validated');
    }, false);

    // تحسين تجربة المستخدم
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
});

// تحسين حقل رقم الهاتف
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 10) {
        value = value.substring(0, 10);
    }
    e.target.value = value;
});

// تحسين حقل السعر
document.getElementById('hourly_rate').addEventListener('input', function(e) {
    let value = parseFloat(e.target.value);
    if (value < 0) {
        e.target.value = 0;
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>