<?php
session_start();
require_once '../includes/database_manager_clean.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// معالجة العمليات
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_status':
            $userId = (int)$_POST['user_id'];
            $newStatus = $_POST['status'];
            
            if ($dbClean->update('users', $userId, ['status' => $newStatus])) {
                $message = 'تم تحديث حالة المستخدم بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في تحديث حالة المستخدم';
                $messageType = 'danger';
            }
            break;
            
        case 'update_role':
            $userId = (int)$_POST['user_id'];
            $newRole = $_POST['role'];
            
            if ($dbClean->update('users', $userId, ['role' => $newRole])) {
                $message = 'تم تحديث دور المستخدم بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في تحديث دور المستخدم';
                $messageType = 'danger';
            }
            break;
            
        case 'delete_user':
            $userId = (int)$_POST['user_id'];
            
            // التحقق من عدم حذف المدير الحالي
            if ($userId != $_SESSION['user_id']) {
                if ($dbClean->delete('users', $userId)) {
                    $message = 'تم حذف المستخدم بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'فشل في حذف المستخدم';
                    $messageType = 'danger';
                }
            } else {
                $message = 'لا يمكن حذف حسابك الخاص';
                $messageType = 'warning';
            }
            break;
    }
}

// جلب البيانات من قاعدة البيانات
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// فلاتر البحث
$search = $_GET['search'] ?? '';
$roleFilter = $_GET['role'] ?? '';
$statusFilter = $_GET['status'] ?? '';

// بناء شروط البحث
$conditions = [];
if (!empty($search)) {
    $users = $dbClean->search('users', $search, ['name', 'email', 'username'], $conditions, '*', $limit);
    $totalUsers = count($users); // تقدير تقريبي
} else {
    if (!empty($roleFilter)) {
        $conditions['role'] = $roleFilter;
    }
    if (!empty($statusFilter)) {
        $conditions['status'] = $statusFilter;
    }
    
    $users = $dbClean->getWhere('users', $conditions, '*', 'created_at DESC', $limit);
    $totalUsers = $dbClean->count('users', $conditions);
}

// إحصائيات المستخدمين
$userStats = [
    'total' => $dbClean->count('users'),
    'admins' => $dbClean->count('users', ['role' => 'admin']),
    'instructors' => $dbClean->count('users', ['role' => 'instructor']),
    'students' => $dbClean->count('users', ['role' => 'student']),
    'active' => $dbClean->count('users', ['status' => 'active']),
    'inactive' => $dbClean->count('users', ['status' => 'inactive']),
    'pending' => $dbClean->count('users', ['status' => 'pending']),
    'suspended' => $dbClean->count('users', ['status' => 'suspended'])
];

$totalPages = ceil($totalUsers / $limit);
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - لوحة التحكم</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card.primary { border-left-color: #667eea; }
        .stats-card.success { border-left-color: #28a745; }
        .stats-card.warning { border-left-color: #ffc107; }
        .stats-card.danger { border-left-color: #dc3545; }
        .stats-card.info { border-left-color: #17a2b8; }
        .stats-card.secondary { border-left-color: #6c757d; }
        
        .data-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem 0.75rem;
        }
        
        .table td {
            padding: 0.75rem;
            vertical-align: middle;
            border-color: #e9ecef;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .btn-action {
            padding: 0.25rem 0.5rem;
            margin: 0 0.125rem;
            border-radius: 5px;
            font-size: 0.875rem;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 0.75rem 1rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .pagination {
            justify-content: center;
            margin-top: 2rem;
        }
        
        .page-link {
            border-radius: 10px;
            margin: 0 0.25rem;
            border: 2px solid #e2e8f0;
            color: #667eea;
        }
        
        .page-link:hover, .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }
        
        .avatar-sm {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-2">
                        <i class="fas fa-users me-3"></i>
                        إدارة المستخدمين
                    </h1>
                    <p class="lead mb-0">إدارة شاملة لجميع مستخدمي النظام مع البيانات الحية</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="dashboard.php" class="btn btn-light btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- رسائل النظام -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات المستخدمين -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card primary">
                    <div class="text-center">
                        <h3 class="text-primary mb-1"><?php echo number_format($userStats['total']); ?></h3>
                        <p class="text-muted mb-0">إجمالي المستخدمين</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card danger">
                    <div class="text-center">
                        <h3 class="text-danger mb-1"><?php echo number_format($userStats['admins']); ?></h3>
                        <p class="text-muted mb-0">المدراء</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card warning">
                    <div class="text-center">
                        <h3 class="text-warning mb-1"><?php echo number_format($userStats['instructors']); ?></h3>
                        <p class="text-muted mb-0">المدربين</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card info">
                    <div class="text-center">
                        <h3 class="text-info mb-1"><?php echo number_format($userStats['students']); ?></h3>
                        <p class="text-muted mb-0">الطلاب</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card success">
                    <div class="text-center">
                        <h3 class="text-success mb-1"><?php echo number_format($userStats['active']); ?></h3>
                        <p class="text-muted mb-0">نشط</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="stats-card secondary">
                    <div class="text-center">
                        <h3 class="text-secondary mb-1"><?php echo number_format($userStats['pending']); ?></h3>
                        <p class="text-muted mb-0">معلق</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="data-card mb-4">
            <h5 class="mb-3">
                <i class="fas fa-search me-2"></i>
                البحث والفلترة
            </h5>
            
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث بالاسم أو البريد الإلكتروني..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">الدور</label>
                    <select name="role" class="form-select">
                        <option value="">جميع الأدوار</option>
                        <option value="admin" <?php echo $roleFilter === 'admin' ? 'selected' : ''; ?>>مدير</option>
                        <option value="instructor" <?php echo $roleFilter === 'instructor' ? 'selected' : ''; ?>>مدرب</option>
                        <option value="student" <?php echo $roleFilter === 'student' ? 'selected' : ''; ?>>طالب</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo $statusFilter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                        <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>معلق</option>
                        <option value="suspended" <?php echo $statusFilter === 'suspended' ? 'selected' : ''; ?>>موقوف</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- جدول المستخدمين -->
        <div class="data-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    قائمة المستخدمين
                </h5>
                <span class="badge bg-primary"><?php echo count($users); ?> من أصل <?php echo number_format($totalUsers); ?></span>
            </div>

            <?php if (!empty($users)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>آخر دخول</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-3">
                                                <?php if (!empty($user['profile_picture'])): ?>
                                                    <img src="<?php echo htmlspecialchars($user['profile_picture']); ?>"
                                                         class="rounded-circle" width="40" height="40" alt="صورة المستخدم">
                                                <?php else: ?>
                                                    <span class="avatar-title bg-primary rounded-circle">
                                                        <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                                                <br>
                                                <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($user['email']); ?>
                                        <?php if ($user['email_verified']): ?>
                                            <i class="fas fa-check-circle text-success ms-1" title="بريد إلكتروني مؤكد"></i>
                                        <?php else: ?>
                                            <i class="fas fa-exclamation-circle text-warning ms-1" title="بريد إلكتروني غير مؤكد"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php
                                            echo $user['role'] === 'admin' ? 'danger' :
                                                ($user['role'] === 'instructor' ? 'warning' : 'info');
                                        ?>">
                                            <?php
                                            $roles = ['admin' => 'مدير', 'instructor' => 'مدرب', 'student' => 'طالب'];
                                            echo $roles[$user['role']] ?? $user['role'];
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php
                                            echo $user['status'] === 'active' ? 'success' :
                                                ($user['status'] === 'pending' ? 'warning' :
                                                ($user['status'] === 'suspended' ? 'danger' : 'secondary'));
                                        ?>">
                                            <?php
                                            $statuses = [
                                                'active' => 'نشط',
                                                'inactive' => 'غير نشط',
                                                'pending' => 'معلق',
                                                'suspended' => 'موقوف'
                                            ];
                                            echo $statuses[$user['status']] ?? $user['status'];
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php
                                            if ($user['last_login']) {
                                                echo date('Y-m-d H:i', strtotime($user['last_login']));
                                            } else {
                                                echo 'لم يدخل بعد';
                                            }
                                            ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- تغيير الحالة -->
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-primary dropdown-toggle btn-action"
                                                        type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><h6 class="dropdown-header">تغيير الحالة</h6></li>
                                                    <?php if ($user['status'] !== 'active'): ?>
                                                    <li>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="action" value="update_status">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <input type="hidden" name="status" value="active">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-check text-success me-2"></i>تفعيل
                                                            </button>
                                                        </form>
                                                    </li>
                                                    <?php endif; ?>

                                                    <?php if ($user['status'] !== 'suspended'): ?>
                                                    <li>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="action" value="update_status">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <input type="hidden" name="status" value="suspended">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-ban text-warning me-2"></i>إيقاف
                                                            </button>
                                                        </form>
                                                    </li>
                                                    <?php endif; ?>

                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><h6 class="dropdown-header">تغيير الدور</h6></li>

                                                    <?php if ($user['role'] !== 'student'): ?>
                                                    <li>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="action" value="update_role">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <input type="hidden" name="role" value="student">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-user-graduate text-info me-2"></i>طالب
                                                            </button>
                                                        </form>
                                                    </li>
                                                    <?php endif; ?>

                                                    <?php if ($user['role'] !== 'instructor'): ?>
                                                    <li>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="action" value="update_role">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <input type="hidden" name="role" value="instructor">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="fas fa-chalkboard-teacher text-warning me-2"></i>مدرب
                                                            </button>
                                                        </form>
                                                    </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>

                                            <!-- عرض التفاصيل -->
                                            <button class="btn btn-sm btn-outline-info btn-action"
                                                    onclick="showUserDetails(<?php echo $user['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>

                                            <!-- حذف المستخدم -->
                                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                            <button class="btn btn-sm btn-outline-danger btn-action"
                                                    onclick="confirmDelete(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav class="mt-4">
                        <ul class="pagination">
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($roleFilter); ?>&status=<?php echo urlencode($statusFilter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>

            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مستخدمين</h5>
                    <p class="text-muted">لم يتم العثور على مستخدمين مطابقين لمعايير البحث</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal لحذف المستخدم -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف المستخدم <strong id="userName"></strong>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" class="d-inline" id="deleteForm">
                        <input type="hidden" name="action" value="delete_user">
                        <input type="hidden" name="user_id" id="deleteUserId">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function confirmDelete(userId, userName) {
            document.getElementById('userName').textContent = userName;
            document.getElementById('deleteUserId').value = userId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function showUserDetails(userId) {
            // يمكن إضافة modal لعرض تفاصيل المستخدم
            alert('عرض تفاصيل المستخدم رقم: ' + userId);
        }
    </script>
</body>
</html>
