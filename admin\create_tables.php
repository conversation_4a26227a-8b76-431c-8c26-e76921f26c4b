<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$success_messages = [];
$error_messages = [];

// إنشاء جدول التصنيفات
$categories_sql = "
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if (executeQuery($categories_sql)) {
    $success_messages[] = 'تم إنشاء جدول التصنيفات بنجاح';
} else {
    $error_messages[] = 'فشل في إنشاء جدول التصنيفات';
}

// إنشاء جدول تقييمات الكورسات
$course_reviews_sql = "
CREATE TABLE IF NOT EXISTS course_reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    student_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_review (course_id, student_id)
)";

if (executeQuery($course_reviews_sql)) {
    $success_messages[] = 'تم إنشاء جدول تقييمات الكورسات بنجاح';
} else {
    $error_messages[] = 'فشل في إنشاء جدول تقييمات الكورسات';
}

// إنشاء جدول أنشطة المستخدمين
$user_activities_sql = "
CREATE TABLE IF NOT EXISTS user_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    activity_type VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

if (executeQuery($user_activities_sql)) {
    $success_messages[] = 'تم إنشاء جدول أنشطة المستخدمين بنجاح';
} else {
    $error_messages[] = 'فشل في إنشاء جدول أنشطة المستخدمين';
}

// تحديث جدول الجلسات لإضافة الأعمدة المطلوبة
$update_sessions_sql = "
ALTER TABLE sessions 
ADD COLUMN IF NOT EXISTS start_time TIME,
ADD COLUMN IF NOT EXISTS end_time TIME,
ADD COLUMN IF NOT EXISTS session_type VARCHAR(50) DEFAULT 'online'
";

if (executeQuery($update_sessions_sql)) {
    $success_messages[] = 'تم تحديث جدول الجلسات بنجاح';
} else {
    $error_messages[] = 'فشل في تحديث جدول الجلسات';
}

// تحديث جدول الكورسات لإضافة الأعمدة المطلوبة
$update_courses_sql = "
ALTER TABLE courses 
ADD COLUMN IF NOT EXISTS category_id INT,
ADD COLUMN IF NOT EXISTS max_students INT,
ADD FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
";

if (executeQuery($update_courses_sql)) {
    $success_messages[] = 'تم تحديث جدول الكورسات بنجاح';
} else {
    $error_messages[] = 'فشل في تحديث جدول الكورسات';
}

// تحديث جدول تسجيل الكورسات لإضافة الأعمدة المطلوبة
$update_enrollments_sql = "
ALTER TABLE course_enrollments 
ADD COLUMN IF NOT EXISTS enrollment_type VARCHAR(50) DEFAULT 'free',
ADD COLUMN IF NOT EXISTS payment_status VARCHAR(50) DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS payment_amount DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS progress_percentage DECIMAL(5,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'active'
";

if (executeQuery($update_enrollments_sql)) {
    $success_messages[] = 'تم تحديث جدول تسجيل الكورسات بنجاح';
} else {
    $error_messages[] = 'فشل في تحديث جدول تسجيل الكورسات';
}

// إضافة بعض التصنيفات الافتراضية
$default_categories = [
    ['name' => 'البرمجة', 'description' => 'كورسات البرمجة وتطوير البرمجيات'],
    ['name' => 'التصميم', 'description' => 'كورسات التصميم الجرافيكي وتصميم المواقع'],
    ['name' => 'التسويق', 'description' => 'كورسات التسويق الرقمي والتسويق التقليدي'],
    ['name' => 'الأعمال', 'description' => 'كورسات إدارة الأعمال وريادة الأعمال'],
    ['name' => 'اللغات', 'description' => 'كورسات تعلم اللغات المختلفة']
];

foreach ($default_categories as $category) {
    // التحقق من عدم وجود التصنيف مسبقاً
    $existing = fetchOne("SELECT id FROM categories WHERE name = ?", [$category['name']]);
    if (!$existing) {
        if (insertRecord('categories', $category)) {
            $success_messages[] = 'تم إضافة تصنيف: ' . $category['name'];
        }
    }
}

$pageTitle = 'إنشاء الجداول المطلوبة';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database me-2"></i>
                    نتائج إنشاء الجداول
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($success_messages)): ?>
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>العمليات الناجحة:</h6>
                        <ul class="mb-0">
                            <?php foreach ($success_messages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_messages)): ?>
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-circle me-2"></i>العمليات الفاشلة:</h6>
                        <ul class="mb-0">
                            <?php foreach ($error_messages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="mt-4">
                    <h6>الجداول المطلوبة:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم الجدول</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>categories</td>
                                    <td>تصنيفات الكورسات</td>
                                    <td><span class="badge bg-success">موجود</span></td>
                                </tr>
                                <tr>
                                    <td>course_reviews</td>
                                    <td>تقييمات الكورسات</td>
                                    <td><span class="badge bg-success">موجود</span></td>
                                </tr>
                                <tr>
                                    <td>user_activities</td>
                                    <td>أنشطة المستخدمين</td>
                                    <td><span class="badge bg-success">موجود</span></td>
                                </tr>
                                <tr>
                                    <td>sessions (محدث)</td>
                                    <td>جلسات الكورسات مع أعمدة إضافية</td>
                                    <td><span class="badge bg-success">محدث</span></td>
                                </tr>
                                <tr>
                                    <td>courses (محدث)</td>
                                    <td>الكورسات مع أعمدة إضافية</td>
                                    <td><span class="badge bg-success">محدث</span></td>
                                </tr>
                                <tr>
                                    <td>course_enrollments (محدث)</td>
                                    <td>تسجيل الكورسات مع أعمدة إضافية</td>
                                    <td><span class="badge bg-success">محدث</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="mt-4">
                    <a href="manage-courses-new.php" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة إلى إدارة الكورسات
                    </a>
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-home me-2"></i>
                        الصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
