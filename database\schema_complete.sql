-- قاعدة البيانات الموحدة والشاملة
-- Complete Unified Database Schema
-- =====================================

SET FOREIGN_KEY_CHECKS = 0;
DROP DATABASE IF EXISTS zoom_learning_system;
CREATE DATABASE zoom_learning_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE zoom_learning_system;

-- جدول المستخدمين الموحد
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NULL,
    profile_picture VARCHAR(255) NULL,
    role ENUM('student', 'instructor', 'admin') NOT NULL DEFAULT 'student',
    status ENUM('active', 'inactive', 'pending', 'suspended') NOT NULL DEFAULT 'pending',
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255) NULL,
    password_reset_token VARCHAR(255) NULL,
    password_reset_expires TIMESTAMP NULL,
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    
    -- معلومات إضافية للمدربين
    specialization VARCHAR(100) NULL,
    bio TEXT NULL,
    experience_years INT NULL,
    education TEXT NULL,
    certifications TEXT NULL,
    social_links JSON NULL,
    hourly_rate DECIMAL(10,2) NULL,
    
    -- معلومات إضافية للطلاب
    date_of_birth DATE NULL,
    gender ENUM('male', 'female') NULL,
    country VARCHAR(50) NULL,
    city VARCHAR(50) NULL,
    education_level VARCHAR(50) NULL,
    interests TEXT NULL,
    
    -- إعدادات الإشعارات
    notification_preferences JSON NULL,
    
    -- تواريخ النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
) ENGINE=InnoDB;

-- جدول التصنيفات
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT NULL,
    icon VARCHAR(50) NULL,
    color VARCHAR(7) DEFAULT '#667eea',
    parent_id INT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_parent (parent_id),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
) ENGINE=InnoDB;

-- جدول الكورسات الموحد
CREATE TABLE courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    short_description TEXT NULL,
    description LONGTEXT NULL,
    thumbnail VARCHAR(255) NULL,
    preview_video VARCHAR(255) NULL,
    
    instructor_id INT NOT NULL,
    category_id INT NOT NULL,
    
    -- معلومات الكورس
    level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    language VARCHAR(10) DEFAULT 'ar',
    duration_hours DECIMAL(5,2) DEFAULT 0,
    total_lessons INT DEFAULT 0,
    
    -- معلومات السعر
    price DECIMAL(10,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    discount_price DECIMAL(10,2) NULL,
    discount_expires TIMESTAMP NULL,
    
    -- إعدادات الكورس
    status ENUM('draft', 'active', 'inactive', 'archived') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    allow_comments BOOLEAN DEFAULT TRUE,
    certificate_enabled BOOLEAN DEFAULT TRUE,
    
    -- إحصائيات
    total_students INT DEFAULT 0,
    total_reviews INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0,
    total_sales DECIMAL(12,2) DEFAULT 0,
    
    -- متطلبات وأهداف
    requirements JSON NULL,
    objectives JSON NULL,
    target_audience JSON NULL,
    
    -- SEO
    meta_title VARCHAR(200) NULL,
    meta_description TEXT NULL,
    meta_keywords TEXT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    
    INDEX idx_instructor (instructor_id),
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_price (price),
    INDEX idx_rating (rating),
    INDEX idx_created (created_at),
    FULLTEXT idx_search (title, short_description, description)
) ENGINE=InnoDB;

-- جدول أقسام الكورس
CREATE TABLE course_sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    INDEX idx_course (course_id),
    INDEX idx_sort (sort_order)
) ENGINE=InnoDB;

-- جدول دروس الكورس (محسن لرفع الفيديو)
CREATE TABLE course_lessons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    section_id INT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NULL,
    content LONGTEXT NULL,
    
    -- معلومات الفيديو المحسنة
    video_type ENUM('upload', 'youtube', 'vimeo', 'external') DEFAULT 'upload',
    video_path VARCHAR(500) NULL,
    video_url VARCHAR(500) NULL,
    video_duration INT NULL, -- بالثواني
    video_size BIGINT NULL, -- بالبايت
    video_format VARCHAR(10) NULL,
    video_quality VARCHAR(10) NULL,
    video_thumbnail VARCHAR(255) NULL,
    
    -- معلومات الملفات المرفقة
    attachments JSON NULL,
    
    -- إعدادات الدرس
    lesson_type ENUM('video', 'text', 'quiz', 'assignment', 'live') DEFAULT 'video',
    is_preview BOOLEAN DEFAULT FALSE,
    is_downloadable BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    status ENUM('draft', 'published') DEFAULT 'draft',
    
    -- تتبع المشاهدة
    view_count INT DEFAULT 0,
    average_watch_time INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (section_id) REFERENCES course_sections(id) ON DELETE SET NULL,
    
    INDEX idx_course (course_id),
    INDEX idx_section (section_id),
    INDEX idx_sort (sort_order),
    INDEX idx_type (lesson_type),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول الاختبارات المحسن
CREATE TABLE quizzes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    lesson_id INT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NULL,
    instructions TEXT NULL,
    
    -- إعدادات الاختبار
    quiz_type ENUM('practice', 'graded', 'final') DEFAULT 'practice',
    time_limit INT NULL, -- بالدقائق
    attempts_allowed INT DEFAULT 1,
    passing_score DECIMAL(5,2) DEFAULT 70.00,
    randomize_questions BOOLEAN DEFAULT FALSE,
    show_results BOOLEAN DEFAULT TRUE,
    show_correct_answers BOOLEAN DEFAULT TRUE,
    
    -- نقاط وتقييم
    total_points INT DEFAULT 0,
    weight_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- تواريخ
    available_from TIMESTAMP NULL,
    available_until TIMESTAMP NULL,
    
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE SET NULL,
    
    INDEX idx_course (course_id),
    INDEX idx_lesson (lesson_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول أسئلة الاختبارات
CREATE TABLE quiz_questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quiz_id INT NOT NULL,
    question_text TEXT NOT NULL,
    question_type ENUM('multiple_choice', 'true_false', 'short_answer', 'essay', 'matching') NOT NULL,
    options JSON NULL, -- للخيارات المتعددة
    correct_answer TEXT NULL,
    explanation TEXT NULL,
    points DECIMAL(5,2) DEFAULT 1.00,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    INDEX idx_quiz (quiz_id),
    INDEX idx_sort (sort_order)
) ENGINE=InnoDB;

-- جدول الواجبات المحسن
CREATE TABLE assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    lesson_id INT NULL,
    title VARCHAR(200) NOT NULL,
    description LONGTEXT NOT NULL,
    instructions TEXT NULL,
    
    -- ملفات الواجب
    attachment_files JSON NULL,
    submission_format ENUM('text', 'file', 'both') DEFAULT 'both',
    allowed_file_types JSON NULL,
    max_file_size INT DEFAULT 10485760, -- 10MB
    
    -- تقييم
    total_points DECIMAL(5,2) DEFAULT 100.00,
    weight_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- تواريخ
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    due_date TIMESTAMP NULL,
    late_submission_allowed BOOLEAN DEFAULT TRUE,
    late_penalty_percentage DECIMAL(5,2) DEFAULT 10.00,
    
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE SET NULL,
    
    INDEX idx_course (course_id),
    INDEX idx_lesson (lesson_id),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول تسليم الواجبات
CREATE TABLE assignment_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    student_id INT NOT NULL,
    submission_text LONGTEXT NULL,
    submitted_files JSON NULL,
    
    -- تقييم
    grade DECIMAL(5,2) NULL,
    feedback TEXT NULL,
    graded_by INT NULL,
    graded_at TIMESTAMP NULL,
    
    -- حالة التسليم
    status ENUM('submitted', 'graded', 'returned', 'late') DEFAULT 'submitted',
    is_late BOOLEAN DEFAULT FALSE,
    
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (graded_by) REFERENCES users(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_submission (assignment_id, student_id),
    INDEX idx_assignment (assignment_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- جدول التسجيل في الكورسات
CREATE TABLE course_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_type ENUM('free', 'paid', 'invited') DEFAULT 'free',
    payment_id INT NULL,
    
    -- حالة التسجيل
    status ENUM('pending', 'active', 'completed', 'cancelled', 'expired') DEFAULT 'pending',
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- تواريخ
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    
    -- إحصائيات
    total_watch_time INT DEFAULT 0, -- بالثواني
    lessons_completed INT DEFAULT 0,
    quizzes_completed INT DEFAULT 0,
    assignments_completed INT DEFAULT 0,
    
    -- تقييم نهائي
    final_grade DECIMAL(5,2) NULL,
    certificate_issued BOOLEAN DEFAULT FALSE,
    certificate_number VARCHAR(50) NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_enrollment (student_id, course_id),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_status (status),
    INDEX idx_enrolled (enrolled_at)
) ENGINE=InnoDB;

-- جدول تقدم الطلاب في الدروس
CREATE TABLE lesson_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    lesson_id INT NOT NULL,
    course_id INT NOT NULL,
    
    -- تقدم المشاهدة
    watch_time INT DEFAULT 0, -- بالثواني
    completion_percentage DECIMAL(5,2) DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    
    -- تفاعل
    is_bookmarked BOOLEAN DEFAULT FALSE,
    notes TEXT NULL,
    last_position INT DEFAULT 0, -- آخر موضع في الفيديو
    
    -- تواريخ
    first_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_progress (student_id, lesson_id),
    INDEX idx_student (student_id),
    INDEX idx_lesson (lesson_id),
    INDEX idx_course (course_id),
    INDEX idx_completed (is_completed)
) ENGINE=InnoDB;

-- جدول نتائج الاختبارات
CREATE TABLE quiz_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quiz_id INT NOT NULL,
    student_id INT NOT NULL,
    attempt_number INT NOT NULL,
    
    -- النتائج
    score DECIMAL(5,2) DEFAULT 0,
    total_points DECIMAL(5,2) DEFAULT 0,
    percentage DECIMAL(5,2) DEFAULT 0,
    is_passed BOOLEAN DEFAULT FALSE,
    
    -- إجابات
    answers JSON NULL,
    
    -- وقت الاختبار
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP NULL,
    time_taken INT NULL, -- بالثواني
    
    status ENUM('in_progress', 'submitted', 'graded') DEFAULT 'in_progress',
    
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_quiz (quiz_id),
    INDEX idx_student (student_id),
    INDEX idx_attempt (quiz_id, student_id, attempt_number)
) ENGINE=InnoDB;

-- جدول المدفوعات المحسن
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    
    -- معلومات الدفع
    payment_method ENUM('stripe', 'paypal', 'bank_transfer', 'wallet') NOT NULL,
    transaction_id VARCHAR(255) NOT NULL UNIQUE,
    payment_intent_id VARCHAR(255) NULL,
    session_id VARCHAR(255) NULL,
    
    -- المبالغ
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    platform_fee DECIMAL(10,2) DEFAULT 0,
    instructor_amount DECIMAL(10,2) DEFAULT 0,
    
    -- كوبون الخصم
    coupon_code VARCHAR(50) NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    
    -- حالة الدفع
    status ENUM('pending', 'processing', 'completed', 'failed', 'refunded', 'cancelled') DEFAULT 'pending',
    
    -- تواريخ
    payment_date TIMESTAMP NULL,
    refund_date TIMESTAMP NULL,
    refund_reason TEXT NULL,
    
    -- بيانات إضافية
    gateway_response JSON NULL,
    metadata JSON NULL,
    notes TEXT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    
    INDEX idx_user (user_id),
    INDEX idx_course (course_id),
    INDEX idx_status (status),
    INDEX idx_transaction (transaction_id),
    INDEX idx_payment_date (payment_date)
) ENGINE=InnoDB;

-- جدول رفع الملفات المحسن (لمنع التكرار)
CREATE TABLE file_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL UNIQUE,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA-256 hash لمنع التكرار
    
    -- معلومات الرفع
    uploaded_by INT NOT NULL,
    upload_type ENUM('profile', 'course_thumbnail', 'lesson_video', 'assignment', 'document') NOT NULL,
    related_id INT NULL, -- معرف العنصر المرتبط
    
    -- معلومات إضافية للفيديو
    video_duration INT NULL,
    video_resolution VARCHAR(20) NULL,
    video_bitrate INT NULL,
    thumbnail_path VARCHAR(500) NULL,
    
    -- معلومات إضافية للصور
    image_width INT NULL,
    image_height INT NULL,
    
    -- حالة المعالجة
    processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    processing_progress INT DEFAULT 0,
    processing_error TEXT NULL,
    
    -- إعدادات الوصول
    is_public BOOLEAN DEFAULT FALSE,
    access_token VARCHAR(255) NULL,
    download_count INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_hash (file_hash),
    INDEX idx_uploader (uploaded_by),
    INDEX idx_type (upload_type),
    INDEX idx_related (related_id),
    INDEX idx_hash (file_hash),
    INDEX idx_created (created_at)
) ENGINE=InnoDB;

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NULL,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
) ENGINE=InnoDB;

-- إدراج البيانات الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'منصة التعلم الإلكتروني', 'string', 'اسم الموقع', TRUE),
('site_description', 'منصة تعليمية متقدمة', 'string', 'وصف الموقع', TRUE),
('platform_commission', '30', 'number', 'نسبة عمولة المنصة', FALSE),
('max_file_size', '52428800', 'number', 'الحد الأقصى لحجم الملف (50MB)', FALSE),
('allowed_video_formats', '["mp4", "avi", "mov", "wmv"]', 'json', 'صيغ الفيديو المسموحة', FALSE),
('email_verification_required', 'true', 'boolean', 'تفعيل التحقق من البريد الإلكتروني', FALSE);

-- إدراج تصنيفات أساسية
INSERT INTO categories (name, slug, description, icon, color) VALUES
('البرمجة وتطوير المواقع', 'programming', 'تعلم لغات البرمجة وتطوير المواقع والتطبيقات', 'fas fa-code', '#667eea'),
('التصميم والجرافيك', 'design', 'تصميم الجرافيك والواجهات والهوية البصرية', 'fas fa-palette', '#764ba2'),
('التسويق الرقمي', 'marketing', 'استراتيجيات التسويق الإلكتروني ووسائل التواصل', 'fas fa-bullhorn', '#f093fb'),
('إدارة الأعمال', 'business', 'مهارات الإدارة وريادة الأعمال والقيادة', 'fas fa-briefcase', '#4facfe'),
('اللغات', 'languages', 'تعلم اللغات المختلفة والترجمة', 'fas fa-language', '#43e97b'),
('التصوير والمونتاج', 'photography', 'فنون التصوير وتحرير الفيديو', 'fas fa-camera', '#38f9d7');

-- جدول سجل الأنشطة
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    details TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_created (created_at)
) ENGINE=InnoDB;

-- جدول الملفات المؤقتة
CREATE TABLE temp_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    uploaded_by INT NULL,
    purpose VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_created (created_at)
) ENGINE=InnoDB;

-- جدول رموز إعادة تعيين كلمة المرور
CREATE TABLE password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
) ENGINE=InnoDB;

-- جدول رموز تفعيل البريد الإلكتروني
CREATE TABLE email_verification_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
) ENGINE=InnoDB;

-- إنشاء مستخدم إداري افتراضي
INSERT INTO users (username, email, password, name, role, status, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المدير العام', 'admin', 'active', TRUE);

SET FOREIGN_KEY_CHECKS = 1;
