<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة الطلاب';
$breadcrumbs = [
    ['title' => 'إدارة الطلاب']
];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $student_id = $_POST['student_id'] ?? 0;
    $course_id = $_POST['course_id'] ?? 0;

    if ($action === 'remove_student' && $student_id && $course_id) {
        try {
            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);

            if ($stmt->fetch()) {
                // إزالة الطالب من الكورس
                $stmt = $conn->prepare("
                    UPDATE course_enrollments
                    SET status = 'dropped'
                    WHERE student_id = ? AND course_id = ?
                ");
                $stmt->execute([$student_id, $course_id]);

                $success_message = 'تم إزالة الطالب من الكورس بنجاح';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء إزالة الطالب';
        }
    }

    if ($action === 'send_message' && $student_id) {
        $message = trim($_POST['message'] ?? '');
        if (!empty($message)) {
            try {
                // إرسال رسالة للطالب (يمكن تطوير هذا لاحقاً)
                $success_message = 'تم إرسال الرسالة بنجاح';
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء إرسال الرسالة';
            }
        }
    }
}

// فلاتر البحث
$search = $_GET['search'] ?? '';
$course_filter = $_GET['course_id'] ?? '';
$status_filter = $_GET['status'] ?? '';

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title
        FROM courses
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// جلب الطلاب
try {

    $where_conditions = ["c.instructor_id = ?"];
    $params = [$_SESSION['user_id']];

    if (!empty($search)) {
        $where_conditions[] = "(u.name LIKE ? OR u.email LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if (!empty($course_filter)) {
        $where_conditions[] = "c.id = ?";
        $params[] = $course_filter;
    }

    if (!empty($status_filter)) {
        $where_conditions[] = "ce.status = ?";
        $params[] = $status_filter;
    }

    $where_clause = implode(' AND ', $where_conditions);

    $stmt = $conn->prepare("
        SELECT
            u.id,
            u.name,
            u.email,
            u.phone,
            c.id as course_id,
            c.title as course_title,
            ce.status as enrollment_status,
            ce.enrolled_at,
            COALESCE(ce.progress_percentage, 0) as progress_percentage,
            0 as avg_grade,
            0 as total_grades,
            0 as attended_sessions,
            0 as total_sessions
        FROM users u
        INNER JOIN course_enrollments ce ON u.id = ce.student_id
        INNER JOIN courses c ON ce.course_id = c.id
        WHERE $where_clause
        ORDER BY u.name, c.title
    ");
    $stmt->execute($params);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات سريعة
    $stmt = $conn->prepare("
        SELECT
            COUNT(DISTINCT ce.student_id) as total_students,
            COUNT(DISTINCT CASE WHEN ce.status = 'active' THEN ce.student_id END) as active_students,
            COUNT(DISTINCT CASE WHEN ce.status = 'completed' THEN ce.student_id END) as completed_students,
            COUNT(DISTINCT CASE WHEN ce.status = 'dropped' THEN ce.student_id END) as dropped_students,
            COALESCE(AVG(CASE WHEN ce.progress_percentage IS NOT NULL THEN ce.progress_percentage ELSE 0 END), 0) as avg_progress
        FROM course_enrollments ce
        INNER JOIN courses c ON ce.course_id = c.id
        WHERE c.instructor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // التأكد من وجود القيم
    if (!$stats) {
        $stats = ['total_students' => 0, 'active_students' => 0, 'completed_students' => 0, 'dropped_students' => 0, 'avg_progress' => 0];
    }

} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
    $students = [];
    $stats = ['total_students' => 0, 'active_students' => 0, 'completed_students' => 0, 'dropped_students' => 0, 'avg_progress' => 0];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-users text-primary me-2"></i>
            إدارة الطلاب
        </h2>
        <p class="text-muted mb-0">عرض وإدارة جميع الطلاب المسجلين في كورساتك</p>
    </div>
    <div class="d-flex gap-2">
        <a href="enrollment-requests.php" class="btn btn-warning">
            <i class="fas fa-user-clock me-1"></i>طلبات الانضمام
        </a>
        <a href="bulk-actions.php" class="btn btn-outline-primary">
            <i class="fas fa-tasks me-1"></i>إجراءات جماعية
        </a>
        <button class="btn btn-success" onclick="exportStudents()">
            <i class="fas fa-download me-1"></i>تصدير
        </button>
        <button class="btn btn-info" onclick="refreshData()" title="تحديث البيانات">
            <i class="fas fa-sync-alt me-1"></i>تحديث
        </button>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- بطاقات الإحصائيات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm stats-card primary">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-users text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="mb-1 fw-bold text-primary"><?php echo $stats['total_students']; ?></h4>
                        <p class="text-muted mb-0 fw-medium">إجمالي الطلاب</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm stats-card success">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-user-check text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="mb-1 fw-bold text-success"><?php echo $stats['active_students']; ?></h4>
                        <p class="text-muted mb-0 fw-medium">طلاب نشطون</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm stats-card info">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-graduation-cap text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="mb-1 fw-bold text-info"><?php echo $stats['completed_students']; ?></h4>
                        <p class="text-muted mb-0 fw-medium">مكملون</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm stats-card warning">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-chart-line text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="mb-1 fw-bold text-warning"><?php echo number_format($stats['avg_progress'], 1); ?>%</h4>
                        <p class="text-muted mb-0 fw-medium">متوسط التقدم</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" name="search" id="search" class="form-control"
                       value="<?php echo htmlspecialchars($search); ?>"
                       placeholder="اسم الطالب أو البريد الإلكتروني...">
            </div>
            <div class="col-md-3">
                <label for="course_id" class="form-label">الكورس</label>
                <select name="course_id" id="course_id" class="form-select">
                    <option value="">جميع الكورسات</option>
                    <?php foreach ($instructor_courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>"
                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select name="status" id="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشط</option>
                    <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                    <option value="dropped" <?php echo $status_filter == 'dropped' ? 'selected' : ''; ?>>منسحب</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- جدول الطلاب -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة الطلاب
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                    <i class="fas fa-check-square me-1"></i>تحديد الكل
                </button>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle"
                            data-bs-toggle="dropdown">
                        <i class="fas fa-cog me-1"></i>إجراءات
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="bulkMessage()">
                            <i class="fas fa-envelope me-2"></i>إرسال رسالة جماعية
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="bulkExport()">
                            <i class="fas fa-download me-2"></i>تصدير المحدد
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="bulkRemove()">
                            <i class="fas fa-user-times me-2"></i>إزالة المحدد
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($students)): ?>
        <div class="text-center py-5">
            <i class="fas fa-user-graduate text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">لا توجد طلاب</h5>
            <p class="text-muted">لم يتم العثور على طلاب بناءً على المعايير المحددة</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="studentsTable">
                <thead class="table-light">
                    <tr>
                        <th>
                            <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                        </th>
                        <th>الطالب</th>
                        <th>الكورس</th>
                        <th>تاريخ التسجيل</th>
                        <th>الحالة</th>
                        <th>التقدم</th>
                        <th>الدرجات</th>
                        <th>الحضور</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($students as $student): ?>
                    <tr class="student-row">
                        <td>
                            <input type="checkbox" class="form-check-input student-checkbox"
                                   value="<?php echo $student['id']; ?>">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                    <i class="fas fa-user text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0 student-name"><?php echo htmlspecialchars($student['name']); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                    <?php if ($student['phone']): ?>
                                    <br><small class="text-muted">
                                        <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($student['phone']); ?>
                                    </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="fw-medium"><?php echo htmlspecialchars($student['course_title']); ?></span>
                        </td>
                        <td>
                            <span class="text-muted"><?php echo date('d/m/Y', strtotime($student['enrolled_at'])); ?></span>
                            <br><small class="text-muted">
                                <?php
                                $days_ago = floor((time() - strtotime($student['enrolled_at'])) / (60 * 60 * 24));
                                echo "منذ {$days_ago} يوم";
                                ?>
                            </small>
                        </td>
                        <td>
                            <?php
                            $status_classes = [
                                'active' => 'success',
                                'completed' => 'primary',
                                'dropped' => 'danger',
                                'suspended' => 'warning'
                            ];
                            $status_text = [
                                'active' => 'نشط',
                                'completed' => 'مكتمل',
                                'dropped' => 'منسحب',
                                'suspended' => 'معلق'
                            ];
                            ?>
                            <span class="badge bg-<?php echo $status_classes[$student['enrollment_status']] ?? 'secondary'; ?>">
                                <?php echo $status_text[$student['enrollment_status']] ?? $student['enrollment_status']; ?>
                            </span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="me-2 fw-bold"><?php echo number_format($student['progress_percentage'], 1); ?>%</span>
                                <div class="progress flex-grow-1" style="width: 80px; height: 8px;">
                                    <?php
                                    $progress_color = $student['progress_percentage'] >= 80 ? 'success' :
                                                     ($student['progress_percentage'] >= 50 ? 'warning' : 'danger');
                                    ?>
                                    <div class="progress-bar bg-<?php echo $progress_color; ?>"
                                         style="width: <?php echo $student['progress_percentage']; ?>%"></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <?php if ($student['total_grades'] > 0): ?>
                            <div class="text-center">
                                <?php
                                $grade_color = $student['avg_grade'] >= 85 ? 'success' :
                                              ($student['avg_grade'] >= 70 ? 'warning' : 'danger');
                                ?>
                                <span class="badge bg-<?php echo $grade_color; ?> fs-6">
                                    <?php echo number_format($student['avg_grade'], 1); ?>
                                </span>
                                <br><small class="text-muted"><?php echo $student['total_grades']; ?> تقييم</small>
                            </div>
                            <?php else: ?>
                            <div class="text-center">
                                <span class="badge bg-secondary">لا توجد درجات</span>
                            </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $attendance_rate = $student['total_sessions'] > 0 ?
                                ($student['attended_sessions'] / $student['total_sessions']) * 100 : 0;
                            $attendance_color = $attendance_rate >= 80 ? 'success' :
                                               ($attendance_rate >= 60 ? 'warning' : 'danger');
                            ?>
                            <div class="text-center">
                                <span class="badge bg-<?php echo $attendance_color; ?> fs-6">
                                    <?php echo number_format($attendance_rate, 1); ?>%
                                </span>
                                <br><small class="text-muted">
                                    <?php echo $student['attended_sessions']; ?>/<?php echo $student['total_sessions']; ?> جلسة
                                </small>
                            </div>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="viewStudentDetails(<?php echo $student['id']; ?>, <?php echo $student['course_id']; ?>)"
                                        title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success"
                                        onclick="sendMessage(<?php echo $student['id']; ?>, '<?php echo htmlspecialchars($student['name'], ENT_QUOTES); ?>')"
                                        title="إرسال رسالة">
                                    <i class="fas fa-envelope"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning"
                                        onclick="manageGrades(<?php echo $student['id']; ?>, <?php echo $student['course_id']; ?>)"
                                        title="إدارة الدرجات">
                                    <i class="fas fa-star"></i>
                                </button>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            data-bs-toggle="dropdown" title="المزيد">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="editStudent(<?php echo $student['id']; ?>)">
                                            <i class="fas fa-edit me-2"></i>تعديل البيانات
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="viewProgress(<?php echo $student['id']; ?>, <?php echo $student['course_id']; ?>)">
                                            <i class="fas fa-chart-line me-2"></i>تتبع التقدم
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="viewAttendance(<?php echo $student['id']; ?>, <?php echo $student['course_id']; ?>)">
                                            <i class="fas fa-calendar-check me-2"></i>سجل الحضور
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"
                                               onclick="removeStudent(<?php echo $student['id']; ?>, <?php echo $student['course_id']; ?>, '<?php echo htmlspecialchars($student['name'], ENT_QUOTES); ?>')">
                                            <i class="fas fa-user-times me-2"></i>إزالة من الكورس
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal إرسال رسالة -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال رسالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="send_message">
                    <input type="hidden" name="student_id" id="messageStudentId">

                    <div class="mb-3">
                        <label for="studentName" class="form-label">إلى:</label>
                        <input type="text" id="studentName" class="form-control" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="message" class="form-label">الرسالة</label>
                        <textarea name="message" id="message" class="form-control" rows="5"
                                  placeholder="اكتب رسالتك هنا..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إرسال الرسالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal إزالة طالب -->
<div class="modal fade" id="removeStudentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">تأكيد الإزالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="remove_student">
                    <input type="hidden" name="student_id" id="removeStudentId">
                    <input type="hidden" name="course_id" id="removeCourseId">

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من إزالة الطالب <strong id="removeStudentName"></strong> من الكورس؟
                    </div>

                    <p class="text-muted">
                        سيتم تغيير حالة الطالب إلى "منسحب" ولن يتمكن من الوصول إلى محتوى الكورس.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">تأكيد الإزالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تحديد/إلغاء تحديد جميع الطلاب
document.getElementById('selectAllCheckbox').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// دالة تحديد الكل
function selectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    selectAllCheckbox.checked = !selectAllCheckbox.checked;
    selectAllCheckbox.dispatchEvent(new Event('change'));
}

// عرض تفاصيل الطالب
function viewStudentDetails(studentId, courseId) {
    window.open(`student-details.php?id=${studentId}&course_id=${courseId}`,
                '_blank', 'width=900,height=700,scrollbars=yes');
}

// إدارة الدرجات
function manageGrades(studentId, courseId) {
    window.location.href = `student-grades.php?student_id=${studentId}&course_id=${courseId}`;
}

// تعديل بيانات الطالب
function editStudent(studentId) {
    window.location.href = `edit-student.php?id=${studentId}`;
}

// عرض تقدم الطالب
function viewProgress(studentId, courseId) {
    window.open(`student-progress.php?id=${studentId}&course_id=${courseId}`,
                '_blank', 'width=800,height=600,scrollbars=yes');
}

// عرض سجل الحضور
function viewAttendance(studentId, courseId) {
    window.open(`student-attendance.php?student_id=${studentId}&course_id=${courseId}`,
                '_blank', 'width=800,height=600,scrollbars=yes');
}

// إرسال رسالة لطالب
function sendMessage(studentId, studentName) {
    document.getElementById('messageStudentId').value = studentId;
    document.getElementById('studentName').value = studentName;
    document.getElementById('message').value = '';

    new bootstrap.Modal(document.getElementById('messageModal')).show();
}

// إزالة طالب
function removeStudent(studentId, courseId, studentName) {
    document.getElementById('removeStudentId').value = studentId;
    document.getElementById('removeCourseId').value = courseId;
    document.getElementById('removeStudentName').textContent = studentName;

    new bootstrap.Modal(document.getElementById('removeStudentModal')).show();
}

// الحصول على الطلاب المحددين
function getSelectedStudents() {
    const checkboxes = document.querySelectorAll('.student-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// إرسال رسالة جماعية
function bulkMessage() {
    const selectedStudents = getSelectedStudents();
    if (selectedStudents.length === 0) {
        alert('يرجى تحديد طلاب أولاً');
        return;
    }

    // يمكن فتح modal للرسالة الجماعية
    alert(`سيتم إرسال رسالة لـ ${selectedStudents.length} طالب`);
}

// تصدير الطلاب المحددين
function bulkExport() {
    const selectedStudents = getSelectedStudents();
    if (selectedStudents.length === 0) {
        alert('يرجى تحديد طلاب أولاً');
        return;
    }

    const studentIds = selectedStudents.join(',');
    window.open(`export-students.php?student_ids=${studentIds}`, '_blank');
}

// إزالة جماعية
function bulkRemove() {
    const selectedStudents = getSelectedStudents();
    if (selectedStudents.length === 0) {
        alert('يرجى تحديد طلاب أولاً');
        return;
    }

    if (confirm(`هل أنت متأكد من إزالة ${selectedStudents.length} طالب؟`)) {
        // يمكن تنفيذ هذا عبر AJAX
        alert('سيتم تنفيذ هذه الميزة قريباً');
    }
}

// تصدير جميع الطلاب
function exportStudents() {
    const params = new URLSearchParams(window.location.search);
    const exportUrl = 'export-students.php?' + params.toString();
    window.open(exportUrl, '_blank');
}

// البحث السريع
document.getElementById('search').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('#studentsTable tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// تهيئة DataTables
document.addEventListener('DOMContentLoaded', function() {
    if (typeof DataTable !== 'undefined' && document.getElementById('studentsTable')) {
        // فحص عدم إعادة التهيئة
        if (!$.fn.DataTable.isDataTable('#studentsTable')) {
            new DataTable('#studentsTable', {
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                },
                pageLength: 25,
                order: [[1, 'asc']], // ترتيب حسب اسم الطالب
                columnDefs: [
                    { orderable: false, targets: [0, 8] } // عدم ترتيب عمود الاختيار والإجراءات
                ]
            });
        }
    }

    // تحسين تفاعل الصفوف (تم نقله إلى CSS)
});

// تحديث البيانات بشكل دوري
function refreshData() {
    // إضافة مؤشر التحميل
    const refreshBtn = document.querySelector('.btn-success');
    if (refreshBtn) {
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحديث...';
        refreshBtn.disabled = true;

        // محاكاة تحديث البيانات
        setTimeout(() => {
            location.reload();
        }, 1000);
    }
}

// تحديث تلقائي كل 10 دقائق
setInterval(function() {
    console.log('تحديث تلقائي للبيانات...');
    // يمكن إضافة AJAX هنا لتحديث البيانات بدون إعادة تحميل الصفحة
}, 600000);
</script>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.progress {
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.3s ease;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--bs-gray-700);
    background-color: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-group .btn {
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.btn-group .btn:not(:last-child) {
    margin-right: 0.25rem;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
}

.card {
    transition: all 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
    }

    .progress {
        width: 50px !important;
        height: 6px;
    }
}

/* تحسينات إضافية */
.student-row {
    cursor: pointer;
}

.student-row:hover .student-name {
    color: #0d6efd;
}

.enrollment-status {
    font-weight: 500;
}

.stats-card {
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.stats-card.primary {
    border-left-color: #0d6efd;
}

.stats-card.success {
    border-left-color: #198754;
}

.stats-card.info {
    border-left-color: #0dcaf0;
}

.stats-card.warning {
    border-left-color: #ffc107;
}
</style>

<?php include 'includes/footer.php'; ?>