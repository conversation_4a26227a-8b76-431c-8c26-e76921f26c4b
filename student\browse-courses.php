<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'تصفح الكورسات';
$breadcrumbs = [
    ['title' => 'تصفح الكورسات']
];

$student_id = $_SESSION['user_id'];
$success = '';
$error = '';

// معالجة طلب الانضمام للكورس
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['enroll_course'])) {
    $course_id = (int)$_POST['course_id'];
    
    try {
        // التحقق من أن الكورس متاح
        $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND status = 'active'");
        $stmt->execute([$course_id]);
        $course = $stmt->fetch();
        
        if (!$course) {
            throw new Exception('الكورس غير متاح');
        }
        
        // التحقق من عدم التسجيل المسبق
        $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE course_id = ? AND student_id = ?");
        $stmt->execute([$course_id, $student_id]);
        if ($stmt->fetch()) {
            throw new Exception('أنت مسجل في هذا الكورس مسبقاً');
        }
        
        // التسجيل في الكورس
        $enrollment_type = $course['price'] > 0 ? 'paid' : 'free';
        $payment_status = $course['price'] > 0 ? 'pending' : 'completed';
        
        $stmt = $conn->prepare("
            INSERT INTO course_enrollments (course_id, student_id, enrollment_type, payment_status, payment_amount)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$course_id, $student_id, $enrollment_type, $payment_status, $course['price']]);
        
        if ($course['price'] > 0) {
            $success = 'تم إرسال طلب التسجيل. يرجى إكمال عملية الدفع.';
        } else {
            $success = 'تم التسجيل في الكورس بنجاح!';
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معاملات البحث والفلترة
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$level = $_GET['level'] ?? '';
$price_filter = $_GET['price'] ?? '';
$sort = $_GET['sort'] ?? 'newest';

// بناء استعلام البحث
$where_conditions = ["c.status = 'active'"];
$params = [];

if ($search) {
    $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ? OR COALESCE(u.full_name, u.username) LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if ($category) {
    $where_conditions[] = "c.category_id = ?";
    $params[] = $category;
}

if ($level) {
    $where_conditions[] = "c.level = ?";
    $params[] = $level;
}

if ($price_filter === 'free') {
    $where_conditions[] = "c.price = 0";
} elseif ($price_filter === 'paid') {
    $where_conditions[] = "c.price > 0";
}

$where_clause = implode(' AND ', $where_conditions);

// ترتيب النتائج
$order_clause = '';
switch ($sort) {
    case 'newest':
        $order_clause = 'c.created_at DESC';
        break;
    case 'oldest':
        $order_clause = 'c.created_at ASC';
        break;
    case 'price_low':
        $order_clause = 'c.price ASC';
        break;
    case 'price_high':
        $order_clause = 'c.price DESC';
        break;
    case 'popular':
        $order_clause = 'enrollment_count DESC';
        break;
    default:
        $order_clause = 'c.created_at DESC';
}

// تهيئة المتغيرات
$courses = [];
$categories = [];

try {
    // جلب الكورسات
    $stmt = $conn->prepare("
        SELECT c.*,
               COALESCE(u.full_name, u.username) as instructor_name,
               cat.name as category_name,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id) as enrollment_count,
               (SELECT COUNT(*) FROM sessions WHERE course_id = c.id) as session_count,
               (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating,
               (SELECT COUNT(*) FROM course_reviews WHERE course_id = c.id) as review_count,
               CASE WHEN ce.id IS NOT NULL THEN 1 ELSE 0 END as is_enrolled
        FROM courses c
        JOIN users u ON c.instructor_id = u.id
        LEFT JOIN categories cat ON c.category_id = cat.id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.student_id = ?
        WHERE $where_clause
        ORDER BY $order_clause
    ");

    $all_params = array_merge([$student_id], $params);
    $stmt->execute($all_params);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب التصنيفات
    $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب الكورسات: ' . $e->getMessage();
    $courses = [];
    $categories = [];
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- العنوان -->
        <div class="col-12 mb-4">
            <h2 class="text-primary">
                <i class="fas fa-search me-2"></i>
                تصفح الكورسات
            </h2>
            <p class="text-muted">اكتشف كورسات جديدة وطور مهاراتك</p>
        </div>

        <?php if ($success): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="col-12 mb-3">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <?php endif; ?>

        <!-- فلاتر البحث -->
        <div class="col-12 mb-4">
            <div class="card-student">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">البحث</label>
                            <input type="text" name="search" class="form-control" 
                                   placeholder="ابحث في الكورسات..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">التصنيف</label>
                            <select name="category" class="form-select">
                                <option value="">جميع التصنيفات</option>
                                <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo $cat['id']; ?>" <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">المستوى</label>
                            <select name="level" class="form-select">
                                <option value="">جميع المستويات</option>
                                <option value="beginner" <?php echo $level === 'beginner' ? 'selected' : ''; ?>>مبتدئ</option>
                                <option value="intermediate" <?php echo $level === 'intermediate' ? 'selected' : ''; ?>>متوسط</option>
                                <option value="advanced" <?php echo $level === 'advanced' ? 'selected' : ''; ?>>متقدم</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">السعر</label>
                            <select name="price" class="form-select">
                                <option value="">جميع الأسعار</option>
                                <option value="free" <?php echo $price_filter === 'free' ? 'selected' : ''; ?>>مجاني</option>
                                <option value="paid" <?php echo $price_filter === 'paid' ? 'selected' : ''; ?>>مدفوع</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الترتيب</label>
                            <select name="sort" class="form-select">
                                <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                                <option value="oldest" <?php echo $sort === 'oldest' ? 'selected' : ''; ?>>الأقدم</option>
                                <option value="popular" <?php echo $sort === 'popular' ? 'selected' : ''; ?>>الأكثر شعبية</option>
                                <option value="price_low" <?php echo $sort === 'price_low' ? 'selected' : ''; ?>>السعر: منخفض إلى مرتفع</option>
                                <option value="price_high" <?php echo $sort === 'price_high' ? 'selected' : ''; ?>>السعر: مرتفع إلى منخفض</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-student-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- نتائج البحث -->
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">النتائج (<?php echo count($courses); ?> كورس)</h5>
            </div>

            <?php if (empty($courses)): ?>
            <div class="card-student text-center py-5">
                <div class="card-body">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد كورسات</h4>
                    <p class="text-muted">لم يتم العثور على كورسات تطابق معايير البحث</p>
                </div>
            </div>
            <?php else: ?>
            <div class="row">
                <?php foreach ($courses as $course): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card-student h-100">
                        <?php if ($course['thumbnail']): ?>
                        <img src="<?php echo htmlspecialchars($course['thumbnail']); ?>" 
                             class="card-img-top" style="height: 200px; object-fit: cover;">
                        <?php else: ?>
                        <div class="card-img-top bg-gradient-primary d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <i class="fas fa-graduation-cap fa-3x text-white"></i>
                        </div>
                        <?php endif; ?>
                        
                        <div class="card-body d-flex flex-column">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge bg-primary"><?php echo htmlspecialchars($course['category_name'] ?? 'عام'); ?></span>
                                <?php if ($course['is_enrolled']): ?>
                                <span class="badge bg-success">مسجل</span>
                                <?php endif; ?>
                            </div>
                            
                            <h5 class="card-title"><?php echo htmlspecialchars($course['title']); ?></h5>
                            <p class="card-text text-muted flex-grow-1">
                                <?php echo htmlspecialchars(substr($course['description'], 0, 100)) . '...'; ?>
                            </p>
                            
                            <div class="course-meta mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">المدرب:</span>
                                    <span><?php echo htmlspecialchars($course['instructor_name']); ?></span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">المستوى:</span>
                                    <span>
                                        <?php
                                        $level_text = [
                                            'beginner' => 'مبتدئ',
                                            'intermediate' => 'متوسط',
                                            'advanced' => 'متقدم'
                                        ];
                                        echo $level_text[$course['level']] ?? $course['level'];
                                        ?>
                                    </span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">الجلسات:</span>
                                    <span><?php echo $course['session_count']; ?> جلسة</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">الطلاب:</span>
                                    <span><?php echo $course['enrollment_count']; ?> طالب</span>
                                </div>
                                
                                <?php if ($course['avg_rating']): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="text-muted">التقييم:</span>
                                    <span>
                                        <?php
                                        $rating = round($course['avg_rating'], 1);
                                        for ($i = 1; $i <= 5; $i++) {
                                            if ($i <= $rating) {
                                                echo '<i class="fas fa-star text-warning"></i>';
                                            } else {
                                                echo '<i class="far fa-star text-warning"></i>';
                                            }
                                        }
                                        echo " ($rating)";
                                        ?>
                                    </span>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="price-section mb-3">
                                <?php if ($course['price'] > 0): ?>
                                <h4 class="text-primary mb-0"><?php echo number_format($course['price'], 2); ?> ر.س</h4>
                                <?php else: ?>
                                <h4 class="text-success mb-0">مجاني</h4>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <?php if ($course['is_enrolled']): ?>
                            <a href="course-view.php?id=<?php echo $course['id']; ?>" 
                               class="btn btn-success w-100">
                                <i class="fas fa-play me-1"></i>
                                متابعة الكورس
                            </a>
                            <?php else: ?>
                            <form method="POST" class="d-inline w-100">
                                <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                                <button type="submit" name="enroll_course" class="btn btn-student-primary w-100">
                                    <i class="fas fa-plus me-1"></i>
                                    <?php echo $course['price'] > 0 ? 'شراء الكورس' : 'انضم للكورس'; ?>
                                </button>
                            </form>
                            <?php endif; ?>
                            
                            <button class="btn btn-outline-primary w-100 mt-2" data-bs-toggle="modal" 
                                    data-bs-target="#courseModal<?php echo $course['id']; ?>">
                                <i class="fas fa-eye me-1"></i>
                                عرض التفاصيل
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modals تفاصيل الكورسات -->
<?php foreach ($courses as $course): ?>
<div class="modal fade" id="courseModal<?php echo $course['id']; ?>" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo htmlspecialchars($course['title']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="text-primary">وصف الكورس:</h6>
                        <p><?php echo nl2br(htmlspecialchars($course['description'])); ?></p>
                        
                        <?php if ($course['objectives']): ?>
                        <h6 class="text-primary">أهداف الكورس:</h6>
                        <p><?php echo nl2br(htmlspecialchars($course['objectives'])); ?></p>
                        <?php endif; ?>
                        
                        <?php if ($course['requirements']): ?>
                        <h6 class="text-primary">المتطلبات:</h6>
                        <p><?php echo nl2br(htmlspecialchars($course['requirements'])); ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-primary">معلومات الكورس:</h6>
                        <ul class="list-unstyled">
                            <li><strong>المدرب:</strong> <?php echo htmlspecialchars($course['instructor_name']); ?></li>
                            <li><strong>التصنيف:</strong> <?php echo htmlspecialchars($course['category_name'] ?? 'عام'); ?></li>
                            <li><strong>المستوى:</strong> 
                                <?php
                                $level_text = [
                                    'beginner' => 'مبتدئ',
                                    'intermediate' => 'متوسط', 
                                    'advanced' => 'متقدم'
                                ];
                                echo $level_text[$course['level']] ?? $course['level'];
                                ?>
                            </li>
                            <li><strong>المدة:</strong> <?php echo $course['duration_hours']; ?> ساعة</li>
                            <li><strong>عدد الجلسات:</strong> <?php echo $course['session_count']; ?></li>
                            <li><strong>عدد الطلاب:</strong> <?php echo $course['enrollment_count']; ?></li>
                            <li><strong>السعر:</strong> 
                                <?php if ($course['price'] > 0): ?>
                                    <?php echo number_format($course['price'], 2); ?> ر.س
                                <?php else: ?>
                                    مجاني
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <?php if (!$course['is_enrolled']): ?>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                    <button type="submit" name="enroll_course" class="btn btn-student-primary">
                        <i class="fas fa-plus me-1"></i>
                        <?php echo $course['price'] > 0 ? 'شراء الكورس' : 'انضم للكورس'; ?>
                    </button>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>

<?php include 'includes/footer.php'; ?>
