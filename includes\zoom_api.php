<?php
/**
 * تكامل Zoom API محسن
 * Enhanced Zoom API Integration
 * =============================
 */

require_once 'config/config.php';

class ZoomAPI {
    private $apiKey;
    private $apiSecret;
    private $baseUrl;
    private $jwt;
    private $jwtExpire;
    
    public function __construct() {
        $this->apiKey = ZOOM_API_KEY;
        $this->apiSecret = ZOOM_API_SECRET;
        $this->baseUrl = ZOOM_BASE_URL;
        $this->jwtExpire = ZOOM_JWT_EXPIRE;
        
        if (empty($this->apiKey) || empty($this->apiSecret)) {
            throw new Exception('Zoom API credentials not configured');
        }
    }
    
    /**
     * إنشاء JWT Token للمصادقة
     */
    private function generateJWT() {
        if ($this->jwt && $this->isJWTValid()) {
            return $this->jwt;
        }
        
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'iss' => $this->apiKey,
            'exp' => time() + $this->jwtExpire,
            'iat' => time(),
            'aud' => 'zoom'
        ]);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->apiSecret, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        $this->jwt = $base64Header . "." . $base64Payload . "." . $base64Signature;
        return $this->jwt;
    }
    
    /**
     * التحقق من صحة JWT Token
     */
    private function isJWTValid() {
        if (!$this->jwt) return false;
        
        $parts = explode('.', $this->jwt);
        if (count($parts) !== 3) return false;
        
        $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[1])), true);
        return isset($payload['exp']) && $payload['exp'] > time();
    }
    
    /**
     * إرسال طلب HTTP إلى Zoom API
     */
    private function makeRequest($method, $endpoint, $data = null) {
        $url = $this->baseUrl . $endpoint;
        $jwt = $this->generateJWT();
        
        $headers = [
            'Authorization: Bearer ' . $jwt,
            'Content-Type: application/json',
            'User-Agent: ZoomLearningPlatform/2.0'
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);
        
        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
            case 'PATCH':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception("cURL Error: " . $error);
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = isset($decodedResponse['message']) ? 
                $decodedResponse['message'] : 
                "HTTP Error: " . $httpCode;
            throw new Exception($errorMessage, $httpCode);
        }
        
        return $decodedResponse;
    }
    
    /**
     * إنشاء اجتماع جديد
     */
    public function createMeeting($data) {
        $defaultSettings = [
            'type' => 2, // Scheduled meeting
            'duration' => 60,
            'timezone' => 'Asia/Riyadh',
            'password' => $this->generateMeetingPassword(),
            'settings' => [
                'host_video' => true,
                'participant_video' => true,
                'cn_meeting' => false,
                'in_meeting' => false,
                'join_before_host' => false,
                'mute_upon_entry' => true,
                'watermark' => false,
                'use_pmi' => false,
                'approval_type' => 0,
                'audio' => 'both',
                'auto_recording' => 'none',
                'enforce_login' => false,
                'enforce_login_domains' => '',
                'alternative_hosts' => '',
                'close_registration' => false,
                'show_share_button' => true,
                'allow_multiple_devices' => true,
                'registrants_confirmation_email' => true,
                'waiting_room' => true,
                'request_permission_to_unmute_participants' => false,
                'global_dial_in_countries' => ['SA'],
                'registration_type' => 1
            ]
        ];
        
        $meetingData = array_merge($defaultSettings, $data);
        
        // التحقق من صحة البيانات المطلوبة
        if (empty($meetingData['topic'])) {
            throw new Exception('Meeting topic is required');
        }
        
        if (empty($meetingData['start_time'])) {
            throw new Exception('Meeting start time is required');
        }
        
        // تنسيق وقت البداية
        if (!is_string($meetingData['start_time'])) {
            $meetingData['start_time'] = date('Y-m-d\TH:i:s\Z', $meetingData['start_time']);
        }
        
        try {
            $response = $this->makeRequest('POST', '/users/me/meetings', $meetingData);
            
            // حفظ معلومات الاجتماع في قاعدة البيانات
            $this->saveMeetingToDatabase($response);
            
            return $response;
        } catch (Exception $e) {
            error_log("Zoom API Error - Create Meeting: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * الحصول على معلومات اجتماع
     */
    public function getMeeting($meetingId) {
        try {
            return $this->makeRequest('GET', "/meetings/{$meetingId}");
        } catch (Exception $e) {
            error_log("Zoom API Error - Get Meeting: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث اجتماع
     */
    public function updateMeeting($meetingId, $data) {
        try {
            return $this->makeRequest('PATCH', "/meetings/{$meetingId}", $data);
        } catch (Exception $e) {
            error_log("Zoom API Error - Update Meeting: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف اجتماع
     */
    public function deleteMeeting($meetingId) {
        try {
            $response = $this->makeRequest('DELETE', "/meetings/{$meetingId}");
            
            // حذف من قاعدة البيانات
            $this->deleteMeetingFromDatabase($meetingId);
            
            return $response;
        } catch (Exception $e) {
            error_log("Zoom API Error - Delete Meeting: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * الحصول على قائمة الاجتماعات
     */
    public function listMeetings($userId = 'me', $type = 'scheduled') {
        try {
            $params = [
                'type' => $type,
                'page_size' => 30
            ];
            
            $queryString = http_build_query($params);
            return $this->makeRequest('GET', "/users/{$userId}/meetings?{$queryString}");
        } catch (Exception $e) {
            error_log("Zoom API Error - List Meetings: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * الحصول على تقرير الاجتماع
     */
    public function getMeetingReport($meetingId) {
        try {
            return $this->makeRequest('GET', "/report/meetings/{$meetingId}");
        } catch (Exception $e) {
            error_log("Zoom API Error - Get Meeting Report: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * الحصول على المشاركين في الاجتماع
     */
    public function getMeetingParticipants($meetingId) {
        try {
            return $this->makeRequest('GET', "/report/meetings/{$meetingId}/participants");
        } catch (Exception $e) {
            error_log("Zoom API Error - Get Meeting Participants: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * إنشاء كلمة مرور للاجتماع
     */
    private function generateMeetingPassword($length = 8) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $password;
    }
    
    /**
     * حفظ معلومات الاجتماع في قاعدة البيانات
     */
    private function saveMeetingToDatabase($meetingData) {
        global $conn;
        
        try {
            $stmt = $conn->prepare("
                INSERT INTO zoom_meetings (
                    meeting_id, topic, start_time, duration, 
                    join_url, password, host_id, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                topic = VALUES(topic),
                start_time = VALUES(start_time),
                duration = VALUES(duration),
                join_url = VALUES(join_url),
                password = VALUES(password),
                updated_at = NOW()
            ");
            
            $stmt->execute([
                $meetingData['id'],
                $meetingData['topic'],
                $meetingData['start_time'],
                $meetingData['duration'],
                $meetingData['join_url'],
                $meetingData['password'] ?? '',
                $meetingData['host_id'] ?? ''
            ]);
        } catch (PDOException $e) {
            error_log("Database Error - Save Meeting: " . $e->getMessage());
        }
    }
    
    /**
     * حذف الاجتماع من قاعدة البيانات
     */
    private function deleteMeetingFromDatabase($meetingId) {
        global $conn;
        
        try {
            $stmt = $conn->prepare("DELETE FROM zoom_meetings WHERE meeting_id = ?");
            $stmt->execute([$meetingId]);
        } catch (PDOException $e) {
            error_log("Database Error - Delete Meeting: " . $e->getMessage());
        }
    }
    
    /**
     * التحقق من حالة الاتصال مع Zoom API
     */
    public function testConnection() {
        try {
            $response = $this->makeRequest('GET', '/users/me');
            return [
                'success' => true,
                'message' => 'Connection successful',
                'user' => $response
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode()
            ];
        }
    }
    
    /**
     * إنشاء webhook للاجتماع
     */
    public function createWebhook($events = ['meeting.started', 'meeting.ended']) {
        $webhookData = [
            'url' => SITE_URL . '/api/zoom-webhook.php',
            'auth_user' => 'zoom_webhook',
            'auth_password' => hash('sha256', ZOOM_WEBHOOK_SECRET),
            'events' => $events
        ];
        
        try {
            return $this->makeRequest('POST', '/webhooks', $webhookData);
        } catch (Exception $e) {
            error_log("Zoom API Error - Create Webhook: " . $e->getMessage());
            throw $e;
        }
    }
}

// إنشاء جدول zoom_meetings إذا لم يكن موجوداً
function createZoomMeetingsTable() {
    global $conn;
    
    try {
        $sql = "CREATE TABLE IF NOT EXISTS zoom_meetings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            meeting_id BIGINT NOT NULL UNIQUE,
            topic VARCHAR(255) NOT NULL,
            start_time DATETIME NOT NULL,
            duration INT DEFAULT 60,
            join_url VARCHAR(500) NOT NULL,
            password VARCHAR(50),
            host_id VARCHAR(100),
            status ENUM('scheduled', 'started', 'ended', 'cancelled') DEFAULT 'scheduled',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_meeting_id (meeting_id),
            INDEX idx_start_time (start_time),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($sql);
    } catch (PDOException $e) {
        error_log("Error creating zoom_meetings table: " . $e->getMessage());
    }
}

// إنشاء الجدول عند تحميل الملف
createZoomMeetingsTable();

// دالة مساعدة لإنشاء اجتماع سريع
function createQuickZoomMeeting($topic, $startTime, $duration = 60, $instructorId = null) {
    try {
        $zoom = new ZoomAPI();
        
        $meetingData = [
            'topic' => $topic,
            'start_time' => $startTime,
            'duration' => $duration,
            'settings' => [
                'waiting_room' => true,
                'mute_upon_entry' => true,
                'approval_type' => 0
            ]
        ];
        
        return $zoom->createMeeting($meetingData);
    } catch (Exception $e) {
        error_log("Quick Zoom Meeting Error: " . $e->getMessage());
        return false;
    }
}

// دالة للتحقق من إعدادات Zoom
function validateZoomSettings() {
    if (empty(ZOOM_API_KEY) || ZOOM_API_KEY === 'YOUR_ZOOM_API_KEY') {
        return [
            'valid' => false,
            'message' => 'Zoom API Key not configured'
        ];
    }
    
    if (empty(ZOOM_API_SECRET) || ZOOM_API_SECRET === 'YOUR_ZOOM_API_SECRET') {
        return [
            'valid' => false,
            'message' => 'Zoom API Secret not configured'
        ];
    }
    
    try {
        $zoom = new ZoomAPI();
        $test = $zoom->testConnection();
        
        return [
            'valid' => $test['success'],
            'message' => $test['message']
        ];
    } catch (Exception $e) {
        return [
            'valid' => false,
            'message' => $e->getMessage()
        ];
    }
}
?>
