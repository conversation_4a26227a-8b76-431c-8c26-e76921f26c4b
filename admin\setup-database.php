<?php
session_start();

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

echo "<h1>إعداد قاعدة البيانات</h1>";

try {
    require_once '../config/database.php';
    
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</div><br>";
    
    // إنشاء جدول المدفوعات
    echo "<h2>إنشاء جدول المدفوعات...</h2>";
    $conn->exec("CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        course_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        commission_amount DECIMAL(10,2) DEFAULT 0,
        payment_method ENUM('credit_card', 'paypal', 'bank_transfer', 'cash') DEFAULT 'credit_card',
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
        transaction_id VARCHAR(255) NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user (user_id),
        INDEX idx_course (course_id),
        INDEX idx_status (status),
        INDEX idx_payment_date (payment_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<div style='color: green;'>✅ تم إنشاء جدول المدفوعات</div>";
    
    // إنشاء جدول الفئات
    echo "<h2>إنشاء جدول الفئات...</h2>";
    $conn->exec("CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT NULL,
        icon VARCHAR(100) NULL,
        color VARCHAR(7) DEFAULT '#007bff',
        status ENUM('active', 'inactive') DEFAULT 'active',
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_sort (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<div style='color: green;'>✅ تم إنشاء جدول الفئات</div>";
    
    // إنشاء جدول فئات الوجبات
    echo "<h2>إنشاء جدول فئات الوجبات...</h2>";
    $conn->exec("CREATE TABLE IF NOT EXISTS meal_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT NULL,
        icon VARCHAR(100) NULL,
        color VARCHAR(7) DEFAULT '#28a745',
        status ENUM('active', 'inactive') DEFAULT 'active',
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_sort (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<div style='color: green;'>✅ تم إنشاء جدول فئات الوجبات</div>";
    
    // إنشاء جدول الوجبات
    echo "<h2>إنشاء جدول الوجبات...</h2>";
    $conn->exec("CREATE TABLE IF NOT EXISTS meals (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT NULL,
        image VARCHAR(500) NULL,
        meal_type ENUM('breakfast', 'lunch', 'dinner', 'snack', 'dessert') NOT NULL,
        category_id INT NULL,
        calories INT DEFAULT 0,
        protein DECIMAL(5,2) DEFAULT 0,
        carbs DECIMAL(5,2) DEFAULT 0,
        fat DECIMAL(5,2) DEFAULT 0,
        fiber DECIMAL(5,2) DEFAULT 0,
        sugar DECIMAL(5,2) DEFAULT 0,
        sodium DECIMAL(5,2) DEFAULT 0,
        prep_time INT DEFAULT 0,
        cook_time INT DEFAULT 0,
        total_time INT DEFAULT 0,
        servings INT DEFAULT 1,
        difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'easy',
        rating DECIMAL(3,2) DEFAULT 0,
        total_reviews INT DEFAULT 0,
        ingredients TEXT NULL,
        instructions TEXT NULL,
        tips TEXT NULL,
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        created_by INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_meal_type (meal_type),
        INDEX idx_status (status),
        INDEX idx_rating (rating)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<div style='color: green;'>✅ تم إنشاء جدول الوجبات</div>";
    
    // إنشاء جدول رفع الملفات
    echo "<h2>إنشاء جدول رفع الملفات...</h2>";
    $conn->exec("CREATE TABLE IF NOT EXISTS file_uploads (
        id INT AUTO_INCREMENT PRIMARY KEY,
        original_name VARCHAR(255) NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        mime_type VARCHAR(100) NOT NULL,
        category ENUM('image', 'video', 'document', 'audio', 'other') DEFAULT 'other',
        uploaded_by INT NULL,
        related_type ENUM('course', 'meal', 'user', 'assignment', 'other') NULL,
        related_id INT NULL,
        status ENUM('active', 'deleted') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_category (category),
        INDEX idx_related (related_type, related_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "<div style='color: green;'>✅ تم إنشاء جدول رفع الملفات</div>";

    // إنشاء جدول طلبات الانضمام
    echo "<h2>إنشاء جدول طلبات الانضمام...</h2>";
    $conn->exec("CREATE TABLE IF NOT EXISTS join_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NULL,
        requested_role ENUM('student', 'instructor') DEFAULT 'student',
        message TEXT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        processed_by INT NULL,
        processed_at TIMESTAMP NULL,
        rejection_reason TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_status (status),
        INDEX idx_created (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // إضافة عمود requested_role إذا لم يكن موجوداً
    try {
        $conn->exec("ALTER TABLE join_requests ADD COLUMN requested_role ENUM('student', 'instructor') DEFAULT 'student' AFTER phone");
        echo "<div style='color: blue;'>➕ تم إضافة عمود requested_role</div>";
    } catch (PDOException $e) {
        echo "<div style='color: orange;'>⚠️ عمود requested_role موجود مسبقاً</div>";
    }
    echo "<div style='color: green;'>✅ تم إنشاء جدول طلبات الانضمام</div>";

    // تحديث جدول الكورسات
    echo "<h2>تحديث جدول الكورسات...</h2>";
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN category_id INT NULL AFTER instructor_id");
        echo "<div style='color: blue;'>➕ تم إضافة عمود category_id</div>";
    } catch (PDOException $e) {
        echo "<div style='color: orange;'>⚠️ عمود category_id موجود مسبقاً</div>";
    }
    
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN short_description TEXT NULL AFTER description");
        echo "<div style='color: blue;'>➕ تم إضافة عمود short_description</div>";
    } catch (PDOException $e) {
        echo "<div style='color: orange;'>⚠️ عمود short_description موجود مسبقاً</div>";
    }
    
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN thumbnail VARCHAR(500) NULL AFTER short_description");
        echo "<div style='color: blue;'>➕ تم إضافة عمود thumbnail</div>";
    } catch (PDOException $e) {
        echo "<div style='color: orange;'>⚠️ عمود thumbnail موجود مسبقاً</div>";
    }
    
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN is_free BOOLEAN DEFAULT TRUE AFTER price");
        echo "<div style='color: blue;'>➕ تم إضافة عمود is_free</div>";
    } catch (PDOException $e) {
        echo "<div style='color: orange;'>⚠️ عمود is_free موجود مسبقاً</div>";
    }
    
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN is_featured BOOLEAN DEFAULT FALSE AFTER is_free");
        echo "<div style='color: blue;'>➕ تم إضافة عمود is_featured</div>";
    } catch (PDOException $e) {
        echo "<div style='color: orange;'>⚠️ عمود is_featured موجود مسبقاً</div>";
    }
    
    // إضافة بيانات تجريبية للفئات
    echo "<h2>إضافة بيانات تجريبية...</h2>";
    
    // فئات الكورسات
    $categories = [
        ['البرمجة', 'كورسات البرمجة وتطوير البرمجيات', 'fas fa-code', '#007bff'],
        ['التصميم', 'كورسات التصميم الجرافيكي وتصميم المواقع', 'fas fa-paint-brush', '#28a745'],
        ['التسويق', 'كورسات التسويق الرقمي والتقليدي', 'fas fa-bullhorn', '#ffc107'],
        ['الأعمال', 'كورسات إدارة الأعمال وريادة الأعمال', 'fas fa-briefcase', '#dc3545'],
        ['اللغات', 'كورسات تعلم اللغات المختلفة', 'fas fa-language', '#6f42c1']
    ];
    
    foreach ($categories as $index => $category) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO categories (name, description, icon, color, sort_order) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$category[0], $category[1], $category[2], $category[3], $index + 1]);
            echo "<div style='color: blue;'>➕ تم إضافة فئة: {$category[0]}</div>";
        } catch (PDOException $e) {
            echo "<div style='color: orange;'>⚠️ فئة {$category[0]} موجودة مسبقاً</div>";
        }
    }
    
    // فئات الوجبات
    $mealCategories = [
        ['صحية', 'وجبات صحية ومتوازنة', 'fas fa-heart', '#28a745'],
        ['نباتية', 'وجبات نباتية خالية من اللحوم', 'fas fa-leaf', '#198754'],
        ['بروتين عالي', 'وجبات غنية بالبروتين', 'fas fa-dumbbell', '#fd7e14'],
        ['قليلة السعرات', 'وجبات منخفضة السعرات الحرارية', 'fas fa-weight-hanging', '#6f42c1'],
        ['سريعة التحضير', 'وجبات سهلة وسريعة التحضير', 'fas fa-clock', '#dc3545']
    ];
    
    foreach ($mealCategories as $index => $category) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO meal_categories (name, description, icon, color, sort_order) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$category[0], $category[1], $category[2], $category[3], $index + 1]);
            echo "<div style='color: blue;'>➕ تم إضافة فئة وجبة: {$category[0]}</div>";
        } catch (PDOException $e) {
            echo "<div style='color: orange;'>⚠️ فئة وجبة {$category[0]} موجودة مسبقاً</div>";
        }
    }
    
    // إضافة بعض الوجبات التجريبية
    $meals = [
        ['سلطة الكينوا', 'سلطة صحية غنية بالبروتين والألياف', 'breakfast', 1, 320, 12, 45, 8],
        ['دجاج مشوي بالخضار', 'وجبة متكاملة من الدجاج المشوي مع الخضار الطازجة', 'lunch', 1, 450, 35, 20, 15],
        ['سمك السلمون المشوي', 'سمك السلمون الغني بالأوميجا 3', 'dinner', 1, 380, 28, 5, 22],
        ['سموثي الفواكه', 'مشروب صحي من الفواكه الطازجة', 'snack', 2, 180, 4, 35, 2]
    ];
    
    foreach ($meals as $meal) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO meals (name, description, meal_type, category_id, calories, protein, carbs, fat, total_time, servings, difficulty, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 30, 2, 'easy', 'published', 1)");
            $stmt->execute($meal);
            echo "<div style='color: blue;'>➕ تم إضافة وجبة: {$meal[0]}</div>";
        } catch (PDOException $e) {
            echo "<div style='color: orange;'>⚠️ وجبة {$meal[0]} موجودة مسبقاً</div>";
        }
    }

    // إضافة طلبات انضمام تجريبية
    echo "<h2>إضافة طلبات انضمام تجريبية...</h2>";
    $joinRequests = [
        ['أحمد علي محمد', '<EMAIL>', '0501234567', 'student', 'أرغب في الانضمام لتعلم البرمجة', 'pending'],
        ['فاطمة سالم', '<EMAIL>', '0507654321', 'instructor', 'مهتمة بتدريس كورسات التصميم', 'pending'],
        ['محمد عبدالله', '<EMAIL>', '0509876543', 'student', 'أريد تعلم التسويق الرقمي', 'approved'],
        ['نورا أحمد', '<EMAIL>', '0502468135', 'instructor', 'مهتمة بتدريس اللغات', 'approved'],
        ['خالد محمد', '<EMAIL>', '0508642097', 'student', 'أرغب في تعلم إدارة الأعمال', 'rejected'],
        ['سارة عبدالرحمن', '<EMAIL>', '0503691472', 'student', 'مهتمة بالكورسات الصحية', 'pending'],
        ['عبدالعزيز سعد', '<EMAIL>', '0505827394', 'instructor', 'أريد تدريس التصوير', 'pending']
    ];

    foreach ($joinRequests as $request) {
        try {
            // تحديد معالج الطلب للطلبات المعتمدة/المرفوضة
            $processedBy = null;
            $processedAt = null;
            $rejectionReason = null;

            if ($request[5] === 'approved') {
                $processedBy = 1; // المدير
                $processedAt = date('Y-m-d H:i:s', strtotime('-' . rand(1, 10) . ' days'));
            } elseif ($request[5] === 'rejected') {
                $processedBy = 1; // المدير
                $processedAt = date('Y-m-d H:i:s', strtotime('-' . rand(1, 10) . ' days'));
                $rejectionReason = 'لا يستوفي المتطلبات المطلوبة';
            }

            $createdAt = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));

            $stmt = $conn->prepare("INSERT IGNORE INTO join_requests (name, email, phone, requested_role, message, status, processed_by, processed_at, rejection_reason, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $request[0],
                $request[1],
                $request[2],
                $request[3],
                $request[4],
                $request[5],
                $processedBy,
                $processedAt,
                $rejectionReason,
                $createdAt
            ]);
            echo "<div style='color: blue;'>➕ تم إضافة طلب انضمام: {$request[0]} ({$request[4]})</div>";
        } catch (PDOException $e) {
            echo "<div style='color: orange;'>⚠️ طلب انضمام {$request[0]} موجود مسبقاً</div>";
        }
    }

    echo "<div style='color: green; font-weight: bold; margin-top: 20px;'>🎉 تم إعداد قاعدة البيانات بنجاح!</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div style='color: red;'>📍 التفاصيل: " . $e->getTraceAsString() . "</div>";
}

echo "<br><a href='check-tables.php'>فحص الجداول</a> | <a href='analytics.php'>التحليلات</a> | <a href='dashboard.php'>لوحة التحكم</a>";
?>
