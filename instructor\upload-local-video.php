<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'رفع فيديو من الجهاز';

// معالجة رفع الفيديو
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $course_id = $_POST['course_id'] ?? 0;
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $is_free = isset($_POST['is_free']) ? 1 : 0;
    
    $errors = [];
    
    // التحقق من البيانات
    if (empty($title)) {
        $errors[] = 'عنوان الفيديو مطلوب';
    }
    
    if (empty($course_id)) {
        $errors[] = 'يجب اختيار كورس';
    }
    
    // التحقق من الملف المرفوع
    if (!isset($_FILES['video_file']) || $_FILES['video_file']['error'] !== UPLOAD_ERR_OK) {
        if (isset($_FILES['video_file'])) {
            switch ($_FILES['video_file']['error']) {
                case UPLOAD_ERR_INI_SIZE:
                case UPLOAD_ERR_FORM_SIZE:
                    $errors[] = 'حجم الملف كبير جداً';
                    break;
                case UPLOAD_ERR_PARTIAL:
                    $errors[] = 'تم رفع جزء من الملف فقط';
                    break;
                case UPLOAD_ERR_NO_FILE:
                    $errors[] = 'لم يتم اختيار ملف';
                    break;
                default:
                    $errors[] = 'حدث خطأ أثناء رفع الملف';
            }
        } else {
            $errors[] = 'يجب اختيار ملف فيديو';
        }
    } else {
        $file = $_FILES['video_file'];
        $allowed_extensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
        $max_size = 1000 * 1024 * 1024; // 1 GB
        
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($file_extension, $allowed_extensions)) {
            $errors[] = 'نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', $allowed_extensions);
        }
        
        if ($file['size'] > $max_size) {
            $errors[] = 'حجم الملف كبير جداً. الحد الأقصى 1 جيجابايت. حجم الملف الحالي: ' . round($file['size'] / (1024*1024), 2) . ' ميجابايت';
        }
    }
    
    if (empty($errors)) {
        try {
            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id, title FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);
            $course = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($course) {
                // التأكد من وجود مجلد الرفع
                $upload_dir = '../uploads/course_videos/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                // إنشاء اسم ملف فريد
                $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                $file_name = 'course_' . $course_id . '_' . time() . '_' . uniqid() . '.' . $file_extension;
                $upload_path = $upload_dir . $file_name;
                
                // رفع الملف
                if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                    // التحقق من وجود جدول course_videos
                    $stmt = $conn->query("SHOW TABLES LIKE 'course_videos'");
                    if ($stmt->rowCount() == 0) {
                        // إنشاء الجدول إذا لم يكن موجود
                        $conn->exec("CREATE TABLE course_videos (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            course_id INT NOT NULL,
                            title VARCHAR(255) NOT NULL,
                            description TEXT,
                            video_url VARCHAR(500) NOT NULL,
                            video_file_path VARCHAR(500) NULL,
                            file_size BIGINT DEFAULT NULL,
                            duration INT DEFAULT 0,
                            is_free TINYINT(1) DEFAULT 0,
                            uploaded_by INT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_course_id (course_id),
                            INDEX idx_uploaded_by (uploaded_by)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
                    }
                    
                    // التحقق من وجود الأعمدة المطلوبة
                    $stmt = $conn->query("DESCRIBE course_videos");
                    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    
                    // إضافة الأعمدة المفقودة
                    if (!in_array('video_file_path', $columns)) {
                        $conn->exec("ALTER TABLE course_videos ADD COLUMN video_file_path VARCHAR(500) NULL");
                    }
                    if (!in_array('file_size', $columns)) {
                        $conn->exec("ALTER TABLE course_videos ADD COLUMN file_size BIGINT DEFAULT NULL");
                    }
                    
                    // إدراج البيانات في قاعدة البيانات
                    $video_url = 'uploads/course_videos/' . $file_name;
                    $stmt = $conn->prepare("
                        INSERT INTO course_videos 
                        (course_id, title, description, video_url, video_file_path, file_size, is_free, uploaded_by) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $course_id, 
                        $title, 
                        $description, 
                        $video_url,
                        $video_url,
                        $file['size'],
                        $is_free, 
                        $_SESSION['user_id']
                    ]);
                    
                    $success_message = 'تم رفع الفيديو بنجاح! اسم الملف: ' . $file_name;
                    
                    // إعادة توجيه لصفحة الفيديوهات
                    header('Location: videos.php?success=1&uploaded=' . urlencode($title));
                    exit;
                } else {
                    $error_message = 'فشل في رفع الملف. تأكد من صلاحيات المجلد.';
                }
            } else {
                $error_message = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage();
        } catch (Exception $e) {
            $error_message = 'حدث خطأ غير متوقع: ' . $e->getMessage();
        }
    }
}

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="h3 mb-2">
                        <i class="fas fa-cloud-upload-alt text-primary me-2"></i>
                        رفع فيديو من الجهاز
                    </h2>
                    <p class="text-muted mb-0">رفع ملف فيديو من جهازك مباشرة للموقع</p>
                </div>
                <div>
                    <a href="videos.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للفيديوهات
                    </a>
                </div>
            </div>

            <!-- تحذير حول حجم الملف -->
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة مهمة:</strong> 
                الحد الأقصى لحجم الملف هو 1 جيجابايت. 
                الأنواع المدعومة: MP4, AVI, MOV, WMV, FLV, WebM, MKV
            </div>

            <!-- رسائل النجاح والخطأ -->
            <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <!-- نموذج رفع الفيديو -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>
                        رفع ملف الفيديو
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="uploadForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                                <select name="course_id" id="course_id" class="form-select" required>
                                    <option value="">اختر الكورس</option>
                                    <?php foreach ($instructor_courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>" 
                                            <?php echo (isset($_POST['course_id']) && $_POST['course_id'] == $course['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input type="checkbox" name="is_free" id="is_free" class="form-check-input"
                                           <?php echo (isset($_POST['is_free'])) ? 'checked' : ''; ?>>
                                    <label for="is_free" class="form-check-label">
                                        <i class="fas fa-unlock text-success me-1"></i>
                                        فيديو مجاني
                                    </label>
                                </div>
                            </div>
                            <div class="col-12">
                                <label for="title" class="form-label">عنوان الفيديو <span class="text-danger">*</span></label>
                                <input type="text" name="title" id="title" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" required>
                            </div>
                            <div class="col-12">
                                <label for="description" class="form-label">وصف الفيديو</label>
                                <textarea name="description" id="description" class="form-control" rows="3"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                            </div>
                            <div class="col-12">
                                <label for="video_file" class="form-label">ملف الفيديو <span class="text-danger">*</span></label>
                                <input type="file" name="video_file" id="video_file" class="form-control" 
                                       accept="video/*" required onchange="validateFile()">
                                <div class="form-text">
                                    <i class="fas fa-info-circle text-info me-1"></i>
                                    الأنواع المدعومة: MP4, AVI, MOV, WMV, FLV, WebM, MKV | الحد الأقصى: 1 جيجابايت
                                </div>
                                <div id="file_info" class="mt-2"></div>
                            </div>
                            <div class="col-12">
                                <div class="progress" id="upload_progress" style="display: none;">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="videos.php" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary" id="upload_btn">
                                <i class="fas fa-cloud-upload-alt me-1"></i>رفع الفيديو
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة الملف
function validateFile() {
    const fileInput = document.getElementById('video_file');
    const fileInfo = document.getElementById('file_info');
    const uploadBtn = document.getElementById('upload_btn');
    
    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        const maxSize = 1000 * 1024 * 1024; // 1 GB
        const allowedExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
        
        let isValid = true;
        let info = '<div class="alert alert-info small">';
        
        // معلومات الملف
        info += '<strong>اسم الملف:</strong> ' + file.name + '<br>';
        info += '<strong>الحجم:</strong> ' + (file.size / (1024*1024)).toFixed(2) + ' ميجابايت<br>';
        info += '<strong>النوع:</strong> ' + file.type + '<br>';
        
        // التحقق من النوع
        const fileExtension = file.name.split('.').pop().toLowerCase();
        if (!allowedExtensions.includes(fileExtension)) {
            info += '<span class="text-danger"><i class="fas fa-times me-1"></i>نوع الملف غير مدعوم</span><br>';
            isValid = false;
        } else {
            info += '<span class="text-success"><i class="fas fa-check me-1"></i>نوع الملف مدعوم</span><br>';
        }
        
        // التحقق من الحجم
        if (file.size > maxSize) {
            info += '<span class="text-danger"><i class="fas fa-times me-1"></i>حجم الملف كبير جداً</span>';
            isValid = false;
        } else {
            info += '<span class="text-success"><i class="fas fa-check me-1"></i>حجم الملف مناسب</span>';
        }
        
        info += '</div>';
        fileInfo.innerHTML = info;
        uploadBtn.disabled = !isValid;
        
        if (isValid) {
            uploadBtn.innerHTML = '<i class="fas fa-cloud-upload-alt me-1"></i>رفع الفيديو - جاهز!';
            uploadBtn.classList.remove('btn-secondary');
            uploadBtn.classList.add('btn-primary');
        } else {
            uploadBtn.innerHTML = '<i class="fas fa-times me-1"></i>ملف غير صالح';
            uploadBtn.classList.remove('btn-primary');
            uploadBtn.classList.add('btn-secondary');
        }
    }
}

// تقدم الرفع
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    const uploadBtn = document.getElementById('upload_btn');
    const progress = document.getElementById('upload_progress');
    
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الرفع...';
    progress.style.display = 'block';
    
    // محاكاة تقدم الرفع
    let width = 0;
    const interval = setInterval(() => {
        width += Math.random() * 5;
        if (width >= 95) {
            clearInterval(interval);
        }
        progress.querySelector('.progress-bar').style.width = Math.min(width, 95) + '%';
    }, 500);
});
</script>

<?php include 'includes/footer.php'; ?>
