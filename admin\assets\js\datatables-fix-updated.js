
/**
 * إصلاح شامل لمشاكل DataTables
 * يحل مشكلة "Cannot reinitialise DataTable"
 */

// منع إعادة تهيئة DataTables
$.fn.dataTable.ext.errMode = "none";

// دالة آمنة لتهيئة DataTable
function initSafeDataTable(selector, options = {}) {
    try {
        // التحقق من وجود العنصر
        if (!$(selector).length) {
            console.warn("Element not found: " + selector);
            return null;
        }
        
        // تدمير DataTable الموجود إن وجد
        if ($.fn.DataTable.isDataTable(selector)) {
            $(selector).DataTable().destroy();
            console.log("Destroyed existing DataTable: " + selector);
        }
        
        // الإعدادات الافتراضية
        const defaultOptions = {
            responsive: true,
            pageLength: 25,
            autoWidth: false,
            processing: false,
            serverSide: false,
            stateSave: false,
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                info: "عرض _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                infoEmpty: "عرض 0 إلى 0 من أصل 0 مدخل",
                infoFiltered: "(مرشح من _MAX_ إجمالي المدخلات)",
                lengthMenu: "عرض _MENU_ مدخلات",
                loadingRecords: "جاري التحميل...",
                processing: "جاري المعالجة...",
                search: "البحث:",
                zeroRecords: "لم يتم العثور على نتائج مطابقة",
                paginate: {
                    first: "الأول",
                    last: "الأخير", 
                    next: "التالي",
                    previous: "السابق"
                }
            }
        };
        
        // دمج الإعدادات
        const finalOptions = $.extend(true, {}, defaultOptions, options);
        
        // إنشاء DataTable جديد
        const table = $(selector).DataTable(finalOptions);
        console.log("DataTable initialized successfully: " + selector);
        
        return table;
        
    } catch (error) {
        console.error("Error initializing DataTable:", error);
        return null;
    }
}

// تهيئة تلقائية عند تحميل الصفحة
$(document).ready(function() {
    // تأخير قصير للتأكد من تحميل جميع العناصر
    setTimeout(function() {
        
        // جدول المستخدمين
        if ($("#usersTable").length) {
            initSafeDataTable("#usersTable", {
                order: [[4, "desc"]],
                columnDefs: [
                    { orderable: false, targets: [-1] }
                ]
            });
        }
        
        // جدول المدربين
        if ($("#instructorsTable").length) {
            initSafeDataTable("#instructorsTable", {
                order: [[7, "desc"]],
                columnDefs: [
                    { orderable: false, targets: [8] }
                ]
            });
        }
        
        // جدول الطلاب
        if ($("#studentsTable").length) {
            initSafeDataTable("#studentsTable", {
                order: [[4, "desc"]],
                columnDefs: [
                    { orderable: false, targets: [5] }
                ]
            });
        }
        
        // جدول الكورسات
        if ($("#coursesTable").length) {
            initSafeDataTable("#coursesTable", {
                order: [[5, "desc"]],
                columnDefs: [
                    { orderable: false, targets: [-1] }
                ]
            });
        }
        
        // جدول طلبات الانضمام
        if ($("#requestsTable").length) {
            initSafeDataTable("#requestsTable", {
                order: [[6, "desc"]],
                columnDefs: [
                    { orderable: false, targets: [7] }
                ]
            });
        }
        
        // جداول أخرى
        $(".data-table, .datatable").each(function() {
            const tableId = "#" + $(this).attr("id");
            if ($(this).attr("id") && !$.fn.DataTable.isDataTable(tableId)) {
                initSafeDataTable(tableId);
            }
        });
        
    }, 300);
});

// تنظيف عند مغادرة الصفحة
$(window).on("beforeunload", function() {
    $(".dataTable").each(function() {
        if ($.fn.DataTable.isDataTable(this)) {
            try {
                $(this).DataTable().destroy();
            } catch (e) {
                console.warn("Error destroying DataTable:", e);
            }
        }
    });
});

console.log("DataTables Fix Script Loaded Successfully");
