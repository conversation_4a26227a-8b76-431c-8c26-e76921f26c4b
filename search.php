<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/search_system.php';
require_once 'includes/security.php';

// تطبيق إعدادات الأمان
applySecurityHeaders();

// الحصول على معاملات البحث
$query = $_GET['q'] ?? '';
$category = $_GET['category'] ?? '';
$level = $_GET['level'] ?? '';
$price_range = $_GET['price_range'] ?? '';
$instructor = $_GET['instructor'] ?? '';
$sort_by = $_GET['sort_by'] ?? 'relevance';
$page = max(1, (int)($_GET['page'] ?? 1));

// تنظيف المدخلات
$query = htmlspecialchars(trim($query), ENT_QUOTES, 'UTF-8');

// إعداد البحث
$searchSystem = new SearchSystem($conn);
$results = [];
$totalResults = 0;
$suggestions = [];
$searchTime = 0;

if (!empty($query)) {
    $filters = array_filter([
        'category_id' => $category,
        'course_level' => $level,
        'price_range' => $price_range,
        'instructor_id' => $instructor
    ]);
    
    $options = [
        'page' => $page,
        'per_page' => 12,
        'sort_by' => $sort_by,
        'sort_order' => 'DESC'
    ];
    
    $searchResult = $searchSystem->search($query, $filters, $options);
    
    if ($searchResult['success']) {
        $results = $searchResult['results'];
        $totalResults = $searchResult['total_results'];
        $suggestions = $searchResult['suggestions'];
        $searchTime = $searchResult['search_time'] ?? 0;
    }
}

// الحصول على التصنيفات للفلاتر
try {
    $stmt = $conn->query("SELECT id, name FROM categories WHERE is_active = 1 ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $categories = [];
}

// الحصول على المدربين للفلاتر
try {
    $stmt = $conn->query("SELECT id, name FROM users WHERE role = 'instructor' AND status = 'active' ORDER BY name LIMIT 50");
    $instructors = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructors = [];
}

$pageTitle = !empty($query) ? "نتائج البحث عن: $query" : "البحث في الكورسات";
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ابحث في مكتبة الكورسات الواسعة واعثر على الكورس المناسب لك">
    <meta name="keywords" content="بحث كورسات, تعلم إلكتروني, دورات تدريبية">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/responsive.css" rel="stylesheet">
    
    <style>
        .search-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 0 40px;
        }
        
        .search-form-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
        }
        
        .search-input-group {
            position: relative;
        }
        
        .search-input {
            border: none;
            border-radius: 50px;
            padding: 15px 60px 15px 20px;
            font-size: 16px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .search-btn {
            position: absolute;
            left: 5px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: var(--primary-color);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .filters-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .filter-group {
            margin-bottom: 20px;
        }
        
        .filter-label {
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dark-color);
        }
        
        .filter-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 10px 15px;
            width: 100%;
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .results-info {
            color: var(--gray-600);
        }
        
        .sort-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 8px 15px;
            min-width: 150px;
        }
        
        .search-result-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .search-result-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .result-title a {
            color: var(--dark-color);
            text-decoration: none;
        }
        
        .result-title a:hover {
            color: var(--primary-color);
        }
        
        .result-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            font-size: 0.9rem;
            color: var(--gray-600);
        }
        
        .result-description {
            color: var(--gray-700);
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .result-tags {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .result-tag {
            background: var(--light-color);
            color: var(--primary-color);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .no-results {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .no-results-icon {
            font-size: 4rem;
            color: var(--gray-400);
            margin-bottom: 20px;
        }
        
        .suggestions-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .suggestion-tags {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .suggestion-tag {
            background: var(--primary-color);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .suggestion-tag:hover {
            background: var(--primary-dark);
            color: white;
            transform: translateY(-2px);
        }
        
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 40px;
        }
        
        .search-stats {
            font-size: 0.9rem;
            color: var(--gray-600);
            margin-top: 10px;
        }
        
        @media (max-width: 768px) {
            .search-header {
                padding: 40px 0 30px;
            }
            
            .search-form-container {
                padding: 20px;
            }
            
            .results-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .result-meta {
                flex-direction: column;
                gap: 5px;
            }
            
            .filters-section {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php include 'includes/header.php'; ?>

    <!-- Search Header -->
    <section class="search-header">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h1 class="text-center mb-4">
                        <i class="fas fa-search"></i>
                        ابحث في مكتبة الكورسات
                    </h1>
                    <p class="text-center mb-4 opacity-75">
                        اعثر على الكورس المناسب لك من بين آلاف الكورسات المتاحة
                    </p>
                    
                    <div class="search-form-container">
                        <form method="GET" action="search.php" class="search-form">
                            <div class="search-input-group">
                                <input type="text" 
                                       name="q" 
                                       class="form-control search-input" 
                                       placeholder="ابحث عن كورس، مدرب، أو موضوع..."
                                       value="<?php echo htmlspecialchars($query); ?>"
                                       autocomplete="off">
                                <button type="submit" class="search-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            
                            <!-- Hidden filters -->
                            <?php if ($category): ?>
                                <input type="hidden" name="category" value="<?php echo htmlspecialchars($category); ?>">
                            <?php endif; ?>
                            <?php if ($level): ?>
                                <input type="hidden" name="level" value="<?php echo htmlspecialchars($level); ?>">
                            <?php endif; ?>
                            <?php if ($price_range): ?>
                                <input type="hidden" name="price_range" value="<?php echo htmlspecialchars($price_range); ?>">
                            <?php endif; ?>
                            <?php if ($instructor): ?>
                                <input type="hidden" name="instructor" value="<?php echo htmlspecialchars($instructor); ?>">
                            <?php endif; ?>
                        </form>
                        
                        <?php if (!empty($query)): ?>
                            <div class="search-stats text-center">
                                تم العثور على <?php echo number_format($totalResults); ?> نتيجة في <?php echo number_format($searchTime * 1000, 2); ?> ميلي ثانية
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="filters-section">
                    <h5 class="mb-3">
                        <i class="fas fa-filter"></i>
                        تصفية النتائج
                    </h5>
                    
                    <form method="GET" action="search.php" id="filtersForm">
                        <input type="hidden" name="q" value="<?php echo htmlspecialchars($query); ?>">
                        
                        <!-- Category Filter -->
                        <div class="filter-group">
                            <label class="filter-label">التصنيف</label>
                            <select name="category" class="filter-select" onchange="document.getElementById('filtersForm').submit()">
                                <option value="">جميع التصنيفات</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo $cat['id']; ?>" <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- Level Filter -->
                        <div class="filter-group">
                            <label class="filter-label">المستوى</label>
                            <select name="level" class="filter-select" onchange="document.getElementById('filtersForm').submit()">
                                <option value="">جميع المستويات</option>
                                <option value="beginner" <?php echo $level == 'beginner' ? 'selected' : ''; ?>>مبتدئ</option>
                                <option value="intermediate" <?php echo $level == 'intermediate' ? 'selected' : ''; ?>>متوسط</option>
                                <option value="advanced" <?php echo $level == 'advanced' ? 'selected' : ''; ?>>متقدم</option>
                            </select>
                        </div>
                        
                        <!-- Price Filter -->
                        <div class="filter-group">
                            <label class="filter-label">السعر</label>
                            <select name="price_range" class="filter-select" onchange="document.getElementById('filtersForm').submit()">
                                <option value="">جميع الأسعار</option>
                                <option value="0-0" <?php echo $price_range == '0-0' ? 'selected' : ''; ?>>مجاني</option>
                                <option value="1-50" <?php echo $price_range == '1-50' ? 'selected' : ''; ?>>$1 - $50</option>
                                <option value="51-100" <?php echo $price_range == '51-100' ? 'selected' : ''; ?>>$51 - $100</option>
                                <option value="101-200" <?php echo $price_range == '101-200' ? 'selected' : ''; ?>>$101 - $200</option>
                                <option value="201-999999" <?php echo $price_range == '201-999999' ? 'selected' : ''; ?>>أكثر من $200</option>
                            </select>
                        </div>
                        
                        <!-- Instructor Filter -->
                        <div class="filter-group">
                            <label class="filter-label">المدرب</label>
                            <select name="instructor" class="filter-select" onchange="document.getElementById('filtersForm').submit()">
                                <option value="">جميع المدربين</option>
                                <?php foreach ($instructors as $inst): ?>
                                    <option value="<?php echo $inst['id']; ?>" <?php echo $instructor == $inst['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($inst['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- Clear Filters -->
                        <?php if ($category || $level || $price_range || $instructor): ?>
                            <div class="filter-group">
                                <a href="search.php?q=<?php echo urlencode($query); ?>" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-times"></i>
                                    مسح الفلاتر
                                </a>
                            </div>
                        <?php endif; ?>
                    </form>
                </div>
            </div>
            
            <!-- Results -->
            <div class="col-lg-9">
                <?php if (!empty($query)): ?>
                    <!-- Results Header -->
                    <div class="results-header">
                        <div class="results-info">
                            <strong><?php echo number_format($totalResults); ?></strong> نتيجة للبحث عن 
                            "<strong><?php echo htmlspecialchars($query); ?></strong>"
                        </div>
                        <div class="sort-section">
                            <form method="GET" action="search.php" style="display: inline;">
                                <input type="hidden" name="q" value="<?php echo htmlspecialchars($query); ?>">
                                <input type="hidden" name="category" value="<?php echo htmlspecialchars($category); ?>">
                                <input type="hidden" name="level" value="<?php echo htmlspecialchars($level); ?>">
                                <input type="hidden" name="price_range" value="<?php echo htmlspecialchars($price_range); ?>">
                                <input type="hidden" name="instructor" value="<?php echo htmlspecialchars($instructor); ?>">
                                <select name="sort_by" class="sort-select" onchange="this.form.submit()">
                                    <option value="relevance" <?php echo $sort_by == 'relevance' ? 'selected' : ''; ?>>الأكثر صلة</option>
                                    <option value="date" <?php echo $sort_by == 'date' ? 'selected' : ''; ?>>الأحدث</option>
                                    <option value="title" <?php echo $sort_by == 'title' ? 'selected' : ''; ?>>الاسم</option>
                                </select>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Search Results -->
                    <?php if (!empty($results)): ?>
                        <div class="search-results">
                            <?php foreach ($results as $result): ?>
                                <div class="search-result-item">
                                    <h3 class="result-title">
                                        <a href="<?php echo htmlspecialchars($result['url']); ?>">
                                            <?php echo $result['title']; ?>
                                        </a>
                                    </h3>
                                    
                                    <div class="result-meta">
                                        <span><i class="fas fa-tag"></i> <?php echo ucfirst($result['type']); ?></span>
                                        <?php if (isset($result['additional_info'])): ?>
                                            <?php $info = $result['additional_info']; ?>
                                            <?php if (isset($info['instructor_name'])): ?>
                                                <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($info['instructor_name']); ?></span>
                                            <?php endif; ?>
                                            <?php if (isset($info['category_name'])): ?>
                                                <span><i class="fas fa-folder"></i> <?php echo htmlspecialchars($info['category_name']); ?></span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <span><i class="fas fa-star"></i> <?php echo number_format($result['relevance'], 1); ?></span>
                                    </div>
                                    
                                    <div class="result-description">
                                        <?php echo $result['content']; ?>
                                    </div>
                                    
                                    <?php if (!empty($result['tags'])): ?>
                                        <div class="result-tags">
                                            <?php foreach ($result['tags'] as $tag): ?>
                                                <span class="result-tag"><?php echo htmlspecialchars($tag); ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($totalResults > 12): ?>
                            <div class="pagination-container">
                                <nav aria-label="نتائج البحث">
                                    <ul class="pagination">
                                        <?php
                                        $totalPages = ceil($totalResults / 12);
                                        $currentPage = $page;
                                        
                                        // Previous page
                                        if ($currentPage > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?q=<?php echo urlencode($query); ?>&page=<?php echo $currentPage - 1; ?>&category=<?php echo urlencode($category); ?>&level=<?php echo urlencode($level); ?>&price_range=<?php echo urlencode($price_range); ?>&instructor=<?php echo urlencode($instructor); ?>&sort_by=<?php echo urlencode($sort_by); ?>">السابق</a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <?php
                                        // Page numbers
                                        $start = max(1, $currentPage - 2);
                                        $end = min($totalPages, $currentPage + 2);
                                        
                                        for ($i = $start; $i <= $end; $i++): ?>
                                            <li class="page-item <?php echo $i == $currentPage ? 'active' : ''; ?>">
                                                <a class="page-link" href="?q=<?php echo urlencode($query); ?>&page=<?php echo $i; ?>&category=<?php echo urlencode($category); ?>&level=<?php echo urlencode($level); ?>&price_range=<?php echo urlencode($price_range); ?>&instructor=<?php echo urlencode($instructor); ?>&sort_by=<?php echo urlencode($sort_by); ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        
                                        <?php
                                        // Next page
                                        if ($currentPage < $totalPages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?q=<?php echo urlencode($query); ?>&page=<?php echo $currentPage + 1; ?>&category=<?php echo urlencode($category); ?>&level=<?php echo urlencode($level); ?>&price_range=<?php echo urlencode($price_range); ?>&instructor=<?php echo urlencode($instructor); ?>&sort_by=<?php echo urlencode($sort_by); ?>">التالي</a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            </div>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <!-- No Results -->
                        <div class="no-results">
                            <div class="no-results-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h3>لم يتم العثور على نتائج</h3>
                            <p class="text-muted">لم نتمكن من العثور على أي نتائج تطابق بحثك. جرب:</p>
                            <ul class="list-unstyled">
                                <li>• التحقق من الإملاء</li>
                                <li>• استخدام كلمات مفتاحية أخرى</li>
                                <li>• تقليل عدد الفلاتر</li>
                                <li>• البحث بمصطلحات أعم</li>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Suggestions -->
                    <?php if (!empty($suggestions['keywords']) || !empty($suggestions['titles'])): ?>
                        <div class="suggestions-section">
                            <h5><i class="fas fa-lightbulb"></i> اقتراحات البحث</h5>
                            
                            <?php if (!empty($suggestions['keywords'])): ?>
                                <h6 class="mt-3">كلمات مفتاحية مقترحة:</h6>
                                <div class="suggestion-tags">
                                    <?php foreach ($suggestions['keywords'] as $keyword): ?>
                                        <a href="?q=<?php echo urlencode($keyword); ?>" class="suggestion-tag">
                                            <?php echo htmlspecialchars($keyword); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($suggestions['titles'])): ?>
                                <h6 class="mt-3">عناوين مشابهة:</h6>
                                <div class="suggestion-tags">
                                    <?php foreach ($suggestions['titles'] as $title): ?>
                                        <a href="?q=<?php echo urlencode($title); ?>" class="suggestion-tag">
                                            <?php echo htmlspecialchars(substr($title, 0, 30)) . (strlen($title) > 30 ? '...' : ''); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <!-- Search Instructions -->
                    <div class="no-results">
                        <div class="no-results-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>ابحث في مكتبة الكورسات</h3>
                        <p class="text-muted">استخدم مربع البحث أعلاه للعثور على الكورسات والمدربين والمواضيع التي تهمك</p>
                        
                        <div class="suggestion-tags mt-4">
                            <a href="?q=برمجة" class="suggestion-tag">برمجة</a>
                            <a href="?q=تصميم" class="suggestion-tag">تصميم</a>
                            <a href="?q=تسويق" class="suggestion-tag">تسويق</a>
                            <a href="?q=إدارة" class="suggestion-tag">إدارة</a>
                            <a href="?q=لغات" class="suggestion-tag">لغات</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/responsive.js"></script>
    
    <script>
        // تحسين تجربة البحث
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.search-input');
            
            if (searchInput) {
                // التركيز على مربع البحث
                searchInput.focus();
                
                // اقتراحات البحث التلقائية
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    const query = this.value.trim();
                    
                    if (query.length >= 2) {
                        searchTimeout = setTimeout(() => {
                            // يمكن إضافة اقتراحات تلقائية هنا
                            console.log('البحث عن:', query);
                        }, 300);
                    }
                });
            }
            
            // تحسين الفلاتر للجوال
            if (window.innerWidth <= 768) {
                const filtersSection = document.querySelector('.filters-section');
                if (filtersSection) {
                    const toggleBtn = document.createElement('button');
                    toggleBtn.className = 'btn btn-outline-primary w-100 mb-3';
                    toggleBtn.innerHTML = '<i class="fas fa-filter"></i> إظهار/إخفاء الفلاتر';
                    
                    toggleBtn.addEventListener('click', function() {
                        filtersSection.style.display = filtersSection.style.display === 'none' ? 'block' : 'none';
                    });
                    
                    filtersSection.parentNode.insertBefore(toggleBtn, filtersSection);
                    filtersSection.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
