<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'كورساتي';
$breadcrumbs = [
    ['title' => 'كورساتي']
];

// جلب كورسات الطالب
try {
    $stmt = $conn->prepare("
        SELECT
            c.*,
            COALESCE(u.full_name, u.username) as instructor_name,
            e.status as enrollment_status,
            e.enrolled_at as enrollment_date,
            e.progress,
            e.grade,
            cat.name as category_name,
            (SELECT COUNT(*) FROM sessions s WHERE s.course_id = c.id) as total_sessions,
            (SELECT COUNT(*) FROM course_videos cv WHERE cv.course_id = c.id) as total_videos,
            (SELECT COUNT(*) FROM video_watches vw
             INNER JOIN course_videos cv2 ON vw.video_id = cv2.id
             WHERE vw.user_id = ? AND cv2.course_id = c.id) as watched_videos,
            (SELECT AVG(grade) FROM assignment_submissions asub
             INNER JOIN assignments a ON asub.assignment_id = a.id
             WHERE a.course_id = c.id AND asub.student_id = ?) as avg_grade
        FROM course_enrollments e
        INNER JOIN courses c ON e.course_id = c.id
        INNER JOIN users u ON c.instructor_id = u.id
        LEFT JOIN categories cat ON c.category_id = cat.id
        WHERE e.student_id = ?
        ORDER BY e.enrolled_at DESC
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id']]);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب الكورسات: ' . $e->getMessage();
    $courses = [];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-graduation-cap text-primary me-2"></i>
            كورساتي
        </h2>
        <p class="text-muted mb-0">جميع الكورسات المسجل بها</p>
    </div>
    <div>
        <a href="browse-courses.php" class="btn btn-student-primary">
            <i class="fas fa-search me-1"></i>تصفح كورسات جديدة
        </a>
    </div>
</div>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
</div>
<?php endif; ?>

<?php if (empty($courses)): ?>
<!-- حالة عدم وجود كورسات -->
<div class="text-center py-5">
    <div class="card-student">
        <div class="card-body py-5">
            <i class="fas fa-graduation-cap text-muted mb-4" style="font-size: 4rem;"></i>
            <h4 class="text-muted mb-3">لا توجد كورسات مسجلة</h4>
            <p class="text-muted mb-4">ابدأ رحلتك التعليمية بالتسجيل في الكورسات المتاحة</p>
            <a href="browse-courses.php" class="btn btn-student-primary btn-lg">
                <i class="fas fa-search me-2"></i>استكشف الكورسات
            </a>
        </div>
    </div>
</div>
<?php else: ?>
<!-- قائمة الكورسات -->
<div class="row g-4">
    <?php foreach ($courses as $course): ?>
    <div class="col-lg-6 col-xl-4">
        <div class="card-student h-100">
            <!-- صورة الكورس -->
            <div class="position-relative">
                <?php if ($course['image']): ?>
                <img src="../uploads/courses/<?php echo htmlspecialchars($course['image']); ?>" 
                     class="card-img-top" style="height: 200px; object-fit: cover;" alt="صورة الكورس">
                <?php else: ?>
                <div class="card-img-top bg-gradient-primary d-flex align-items-center justify-content-center" 
                     style="height: 200px;">
                    <i class="fas fa-graduation-cap text-white" style="font-size: 3rem;"></i>
                </div>
                <?php endif; ?>
                
                <!-- شارة الحالة -->
                <div class="position-absolute top-0 end-0 m-3">
                    <?php
                    $status_classes = [
                        'active' => 'bg-success',
                        'pending' => 'bg-warning',
                        'completed' => 'bg-info',
                        'suspended' => 'bg-danger'
                    ];
                    $status_labels = [
                        'active' => 'نشط',
                        'pending' => 'معلق',
                        'completed' => 'مكتمل',
                        'suspended' => 'موقوف'
                    ];
                    $status_class = $status_classes[$course['enrollment_status']] ?? 'bg-secondary';
                    $status_label = $status_labels[$course['enrollment_status']] ?? $course['enrollment_status'];
                    ?>
                    <span class="badge <?php echo $status_class; ?>"><?php echo $status_label; ?></span>
                </div>
            </div>
            
            <div class="card-body">
                <!-- عنوان الكورس -->
                <h5 class="card-title mb-2"><?php echo htmlspecialchars($course['title']); ?></h5>
                
                <!-- معلومات المدرب -->
                <p class="text-muted mb-3">
                    <i class="fas fa-user me-1"></i>
                    <?php echo htmlspecialchars($course['instructor_name']); ?>
                </p>
                
                <!-- وصف مختصر -->
                <?php if ($course['description']): ?>
                <p class="card-text text-muted small mb-3">
                    <?php echo mb_substr(strip_tags($course['description']), 0, 100) . '...'; ?>
                </p>
                <?php endif; ?>
                
                <!-- شريط التقدم -->
                <?php 
                $progress = $course['total_videos'] > 0 ? 
                    round(($course['watched_videos'] / $course['total_videos']) * 100) : 0;
                ?>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">التقدم</small>
                        <small class="fw-bold"><?php echo $progress; ?>%</small>
                    </div>
                    <div class="progress-student">
                        <div class="progress-bar-student" style="width: <?php echo $progress; ?>%"></div>
                    </div>
                    <small class="text-muted">
                        <?php echo $course['watched_videos']; ?> من <?php echo $course['total_videos']; ?> فيديو
                    </small>
                </div>
                
                <!-- الدرجة -->
                <?php if ($course['avg_grade']): ?>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">متوسط الدرجات</small>
                        <span class="badge bg-primary"><?php echo number_format($course['avg_grade'], 1); ?>%</span>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- معلومات إضافية -->
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <small class="text-muted d-block">المستوى</small>
                        <small class="fw-bold"><?php echo htmlspecialchars($course['level'] ?? 'مبتدئ'); ?></small>
                    </div>
                    <div class="col-4">
                        <small class="text-muted d-block">المدة</small>
                        <small class="fw-bold"><?php echo htmlspecialchars($course['duration'] ?? '-'); ?></small>
                    </div>
                    <div class="col-4">
                        <small class="text-muted d-block">التسجيل</small>
                        <small class="fw-bold"><?php echo date('Y-m-d', strtotime($course['enrollment_date'])); ?></small>
                    </div>
                </div>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="card-footer bg-transparent">
                <div class="d-grid gap-2">
                    <?php if ($course['enrollment_status'] === 'active'): ?>
                    <a href="course-view.php?id=<?php echo $course['id']; ?>" class="btn btn-student-primary">
                        <i class="fas fa-play me-1"></i>متابعة التعلم
                    </a>
                    <?php elseif ($course['enrollment_status'] === 'completed'): ?>
                    <a href="course-view.php?id=<?php echo $course['id']; ?>" class="btn btn-outline-success">
                        <i class="fas fa-eye me-1"></i>مراجعة الكورس
                    </a>
                    <?php elseif ($course['enrollment_status'] === 'pending'): ?>
                    <button class="btn btn-outline-warning" disabled>
                        <i class="fas fa-clock me-1"></i>في انتظار الموافقة
                    </button>
                    <?php else: ?>
                    <button class="btn btn-outline-secondary" disabled>
                        <i class="fas fa-ban me-1"></i>غير متاح
                    </button>
                    <?php endif; ?>
                    
                    <div class="btn-group" role="group">
                        <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-info-circle me-1"></i>التفاصيل
                        </a>
                        <a href="assignments.php?course_id=<?php echo $course['id']; ?>" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-tasks me-1"></i>الواجبات
                        </a>
                        <a href="grades.php?course_id=<?php echo $course['id']; ?>" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-chart-line me-1"></i>الدرجات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- إحصائيات سريعة -->
<div class="row g-4 mt-4">
    <div class="col-md-3">
        <div class="card-student text-center">
            <div class="card-body">
                <i class="fas fa-graduation-cap text-primary mb-2" style="font-size: 2rem;"></i>
                <h4><?php echo count($courses); ?></h4>
                <small class="text-muted">إجمالي الكورسات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card-student text-center">
            <div class="card-body">
                <i class="fas fa-check-circle text-success mb-2" style="font-size: 2rem;"></i>
                <h4><?php echo count(array_filter($courses, function($c) { return $c['enrollment_status'] === 'completed'; })); ?></h4>
                <small class="text-muted">كورسات مكتملة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card-student text-center">
            <div class="card-body">
                <i class="fas fa-play-circle text-info mb-2" style="font-size: 2rem;"></i>
                <h4><?php echo count(array_filter($courses, function($c) { return $c['enrollment_status'] === 'active'; })); ?></h4>
                <small class="text-muted">كورسات نشطة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card-student text-center">
            <div class="card-body">
                <i class="fas fa-chart-line text-warning mb-2" style="font-size: 2rem;"></i>
                <h4><?php 
                $total_progress = 0;
                $active_courses = array_filter($courses, function($c) { return $c['enrollment_status'] === 'active'; });
                if (!empty($active_courses)) {
                    foreach ($active_courses as $course) {
                        $progress = $course['total_videos'] > 0 ? 
                            round(($course['watched_videos'] / $course['total_videos']) * 100) : 0;
                        $total_progress += $progress;
                    }
                    echo round($total_progress / count($active_courses));
                } else {
                    echo '0';
                }
                ?>%</h4>
                <small class="text-muted">متوسط التقدم</small>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
