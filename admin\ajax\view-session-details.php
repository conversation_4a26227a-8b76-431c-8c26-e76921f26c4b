<?php
session_start();
require_once '../includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

$session_id = (int)($_GET['session_id'] ?? 0);

if (!$session_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الجلسة مطلوب']);
    exit;
}

try {
    // جلب تفاصيل الجلسة
    $session = fetchOne("
        SELECT s.*, c.title as course_title, u.name as instructor_name
        FROM sessions s
        LEFT JOIN courses c ON s.course_id = c.id
        LEFT JOIN users u ON c.instructor_id = u.id
        WHERE s.id = ?
    ", [$session_id]);
    
    if (!$session) {
        echo json_encode(['success' => false, 'message' => 'الجلسة غير موجودة']);
        exit;
    }
    
    // جلب إحصائيات الحضور
    $attendance_stats = fetchOne("
        SELECT 
            COUNT(*) as total_enrolled,
            COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present_count,
            COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent_count
        FROM course_enrollments ce
        LEFT JOIN session_attendance sa ON ce.student_id = sa.student_id AND sa.session_id = ?
        WHERE ce.course_id = ?
    ", [$session_id, $session['course_id']]);
    
    // جلب قائمة الحضور
    $attendance_list = fetchAll("
        SELECT 
            u.name as student_name,
            u.email as student_email,
            sa.status,
            sa.attended_at,
            sa.notes
        FROM course_enrollments ce
        JOIN users u ON ce.student_id = u.id
        LEFT JOIN session_attendance sa ON ce.student_id = sa.student_id AND sa.session_id = ?
        WHERE ce.course_id = ?
        ORDER BY u.name
    ", [$session_id, $session['course_id']]);
    
    // تنسيق البيانات
    $response = [
        'success' => true,
        'session' => [
            'id' => $session['id'],
            'title' => $session['title'],
            'description' => $session['description'],
            'course_title' => $session['course_title'],
            'instructor_name' => $session['instructor_name'],
            'session_date' => $session['session_date'],
            'start_time' => $session['start_time'],
            'end_time' => $session['end_time'],
            'session_type' => $session['session_type'],
            'meeting_link' => $session['meeting_link'],
            'status' => $session['status'],
            'created_at' => $session['created_at']
        ],
        'attendance_stats' => $attendance_stats,
        'attendance_list' => $attendance_list
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
