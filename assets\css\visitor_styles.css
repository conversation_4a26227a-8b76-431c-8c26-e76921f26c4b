/**
 * أنماط CSS محسنة لواجهة الزوار
 * Enhanced CSS Styles for Visitor Interface
 * ========================================
 */

/* متغيرات CSS المحسنة */
:root {
    /* الألوان الأساسية */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    
    /* ألوان مفردة */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4facfe;
    --warning-color: #43e97b;
    --danger-color: #f5576c;
    --info-color: #a8edea;
    
    /* ألوان رمادية */
    --dark-color: #2d3748;
    --light-color: #f8fafc;
    --gray-50: #fafafa;
    --gray-100: #f7fafc;
    --gray-200: #edf2f7;
    --gray-300: #e2e8f0;
    --gray-400: #cbd5e0;
    --gray-500: #a0aec0;
    --gray-600: #718096;
    --gray-700: #4a5568;
    --gray-800: #2d3748;
    --gray-900: #1a202c;
    
    /* أنصاف الأقطار */
    --border-radius-sm: 8px;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --border-radius-2xl: 24px;
    --border-radius-full: 50%;
    
    /* الظلال */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 15px 35px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    --shadow-2xl: 0 35px 60px rgba(0, 0, 0, 0.3);
    
    /* الانتقالات */
    --transition-fast: all 0.15s ease;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* الخطوط */
    --font-family-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family-primary);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسينات الطباعة */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing);
    color: var(--dark-color);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--spacing);
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

/* تحسينات الأزرار */
.btn-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing) var(--spacing-xl);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: var(--font-size-base);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-primary-modern {
    background: var(--primary-gradient);
    color: white;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-secondary-modern {
    background: var(--secondary-gradient);
    color: white;
}

.btn-outline-modern {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-modern:hover {
    background: var(--primary-color);
    color: white;
}

/* تحسينات البطاقات */
.card-enhanced {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    position: relative;
}

.card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.card-enhanced:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.card-enhanced:hover::before {
    transform: scaleX(1);
}

.card-body-enhanced {
    padding: var(--spacing-xl);
}

.card-title-enhanced {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing);
    color: var(--dark-color);
}

.card-text-enhanced {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

/* تأثيرات الصور */
.image-enhanced {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
}

.image-enhanced img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.image-enhanced:hover img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        rgba(0,0,0,0.3) 50%,
        rgba(0,0,0,0.7) 100%
    );
    display: flex;
    align-items: flex-end;
    padding: var(--spacing-xl);
    opacity: 0;
    transition: var(--transition);
}

.image-enhanced:hover .image-overlay {
    opacity: 1;
}

/* تحسينات النماذج */
.form-enhanced {
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.form-group-enhanced {
    margin-bottom: var(--spacing-lg);
}

.form-label-enhanced {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--dark-color);
}

.form-control-enhanced {
    width: 100%;
    padding: var(--spacing) var(--spacing-lg);
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
    background: white;
}

.form-control-enhanced:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* تحسينات الشارات */
.badge-enhanced {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary-enhanced {
    background: var(--primary-gradient);
    color: white;
}

.badge-success-enhanced {
    background: var(--success-gradient);
    color: white;
}

.badge-warning-enhanced {
    background: var(--warning-gradient);
    color: white;
}

/* تحسينات التنبيهات */
.alert-enhanced {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    border-left: 4px solid;
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.alert-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.alert-success-enhanced {
    background: rgba(67, 233, 123, 0.1);
    border-color: var(--success-color);
    color: #155724;
}

.alert-warning-enhanced {
    background: rgba(255, 193, 7, 0.1);
    border-color: var(--warning-color);
    color: #856404;
}

.alert-danger-enhanced {
    background: rgba(245, 87, 108, 0.1);
    border-color: var(--danger-color);
    color: #721c24;
}

.alert-info-enhanced {
    background: rgba(168, 237, 234, 0.1);
    border-color: var(--info-color);
    color: #0c5460;
}

/* تحسينات التحميل */
.loading-enhanced {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* تحسينات الجداول */
.table-enhanced {
    width: 100%;
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table-enhanced th {
    background: var(--gray-50);
    padding: var(--spacing-lg);
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 2px solid var(--gray-200);
}

.table-enhanced td {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.table-enhanced tr:hover {
    background: var(--gray-50);
}

/* تحسينات التقييم */
.rating-enhanced {
    display: inline-flex;
    gap: 2px;
}

.rating-enhanced .star {
    color: var(--gray-300);
    font-size: var(--font-size-lg);
    transition: var(--transition-fast);
    cursor: pointer;
}

.rating-enhanced .star.active {
    color: #ffc107;
}

.rating-enhanced .star:hover {
    transform: scale(1.2);
}

/* تحسينات شريط التقدم */
.progress-enhanced {
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--border-radius-full);
    overflow: hidden;
    position: relative;
}

.progress-bar-enhanced {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-full);
    transition: width 0.6s ease;
    position: relative;
}

.progress-bar-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        45deg,
        rgba(255,255,255,0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255,255,255,0.2) 50%,
        rgba(255,255,255,0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 20px 20px;
    animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
}

/* تحسينات الاستجابة */
@media (max-width: 1200px) {
    :root {
        --font-size-5xl: 2.5rem;
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.75rem;
    }
}

@media (max-width: 768px) {
    :root {
        --font-size-5xl: 2rem;
        --font-size-4xl: 1.75rem;
        --font-size-3xl: 1.5rem;
        --spacing-2xl: 2rem;
        --spacing-3xl: 2.5rem;
    }
    
    .btn-modern {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
    
    .card-body-enhanced {
        padding: var(--spacing-lg);
    }
    
    .form-enhanced {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 576px) {
    :root {
        --font-size-5xl: 1.75rem;
        --font-size-4xl: 1.5rem;
        --font-size-3xl: 1.25rem;
    }
    
    .btn-modern {
        width: 100%;
        justify-content: center;
    }
}

/* تحسينات إمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسينات الطباعة */
@media print {
    .btn-modern,
    .loading-enhanced,
    .alert-enhanced {
        display: none !important;
    }
    
    .card-enhanced {
        box-shadow: none !important;
        border: 1px solid var(--gray-300) !important;
    }
}

/* تحسينات الوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-color: #f7fafc;
        --light-color: #1a202c;
        --gray-100: #2d3748;
        --gray-200: #4a5568;
        --gray-300: #718096;
    }
    
    body {
        background-color: var(--light-color);
        color: var(--dark-color);
    }
    
    .card-enhanced {
        background: var(--gray-800);
        border-color: var(--gray-700);
    }
    
    .form-control-enhanced {
        background: var(--gray-800);
        border-color: var(--gray-600);
        color: var(--dark-color);
    }
}
