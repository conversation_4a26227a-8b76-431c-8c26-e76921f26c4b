<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if admin




// Handle delete
if (isset($_POST['delete'])) {
    $id = $_POST['id'];
    try {
        $stmt = $conn->prepare("DELETE FROM users WHERE id = ? AND role = 'student'");
        $stmt->execute([$id]);
        $_SESSION['success'] = 'تم حذف الطالب بنجاح';
    } catch(PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء حذف الطالب';
    }
}

// Get all students
try {
    $stmt = $conn->query("SELECT * FROM users WHERE role = 'student' ORDER BY created_at DESC");
    $students = $stmt->fetchAll();
} catch(PDOException $e) {
    $_SESSION['error'] = 'حدث خطأ في جلب بيانات الطلاب';
}
$pageTitle = 'لوحة التحكم';
require_once 'includes/header.php';
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="إدارة الطلاب في منصة التعلم الإلكتروني">
    <title>إدارة الطلاب - لوحة التحكم</title>

    <!-- الخطوط المحسنة -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- ملفات CSS المخصصة -->
    <link href="../assets/css/main.css" rel="stylesheet">
    <link href="../assets/css/components.css" rel="stylesheet">

    <style>
        body {
            background: var(--gray-50);
            font-family: var(--font-family-arabic);
        }

        .admin-header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: var(--spacing-8) 0;
            margin-bottom: var(--spacing-8);
            position: relative;
            overflow: hidden;
        }

        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
            animation: particleFloat 8s ease-in-out infinite;
        }

        .admin-header-content {
            position: relative;
            z-index: 2;
        }

        .stats-cards {
            margin-top: -var(--spacing-12);
            position: relative;
            z-index: 3;
        }

        .stat-card-admin {
            background: var(--white);
            border-radius: var(--border-radius-2xl);
            padding: var(--spacing-6);
            box-shadow: var(--shadow-lg);
            border: var(--border-width-1) solid var(--gray-200);
            transition: var(--transition-normal);
            height: 100%;
        }

        .stat-card-admin:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-2xl);
        }

        .stat-icon-admin {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-4);
            font-size: var(--font-size-2xl);
            color: var(--white);
        }

        .stat-number-admin {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-extrabold);
            color: var(--gray-900);
            line-height: 1;
            margin-bottom: var(--spacing-2);
        }

        .stat-label-admin {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-700);
        }

        .main-card {
            background: var(--white);
            border-radius: var(--border-radius-2xl);
            box-shadow: var(--shadow-md);
            border: var(--border-width-1) solid var(--gray-200);
            overflow: hidden;
        }

        .main-card-header {
            background: var(--gray-50);
            border-bottom: var(--border-width-1) solid var(--gray-200);
            padding: var(--spacing-6);
        }

        .main-card-body {
            padding: var(--spacing-6);
        }

        .table-enhanced {
            margin-bottom: 0;
        }

        .table-enhanced th {
            background: var(--gray-50);
            border-bottom: var(--border-width-2) solid var(--gray-200);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-700);
            padding: var(--spacing-4);
        }

        .table-enhanced td {
            padding: var(--spacing-4);
            vertical-align: middle;
            border-bottom: var(--border-width-1) solid var(--gray-100);
        }

        .table-enhanced tbody tr:hover {
            background: var(--gray-50);
        }

        .status-badge {
            padding: var(--spacing-2) var(--spacing-4);
            border-radius: var(--border-radius-full);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-semibold);
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-1);
        }

        .status-approved {
            background: rgba(72, 187, 120, 0.1);
            color: var(--success-color);
        }

        .status-pending {
            background: rgba(237, 137, 54, 0.1);
            color: var(--warning-color);
        }

        .status-rejected {
            background: rgba(245, 101, 101, 0.1);
            color: var(--danger-color);
        }

        .action-buttons {
            display: flex;
            gap: var(--spacing-2);
            align-items: center;
        }

        .btn-action {
            width: 36px;
            height: 36px;
            border-radius: var(--border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            font-size: var(--font-size-sm);
            transition: var(--transition-normal);
            cursor: pointer;
        }

        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-edit {
            background: var(--info-color);
            color: var(--white);
        }

        .btn-delete {
            background: var(--danger-color);
            color: var(--white);
        }

        .btn-approve {
            background: var(--success-color);
            color: var(--white);
        }

        .btn-reject {
            background: var(--warning-color);
            color: var(--white);
        }

        .search-box {
            position: relative;
            margin-bottom: var(--spacing-6);
        }

        .search-input {
            width: 100%;
            padding: var(--spacing-4) var(--spacing-4) var(--spacing-4) var(--spacing-12);
            border: var(--border-width-2) solid var(--gray-200);
            border-radius: var(--border-radius-xl);
            background: var(--white);
            font-size: var(--font-size-base);
            transition: var(--transition-normal);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-icon {
            position: absolute;
            top: 50%;
            right: var(--spacing-4);
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: var(--font-size-lg);
        }

        .filter-tabs {
            display: flex;
            gap: var(--spacing-2);
            margin-bottom: var(--spacing-6);
            flex-wrap: wrap;
        }

        .filter-tab {
            padding: var(--spacing-3) var(--spacing-6);
            border: var(--border-width-2) solid var(--gray-200);
            border-radius: var(--border-radius-full);
            background: var(--white);
            color: var(--gray-600);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
            transition: var(--transition-normal);
            cursor: pointer;
        }

        .filter-tab.active,
        .filter-tab:hover {
            background: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        @media (max-width: 768px) {
            .admin-header {
                padding: var(--spacing-6) 0;
            }

            .stats-cards {
                margin-top: -var(--spacing-8);
            }

            .action-buttons {
                flex-direction: column;
                gap: var(--spacing-1);
            }

            .table-responsive {
                font-size: var(--font-size-sm);
            }
        }
    </style>
</head>
<body>
    <!-- رأس الصفحة -->
    <div class="admin-header">
        <div class="container">
            <div class="admin-header-content">
                <div class="row align-items-center">
                    <div class="col-md-8" data-aos="fade-right">
                        <h1 class="display-5 fw-bold mb-3">إدارة الطلاب</h1>
                        <p class="lead mb-0 opacity-90">إدارة وتتبع جميع الطلاب المسجلين في المنصة</p>
                    </div>
                    <div class="col-md-4 text-center" data-aos="fade-left">
                        <i class="fas fa-user-graduate" style="font-size: 5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- بطاقات الإحصائيات -->
        <div class="stats-cards">
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-card-admin">
                        <div class="stat-icon-admin bg-primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number-admin counter" data-target="<?php echo count($students); ?>">0</div>
                        <div class="stat-label-admin">إجمالي الطلاب</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-card-admin">
                        <div class="stat-icon-admin bg-success">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-number-admin counter" data-target="<?php echo count(array_filter($students, function($s) { return $s['status'] === 'approved'; })); ?>">0</div>
                        <div class="stat-label-admin">طلاب معتمدون</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-card-admin">
                        <div class="stat-icon-admin bg-warning">
                            <i class="fas fa-user-clock"></i>
                        </div>
                        <div class="stat-number-admin counter" data-target="<?php echo count(array_filter($students, function($s) { return $s['status'] === 'pending'; })); ?>">0</div>
                        <div class="stat-label-admin">في الانتظار</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-card-admin">
                        <div class="stat-icon-admin bg-info">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="stat-number-admin counter" data-target="<?php echo count(array_filter($students, function($s) { return date('Y-m-d', strtotime($s['created_at'])) === date('Y-m-d'); })); ?>">0</div>
                        <div class="stat-label-admin">جدد اليوم</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسائل -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert" data-aos="fade-down">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" data-aos="fade-down">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- أدوات البحث والفلترة -->
        <div class="main-card mb-4" data-aos="fade-up">
            <div class="main-card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            البحث والفلترة
                        </h5>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="add-student.php" class="btn-enhanced btn-primary-enhanced">
                            <i class="fas fa-plus"></i>
                            <span>إضافة طالب جديد</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="main-card-body">
                <!-- مربع البحث -->
                <div class="search-box">
                    <input type="text" class="search-input" id="searchInput" placeholder="البحث عن طالب...">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <!-- تبويبات الفلترة -->
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">
                        <i class="fas fa-users me-1"></i>
                        جميع الطلاب
                    </button>
                    <button class="filter-tab" data-filter="approved">
                        <i class="fas fa-user-check me-1"></i>
                        معتمدون
                    </button>
                    <button class="filter-tab" data-filter="pending">
                        <i class="fas fa-user-clock me-1"></i>
                        في الانتظار
                    </button>
                    <button class="filter-tab" data-filter="today">
                        <i class="fas fa-calendar-day me-1"></i>
                        جدد اليوم
                    </button>
                </div>
            </div>
        </div>

        <!-- جدول الطلاب -->
        <div class="main-card" data-aos="fade-up" data-aos-delay="200">
            <div class="main-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    قائمة الطلاب
                    <span class="badge bg-primary ms-2" id="studentsCount"><?php echo count($students); ?></span>
                </h5>
            </div>
            <div class="main-card-body">
                <div class="table-responsive">
                    <table class="table table-enhanced" id="studentsTable">
                        <thead>
                            <tr>
                                <th>
                                    <i class="fas fa-user me-1"></i>
                                    الاسم
                                </th>
                                <th>
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </th>
                                <th>
                                    <i class="fas fa-phone me-1"></i>
                                    رقم الهاتف
                                </th>
                                <th>
                                    <i class="fas fa-info-circle me-1"></i>
                                    الحالة
                                </th>
                                <th>
                                    <i class="fas fa-calendar me-1"></i>
                                    تاريخ التسجيل
                                </th>
                                <th>
                                    <i class="fas fa-cogs me-1"></i>
                                    الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($students as $student): ?>
                                <tr data-status="<?php echo $student['status']; ?>" data-date="<?php echo date('Y-m-d', strtotime($student['created_at'])); ?>">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                <?php echo strtoupper(substr($student['name'], 0, 1)); ?>
                                            </div>
                                            <div>
                                                <div class="fw-semibold"><?php echo htmlspecialchars($student['name']); ?></div>
                                                <small class="text-muted">ID: <?php echo $student['id']; ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-break"><?php echo htmlspecialchars($student['email']); ?></div>
                                    </td>
                                    <td>
                                        <?php if (isset($student['phone']) && !empty($student['phone'])): ?>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-phone-alt text-success me-2"></i>
                                                <?php echo htmlspecialchars($student['phone']); ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">
                                                <i class="fas fa-minus me-1"></i>
                                                غير محدد
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = '';
                                        $statusIcon = '';
                                        $statusText = '';

                                        switch($student['status']) {
                                            case 'approved':
                                                $statusClass = 'status-approved';
                                                $statusIcon = 'fas fa-check-circle';
                                                $statusText = 'معتمد';
                                                break;
                                            case 'pending':
                                                $statusClass = 'status-pending';
                                                $statusIcon = 'fas fa-clock';
                                                $statusText = 'قيد المراجعة';
                                                break;
                                            case 'rejected':
                                                $statusClass = 'status-rejected';
                                                $statusIcon = 'fas fa-times-circle';
                                                $statusText = 'مرفوض';
                                                break;
                                            default:
                                                $statusClass = 'status-pending';
                                                $statusIcon = 'fas fa-question-circle';
                                                $statusText = 'غير محدد';
                                        }
                                        ?>
                                        <span class="status-badge <?php echo $statusClass; ?>">
                                            <i class="<?php echo $statusIcon; ?>"></i>
                                            <?php echo $statusText; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-medium"><?php echo date('Y-m-d', strtotime($student['created_at'])); ?></span>
                                            <small class="text-muted"><?php echo date('H:i', strtotime($student['created_at'])); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-action btn-edit" title="تعديل" onclick="editStudent(<?php echo $student['id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>

                                            <?php if ($student['status'] === 'pending'): ?>
                                                <button class="btn-action btn-approve" title="اعتماد" onclick="approveStudent(<?php echo $student['id']; ?>)">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn-action btn-reject" title="رفض" onclick="rejectStudent(<?php echo $student['id']; ?>)">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            <?php endif; ?>

                                            <button class="btn-action btn-delete" title="حذف" onclick="deleteStudent(<?php echo $student['id']; ?>, '<?php echo htmlspecialchars($student['name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../assets/js/enhanced.js"></script>

    <script>
        // تهيئة AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        document.addEventListener('DOMContentLoaded', function() {
            // تحريك العدادات
            const counters = document.querySelectorAll('.counter');
            const animateCounters = () => {
                counters.forEach(counter => {
                    const target = parseInt(counter.getAttribute('data-target'));
                    const duration = 2000;
                    const step = target / (duration / 16);
                    let current = 0;

                    const timer = setInterval(() => {
                        current += step;
                        if (current >= target) {
                            counter.textContent = target;
                            clearInterval(timer);
                        } else {
                            counter.textContent = Math.floor(current);
                        }
                    }, 16);
                });
            };

            // تشغيل العداد عند الوصول للقسم
            const statsSection = document.querySelector('.stats-cards');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCounters();
                        observer.unobserve(entry.target);
                    }
                });
            });
            if (statsSection) observer.observe(statsSection);

            // تهيئة DataTable مع فحص عدم إعادة التهيئة
            if (!$.fn.DataTable.isDataTable('#studentsTable')) {
                const table = $('#studentsTable').DataTable({
                    language: {
                        url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                    },
                    responsive: true,
                    pageLength: 25,
                    order: [[4, 'desc']], // ترتيب حسب تاريخ التسجيل
                    columnDefs: [
                        { orderable: false, targets: [5] } // عمود الإجراءات غير قابل للترتيب
                    ]
                });
            }

            // البحث المخصص
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('keyup', function() {
                table.search(this.value).draw();
                updateStudentsCount();
            });

            // فلترة حسب الحالة
            const filterTabs = document.querySelectorAll('.filter-tab');
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // تحديث التبويبات النشطة
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    const filter = this.getAttribute('data-filter');
                    filterStudents(filter);
                });
            });

            function filterStudents(filter) {
                const rows = document.querySelectorAll('#studentsTable tbody tr');
                let visibleCount = 0;

                rows.forEach(row => {
                    let show = true;

                    switch(filter) {
                        case 'approved':
                            show = row.getAttribute('data-status') === 'approved';
                            break;
                        case 'pending':
                            show = row.getAttribute('data-status') === 'pending';
                            break;
                        case 'today':
                            show = row.getAttribute('data-date') === new Date().toISOString().split('T')[0];
                            break;
                        case 'all':
                        default:
                            show = true;
                    }

                    if (show) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                updateStudentsCount(visibleCount);
            }

            function updateStudentsCount(count = null) {
                const countElement = document.getElementById('studentsCount');
                if (count !== null) {
                    countElement.textContent = count;
                } else {
                    const visibleRows = document.querySelectorAll('#studentsTable tbody tr:not([style*="display: none"])');
                    countElement.textContent = visibleRows.length;
                }
            }

            // إضافة CSS للأفاتار
            const style = document.createElement('style');
            style.textContent = `
                .avatar-circle {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: var(--gradient-primary);
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                    font-size: 1.1rem;
                }
            `;
            document.head.appendChild(style);
        });

        // وظائف الإجراءات
        function editStudent(id) {
            window.location.href = `edit-student.php?id=${id}`;
        }

        function approveStudent(id) {
            if (confirm('هل أنت متأكد من اعتماد هذا الطالب؟')) {
                window.location.href = `approve_student.php?id=${id}`;
            }
        }

        function rejectStudent(id) {
            if (confirm('هل أنت متأكد من رفض هذا الطالب؟')) {
                window.location.href = `reject_student.php?id=${id}`;
            }
        }

        function deleteStudent(id, name) {
            if (confirm(`هل أنت متأكد من حذف الطالب "${name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                // إنشاء نموذج مخفي للحذف
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'id';
                idInput.value = id;

                const deleteInput = document.createElement('input');
                deleteInput.type = 'hidden';
                deleteInput.name = 'delete';
                deleteInput.value = '1';

                form.appendChild(idInput);
                form.appendChild(deleteInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // تحسين تجربة المستخدم
        document.querySelectorAll('.btn-action').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.05)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // تحديث الوقت بشكل دوري
        setInterval(() => {
            const timeElements = document.querySelectorAll('[data-time]');
            timeElements.forEach(element => {
                const timestamp = element.getAttribute('data-time');
                const timeAgo = getTimeAgo(new Date(timestamp));
                element.textContent = timeAgo;
            });
        }, 60000); // كل دقيقة

        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'الآن';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} دقيقة`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} ساعة`;
            return `${Math.floor(diffInSeconds / 86400)} يوم`;
        }

        // إضافة تأثيرات بصرية للجدول
        document.querySelectorAll('#studentsTable tbody tr').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
                this.style.transition = 'all 0.2s ease';
            });

            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // تحسين الاستجابة للأجهزة المحمولة
        if (window.innerWidth <= 768) {
            // تحسينات خاصة بالأجهزة المحمولة
            document.querySelectorAll('.action-buttons').forEach(container => {
                container.style.flexDirection = 'column';
                container.style.gap = '0.25rem';
            });
        }
    </script>
</body>
</html>
