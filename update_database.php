<?php
/**
 * تحديث قاعدة البيانات إلى النسخة المحسنة
 * Database Update to Enhanced Version
 * ==================================
 */

require_once 'config/database.php';

echo "<h1>🔄 تحديث قاعدة البيانات إلى النسخة المحسنة</h1>";
echo "<div style='font-family: Arial; padding: 20px; background: #f8f9fa; border-radius: 10px; margin: 20px;'>";

try {
    // قراءة ملف قاعدة البيانات المحسنة
    $sqlFile = 'database/enhanced_schema.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف قاعدة البيانات المحسنة غير موجود: {$sqlFile}");
    }
    
    echo "<h2>📋 خطوات التحديث:</h2>";
    
    // 1. نسخ احتياطية للبيانات المهمة
    echo "<h3>1️⃣ إنشاء نسخة احتياطية للبيانات المهمة...</h3>";
    
    $backupData = [];
    
    // نسخ احتياطية للمستخدمين
    try {
        $stmt = $conn->query("SELECT * FROM users WHERE role = 'admin'");
        $backupData['admin_users'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "✅ تم حفظ بيانات المدراء: " . count($backupData['admin_users']) . " مستخدم<br>";
    } catch (PDOException $e) {
        echo "⚠️ لم يتم العثور على جدول المستخدمين الحالي<br>";
    }
    
    // نسخ احتياطية للإعدادات
    try {
        $stmt = $conn->query("SELECT * FROM system_settings");
        $backupData['settings'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "✅ تم حفظ إعدادات النظام: " . count($backupData['settings']) . " إعداد<br>";
    } catch (PDOException $e) {
        echo "⚠️ لم يتم العثور على جدول الإعدادات الحالي<br>";
    }
    
    // 2. تنفيذ ملف قاعدة البيانات الجديدة
    echo "<h3>2️⃣ تطبيق هيكل قاعدة البيانات الجديد...</h3>";
    
    $sql = file_get_contents($sqlFile);
    
    // تقسيم الاستعلامات
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        try {
            $conn->exec($statement);
            $successCount++;
        } catch (PDOException $e) {
            $errorCount++;
            if (strpos($e->getMessage(), 'already exists') === false) {
                echo "⚠️ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "<br>";
            }
        }
    }
    
    echo "✅ تم تنفيذ {$successCount} استعلام بنجاح<br>";
    if ($errorCount > 0) {
        echo "⚠️ {$errorCount} استعلام تم تجاهله (موجود مسبقاً)<br>";
    }
    
    // 3. استعادة البيانات المهمة
    echo "<h3>3️⃣ استعادة البيانات المهمة...</h3>";
    
    // استعادة المدراء
    if (!empty($backupData['admin_users'])) {
        foreach ($backupData['admin_users'] as $admin) {
            try {
                // التحقق من عدم وجود المدير مسبقاً
                $checkStmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
                $checkStmt->execute([$admin['email']]);
                
                if ($checkStmt->rowCount() == 0) {
                    $insertStmt = $conn->prepare("
                        INSERT INTO users (username, email, password, name, role, status, email_verified, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $insertStmt->execute([
                        $admin['username'],
                        $admin['email'],
                        $admin['password'],
                        $admin['name'],
                        $admin['role'],
                        $admin['status'],
                        $admin['email_verified'] ?? 1,
                        $admin['created_at'] ?? date('Y-m-d H:i:s')
                    ]);
                    echo "✅ تم استعادة المدير: {$admin['name']}<br>";
                }
            } catch (PDOException $e) {
                echo "⚠️ خطأ في استعادة المدير {$admin['name']}: " . $e->getMessage() . "<br>";
            }
        }
    }
    
    // استعادة الإعدادات
    if (!empty($backupData['settings'])) {
        foreach ($backupData['settings'] as $setting) {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO system_settings (setting_key, setting_value, setting_type, category, description, is_public)
                    VALUES (?, ?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    setting_value = VALUES(setting_value),
                    updated_at = CURRENT_TIMESTAMP
                ");
                $stmt->execute([
                    $setting['setting_key'],
                    $setting['setting_value'],
                    $setting['setting_type'] ?? 'string',
                    $setting['category'] ?? 'general',
                    $setting['description'] ?? null,
                    $setting['is_public'] ?? 0
                ]);
            } catch (PDOException $e) {
                echo "⚠️ خطأ في استعادة الإعداد {$setting['setting_key']}: " . $e->getMessage() . "<br>";
            }
        }
        echo "✅ تم استعادة " . count($backupData['settings']) . " إعداد<br>";
    }
    
    // 4. التحقق من سلامة قاعدة البيانات
    echo "<h3>4️⃣ التحقق من سلامة قاعدة البيانات...</h3>";
    
    $requiredTables = [
        'users', 'categories', 'courses', 'course_sections', 'course_lessons',
        'course_enrollments', 'lesson_progress', 'quizzes', 'quiz_questions',
        'quiz_attempts', 'assignments', 'assignment_submissions', 'file_uploads',
        'payments', 'meals', 'meal_categories', 'system_settings', 'activity_logs'
    ];
    
    $existingTables = [];
    $stmt = $conn->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existingTables[] = $row[0];
    }
    
    $missingTables = array_diff($requiredTables, $existingTables);
    
    if (empty($missingTables)) {
        echo "✅ جميع الجداول المطلوبة موجودة (" . count($requiredTables) . " جدول)<br>";
    } else {
        echo "⚠️ الجداول المفقودة: " . implode(', ', $missingTables) . "<br>";
    }
    
    // 5. إحصائيات قاعدة البيانات
    echo "<h3>5️⃣ إحصائيات قاعدة البيانات:</h3>";
    
    foreach ($existingTables as $table) {
        try {
            $countStmt = $conn->query("SELECT COUNT(*) as count FROM {$table}");
            $count = $countStmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo "📊 {$table}: {$count} سجل<br>";
        } catch (PDOException $e) {
            echo "⚠️ خطأ في عد سجلات {$table}<br>";
        }
    }
    
    // 6. اختبار العمليات الأساسية
    echo "<h3>6️⃣ اختبار العمليات الأساسية...</h3>";
    
    // اختبار الإدراج
    try {
        $testStmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, category) VALUES (?, ?, ?)");
        $testStmt->execute(['test_update_' . time(), 'test_value', 'test']);
        $testId = $conn->lastInsertId();
        echo "✅ اختبار الإدراج نجح<br>";
        
        // اختبار القراءة
        $readStmt = $conn->prepare("SELECT * FROM system_settings WHERE id = ?");
        $readStmt->execute([$testId]);
        if ($readStmt->rowCount() > 0) {
            echo "✅ اختبار القراءة نجح<br>";
        }
        
        // اختبار التحديث
        $updateStmt = $conn->prepare("UPDATE system_settings SET setting_value = ? WHERE id = ?");
        $updateStmt->execute(['updated_value', $testId]);
        echo "✅ اختبار التحديث نجح<br>";
        
        // اختبار الحذف
        $deleteStmt = $conn->prepare("DELETE FROM system_settings WHERE id = ?");
        $deleteStmt->execute([$testId]);
        echo "✅ اختبار الحذف نجح<br>";
        
    } catch (PDOException $e) {
        echo "❌ فشل في اختبار العمليات: " . $e->getMessage() . "<br>";
    }
    
    // النتيجة النهائية
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>🎉 تم التحديث بنجاح!</h2>";
    echo "<p><strong>قاعدة البيانات الآن محدثة إلى النسخة المحسنة مع دعم كامل لعمليات CRUD</strong></p>";
    
    echo "<h4>✨ المميزات الجديدة:</h4>";
    echo "<ul>";
    echo "<li>🗄️ هيكل قاعدة بيانات محسن ومنظم</li>";
    echo "<li>🔧 دعم كامل لجميع عمليات CRUD</li>";
    echo "<li>🔍 فهرسة محسنة للأداء</li>";
    echo "<li>🔐 أمان متقدم مع تسجيل الأنشطة</li>";
    echo "<li>📊 إحصائيات وتقارير شاملة</li>";
    echo "<li>🍽️ نظام الوجبات الصحية</li>";
    echo "<li>📚 نظام الواجبات والاختبارات</li>";
    echo "<li>💾 نظام رفع الملفات بدون تكرار</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #cff4fc; padding: 15px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🔗 الخطوات التالية:</h3>";
    echo "<a href='test_crud_operations.php' style='display: inline-block; padding: 10px 20px; background: #0d6efd; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>اختبار عمليات CRUD</a>";
    echo "<a href='admin/database_viewer.php' style='display: inline-block; padding: 10px 20px; background: #198754; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>عارض قاعدة البيانات</a>";
    echo "<a href='visitor_homepage.php' style='display: inline-block; padding: 10px 20px; background: #6f42c1; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>الصفحة الرئيسية</a>";
    echo "<a href='meals.php' style='display: inline-block; padding: 10px 20px; background: #fd7e14; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>صفحة الوجبات</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>❌ خطأ في التحديث!</h2>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p>يرجى التحقق من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة إعدادات قاعدة البيانات</li>";
    echo "<li>وجود ملف enhanced_schema.sql</li>";
    echo "<li>صلاحيات قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

h1 {
    color: white;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 30px;
}

h2, h3, h4 {
    color: #495057;
    margin-top: 20px;
}

h2 {
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 5px;
}

ul {
    padding-left: 20px;
}

li {
    margin-bottom: 5px;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-2px);
}

.success {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 10px;
    margin: 10px 0;
}

.error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 10px;
    margin: 10px 0;
}

.info {
    background: #cff4fc;
    color: #055160;
    padding: 15px;
    border-radius: 10px;
    margin: 10px 0;
}
</style>
