<?php
/**
 * اختبار شامل لعمليات CRUD
 * Comprehensive CRUD Operations Test
 * ==================================
 */

require_once 'includes/database_manager.php';

// تشغيل الاختبارات
$testResults = [];

echo "<h1>🧪 اختبار عمليات CRUD الشاملة</h1>";
echo "<div style='font-family: Arial; padding: 20px; background: #f8f9fa; border-radius: 10px; margin: 20px;'>";

// ===================================================================
// 1. اختبار عمليات CREATE (إنشاء)
// ===================================================================
echo "<h2>1️⃣ اختبار عمليات CREATE (إنشاء)</h2>";

// إنشاء فئة جديدة
$categoryData = [
    'name' => 'فئة اختبار',
    'slug' => 'test-category-' . time(),
    'description' => 'هذه فئة للاختبار',
    'icon' => 'fas fa-test',
    'color' => '#ff6b6b',
    'is_active' => 1,
    'sort_order' => 999
];

$categoryId = $db->create('categories', $categoryData);
if ($categoryId) {
    echo "✅ <span style='color: green;'>تم إنشاء فئة جديدة بالمعرف: {$categoryId}</span><br>";
    $testResults['create_category'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في إنشاء الفئة</span><br>";
    $testResults['create_category'] = false;
}

// إنشاء مستخدم جديد
$userData = [
    'username' => 'testuser_' . time(),
    'email' => 'test_' . time() . '@example.com',
    'password' => password_hash('123456', PASSWORD_DEFAULT),
    'name' => 'مستخدم اختبار',
    'role' => 'student',
    'status' => 'active',
    'email_verified' => 1
];

$userId = $db->create('users', $userData);
if ($userId) {
    echo "✅ <span style='color: green;'>تم إنشاء مستخدم جديد بالمعرف: {$userId}</span><br>";
    $testResults['create_user'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في إنشاء المستخدم</span><br>";
    $testResults['create_user'] = false;
}

// إنشاء كورس جديد
if ($categoryId && $userId) {
    $courseData = [
        'title' => 'كورس اختبار',
        'slug' => 'test-course-' . time(),
        'short_description' => 'وصف قصير للكورس',
        'description' => 'وصف مفصل للكورس التجريبي',
        'category_id' => $categoryId,
        'instructor_id' => $userId,
        'level' => 'beginner',
        'price' => 99.99,
        'is_free' => 0,
        'status' => 'published'
    ];
    
    $courseId = $db->create('courses', $courseData);
    if ($courseId) {
        echo "✅ <span style='color: green;'>تم إنشاء كورس جديد بالمعرف: {$courseId}</span><br>";
        $testResults['create_course'] = true;
    } else {
        echo "❌ <span style='color: red;'>فشل في إنشاء الكورس</span><br>";
        $testResults['create_course'] = false;
    }
}

// ===================================================================
// 2. اختبار عمليات READ (قراءة)
// ===================================================================
echo "<h2>2️⃣ اختبار عمليات READ (قراءة)</h2>";

// قراءة سجل واحد
if ($categoryId) {
    $category = $db->find('categories', $categoryId);
    if ($category && $category['name'] === 'فئة اختبار') {
        echo "✅ <span style='color: green;'>تم قراءة الفئة بنجاح: {$category['name']}</span><br>";
        $testResults['read_single'] = true;
    } else {
        echo "❌ <span style='color: red;'>فشل في قراءة الفئة</span><br>";
        $testResults['read_single'] = false;
    }
}

// قراءة متعددة بشروط
$activeCategories = $db->getWhere('categories', ['is_active' => 1]);
if (!empty($activeCategories)) {
    echo "✅ <span style='color: green;'>تم قراءة " . count($activeCategories) . " فئة نشطة</span><br>";
    $testResults['read_multiple'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في قراءة الفئات النشطة</span><br>";
    $testResults['read_multiple'] = false;
}

// البحث في النصوص
$searchResults = $db->search('categories', 'اختبار', ['name', 'description']);
if (!empty($searchResults)) {
    echo "✅ <span style='color: green;'>تم العثور على " . count($searchResults) . " نتيجة بحث</span><br>";
    $testResults['search'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في البحث</span><br>";
    $testResults['search'] = false;
}

// عد السجلات
$totalUsers = $db->count('users');
if ($totalUsers > 0) {
    echo "✅ <span style='color: green;'>إجمالي المستخدمين: {$totalUsers}</span><br>";
    $testResults['count'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في عد المستخدمين</span><br>";
    $testResults['count'] = false;
}

// ===================================================================
// 3. اختبار عمليات UPDATE (تحديث)
// ===================================================================
echo "<h2>3️⃣ اختبار عمليات UPDATE (تحديث)</h2>";

// تحديث سجل واحد
if ($categoryId) {
    $updateData = [
        'name' => 'فئة اختبار محدثة',
        'description' => 'تم تحديث الوصف',
        'color' => '#4ecdc4'
    ];
    
    $updateResult = $db->update('categories', $categoryId, $updateData);
    if ($updateResult) {
        echo "✅ <span style='color: green;'>تم تحديث الفئة بنجاح</span><br>";
        $testResults['update_single'] = true;
        
        // التحقق من التحديث
        $updatedCategory = $db->find('categories', $categoryId);
        if ($updatedCategory['name'] === 'فئة اختبار محدثة') {
            echo "✅ <span style='color: green;'>تم التحقق من التحديث بنجاح</span><br>";
        }
    } else {
        echo "❌ <span style='color: red;'>فشل في تحديث الفئة</span><br>";
        $testResults['update_single'] = false;
    }
}

// تحديث متعدد بشروط
$bulkUpdateResult = $db->updateWhere('categories', 
    ['name LIKE' => '%اختبار%'], 
    ['sort_order' => 1000]
);
if ($bulkUpdateResult) {
    echo "✅ <span style='color: green;'>تم التحديث المتعدد بنجاح</span><br>";
    $testResults['update_multiple'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في التحديث المتعدد</span><br>";
    $testResults['update_multiple'] = false;
}

// زيادة قيمة رقمية
if ($courseId) {
    $incrementResult = $db->increment('courses', $courseId, 'views_count', 5);
    if ($incrementResult) {
        echo "✅ <span style='color: green;'>تم زيادة عدد المشاهدات بنجاح</span><br>";
        $testResults['increment'] = true;
    } else {
        echo "❌ <span style='color: red;'>فشل في زيادة عدد المشاهدات</span><br>";
        $testResults['increment'] = false;
    }
}

// ===================================================================
// 4. اختبار العمليات المتقدمة
// ===================================================================
echo "<h2>4️⃣ اختبار العمليات المتقدمة</h2>";

// المعاملات (Transactions)
try {
    $db->beginTransaction();
    
    // إنشاء عدة سجلات في معاملة واحدة
    $mealCategoryId = $db->create('meal_categories', [
        'name' => 'فئة وجبة اختبار',
        'description' => 'فئة للاختبار',
        'is_active' => 1
    ]);
    
    $mealId = $db->create('meals', [
        'name' => 'وجبة اختبار',
        'description' => 'وجبة للاختبار',
        'category_id' => $mealCategoryId,
        'calories' => 250,
        'prep_time' => 15,
        'meal_type' => 'breakfast',
        'created_by' => $userId
    ]);
    
    $db->commit();
    
    if ($mealCategoryId && $mealId) {
        echo "✅ <span style='color: green;'>تم تنفيذ المعاملة بنجاح</span><br>";
        $testResults['transaction'] = true;
    } else {
        throw new Exception("فشل في إنشاء السجلات");
    }
    
} catch (Exception $e) {
    $db->rollback();
    echo "❌ <span style='color: red;'>فشل في المعاملة: " . $e->getMessage() . "</span><br>";
    $testResults['transaction'] = false;
}

// استعلام مخصص
$customQuery = "SELECT c.name as category_name, COUNT(co.id) as courses_count 
                FROM categories c 
                LEFT JOIN courses co ON c.id = co.category_id 
                GROUP BY c.id, c.name 
                ORDER BY courses_count DESC 
                LIMIT 5";

$customResults = $db->query($customQuery);
if (!empty($customResults)) {
    echo "✅ <span style='color: green;'>تم تنفيذ الاستعلام المخصص بنجاح - " . count($customResults) . " نتيجة</span><br>";
    $testResults['custom_query'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في الاستعلام المخصص</span><br>";
    $testResults['custom_query'] = false;
}

// ===================================================================
// 5. اختبار عمليات DELETE (حذف)
// ===================================================================
echo "<h2>5️⃣ اختبار عمليات DELETE (حذف)</h2>";

// حذف سجل واحد
if (isset($mealId)) {
    $deleteResult = $db->delete('meals', $mealId);
    if ($deleteResult) {
        echo "✅ <span style='color: green;'>تم حذف الوجبة بنجاح</span><br>";
        $testResults['delete_single'] = true;
    } else {
        echo "❌ <span style='color: red;'>فشل في حذف الوجبة</span><br>";
        $testResults['delete_single'] = false;
    }
}

// حذف متعدد بشروط
$deleteMultipleResult = $db->deleteWhere('meal_categories', ['name LIKE' => '%اختبار%']);
if ($deleteMultipleResult) {
    echo "✅ <span style='color: green;'>تم الحذف المتعدد بنجاح</span><br>";
    $testResults['delete_multiple'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في الحذف المتعدد</span><br>";
    $testResults['delete_multiple'] = false;
}

// تنظيف البيانات التجريبية
if ($courseId) $db->delete('courses', $courseId);
if ($categoryId) $db->delete('categories', $categoryId);
if ($userId) $db->delete('users', $userId);

// ===================================================================
// 6. اختبار الدوال المساعدة السريعة
// ===================================================================
echo "<h2>6️⃣ اختبار الدوال المساعدة السريعة</h2>";

// اختبار الدوال السريعة
$quickTestId = dbCreate('system_settings', [
    'setting_key' => 'test_setting_' . time(),
    'setting_value' => 'test_value',
    'category' => 'test'
]);

if ($quickTestId) {
    echo "✅ <span style='color: green;'>دالة dbCreate تعمل بنجاح</span><br>";
    
    $quickTestRecord = dbFind('system_settings', $quickTestId);
    if ($quickTestRecord) {
        echo "✅ <span style='color: green;'>دالة dbFind تعمل بنجاح</span><br>";
        
        $updateQuickResult = dbUpdate('system_settings', $quickTestId, ['setting_value' => 'updated_value']);
        if ($updateQuickResult) {
            echo "✅ <span style='color: green;'>دالة dbUpdate تعمل بنجاح</span><br>";
        }
        
        $deleteQuickResult = dbDelete('system_settings', $quickTestId);
        if ($deleteQuickResult) {
            echo "✅ <span style='color: green;'>دالة dbDelete تعمل بنجاح</span><br>";
        }
    }
}

// ===================================================================
// 7. ملخص النتائج
// ===================================================================
echo "<h2>📊 ملخص نتائج الاختبار</h2>";

$totalTests = count($testResults);
$passedTests = count(array_filter($testResults));
$failedTests = $totalTests - $passedTests;

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 10px 0;'>";
echo "<h3>📈 الإحصائيات:</h3>";
echo "<strong>إجمالي الاختبارات:</strong> {$totalTests}<br>";
echo "<strong style='color: green;'>الاختبارات الناجحة:</strong> {$passedTests}<br>";
echo "<strong style='color: red;'>الاختبارات الفاشلة:</strong> {$failedTests}<br>";
echo "<strong>معدل النجاح:</strong> " . round(($passedTests / $totalTests) * 100, 2) . "%<br>";
echo "</div>";

echo "<h3>📋 تفاصيل النتائج:</h3>";
echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'><th style='padding: 10px; border: 1px solid #ddd;'>الاختبار</th><th style='padding: 10px; border: 1px solid #ddd;'>النتيجة</th></tr>";

foreach ($testResults as $test => $result) {
    $status = $result ? '✅ نجح' : '❌ فشل';
    $color = $result ? 'green' : 'red';
    echo "<tr><td style='padding: 10px; border: 1px solid #ddd;'>{$test}</td><td style='padding: 10px; border: 1px solid #ddd; color: {$color};'>{$status}</td></tr>";
}

echo "</table>";

if ($passedTests === $totalTests) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 تهانينا!</h3>";
    echo "<p>جميع اختبارات CRUD نجحت بنسبة 100%! النظام جاهز للاستخدام.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ تحذير!</h3>";
    echo "<p>بعض الاختبارات فشلت. يرجى مراجعة الأخطاء وإصلاحها قبل الاستخدام.</p>";
    echo "</div>";
}

echo "<div style='background: #cff4fc; padding: 15px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<a href='admin/database_viewer.php' style='display: inline-block; padding: 8px 15px; background: #0d6efd; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>عارض قاعدة البيانات</a>";
echo "<a href='visitor_homepage.php' style='display: inline-block; padding: 8px 15px; background: #6f42c1; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a>";
echo "<a href='meals.php' style='display: inline-block; padding: 8px 15px; background: #198754; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الوجبات</a>";
echo "</div>";

echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

h1 {
    color: white;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 30px;
}

h2 {
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 5px;
    margin-top: 30px;
}

h3 {
    color: #495057;
    margin-top: 20px;
}

table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-2px);
    transition: all 0.3s ease;
}
</style>
