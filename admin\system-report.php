<?php
session_start();
require_once '../includes/database_manager_clean.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// جلب تقرير شامل للنظام
try {
    // إحصائيات المستخدمين
    $userStats = [
        'total_users' => $dbClean->count('users'),
        'active_users' => $dbClean->count('users', ['status' => 'active']),
        'new_users_today' => $dbClean->count('users', ['DATE(created_at)' => date('Y-m-d')]),
        'new_users_week' => $dbClean->query("SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")[0]['count'],
        'new_users_month' => $dbClean->query("SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)")[0]['count'],
        'students' => $dbClean->count('users', ['role' => 'student']),
        'instructors' => $dbClean->count('users', ['role' => 'instructor']),
        'admins' => $dbClean->count('users', ['role' => 'admin'])
    ];

    // إحصائيات الكورسات
    $courseStats = [
        'total_courses' => $dbClean->count('courses'),
        'published_courses' => $dbClean->count('courses', ['status' => 'published']),
        'draft_courses' => $dbClean->count('courses', ['status' => 'draft']),
        'featured_courses' => $dbClean->count('courses', ['is_featured' => 1]),
        'free_courses' => $dbClean->count('courses', ['is_free' => 1]),
        'paid_courses' => $dbClean->count('courses', ['is_free' => 0]),
        'new_courses_today' => $dbClean->count('courses', ['DATE(created_at)' => date('Y-m-d')]),
        'new_courses_week' => $dbClean->query("SELECT COUNT(*) as count FROM courses WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")[0]['count']
    ];

    // إحصائيات التسجيلات
    $enrollmentStats = [
        'total_enrollments' => $dbClean->count('course_enrollments'),
        'active_enrollments' => $dbClean->count('course_enrollments', ['status' => 'active']),
        'completed_enrollments' => $dbClean->count('course_enrollments', ['status' => 'completed']),
        'pending_enrollments' => $dbClean->count('course_enrollments', ['status' => 'pending']),
        'new_enrollments_today' => $dbClean->count('course_enrollments', ['DATE(enrolled_at)' => date('Y-m-d')]),
        'new_enrollments_week' => $dbClean->query("SELECT COUNT(*) as count FROM course_enrollments WHERE enrolled_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")[0]['count']
    ];

    // إحصائيات المدفوعات
    $paymentStats = [
        'total_payments' => $dbClean->count('payments'),
        'completed_payments' => $dbClean->count('payments', ['status' => 'completed']),
        'pending_payments' => $dbClean->count('payments', ['status' => 'pending']),
        'failed_payments' => $dbClean->count('payments', ['status' => 'failed'])
    ];

    // حساب الإيرادات
    $revenueQuery = "SELECT 
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
        SUM(CASE WHEN status = 'completed' AND DATE(payment_date) = CURDATE() THEN amount ELSE 0 END) as today_revenue,
        SUM(CASE WHEN status = 'completed' AND payment_date >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN amount ELSE 0 END) as week_revenue,
        SUM(CASE WHEN status = 'completed' AND MONTH(payment_date) = MONTH(CURRENT_DATE()) AND YEAR(payment_date) = YEAR(CURRENT_DATE()) THEN amount ELSE 0 END) as month_revenue,
        SUM(CASE WHEN status = 'completed' THEN commission_amount ELSE 0 END) as total_commission
        FROM payments";
    
    $revenueData = $dbClean->query($revenueQuery);
    $revenueStats = $revenueData[0] ?? [
        'total_revenue' => 0,
        'today_revenue' => 0,
        'week_revenue' => 0,
        'month_revenue' => 0,
        'total_commission' => 0
    ];

    // إحصائيات الوجبات
    $mealStats = [
        'total_meals' => $dbClean->count('meals'),
        'published_meals' => $dbClean->count('meals', ['status' => 'published']),
        'meal_categories' => $dbClean->count('meal_categories'),
        'breakfast_meals' => $dbClean->count('meals', ['meal_type' => 'breakfast']),
        'lunch_meals' => $dbClean->count('meals', ['meal_type' => 'lunch']),
        'dinner_meals' => $dbClean->count('meals', ['meal_type' => 'dinner'])
    ];

    // إحصائيات الملفات
    $fileStats = [
        'total_files' => $dbClean->count('file_uploads'),
        'video_files' => $dbClean->count('file_uploads', ['category' => 'video']),
        'document_files' => $dbClean->count('file_uploads', ['category' => 'document']),
        'image_files' => $dbClean->count('file_uploads', ['category' => 'image'])
    ];

    // إحصائيات الأنشطة
    $activityStats = [
        'total_activities' => $dbClean->count('activity_logs'),
        'today_activities' => $dbClean->count('activity_logs', ['DATE(created_at)' => date('Y-m-d')]),
        'week_activities' => $dbClean->query("SELECT COUNT(*) as count FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")[0]['count']
    ];

    // أفضل الكورسات (حسب عدد التسجيلات)
    $topCourses = $dbClean->query("
        SELECT c.title, c.price, c.is_free, COUNT(ce.id) as enrollments_count,
               u.name as instructor_name
        FROM courses c 
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id 
        LEFT JOIN users u ON c.instructor_id = u.id 
        WHERE c.status = 'published'
        GROUP BY c.id, c.title, c.price, c.is_free, u.name 
        ORDER BY enrollments_count DESC 
        LIMIT 10
    ");

    // أفضل المدربين (حسب عدد الطلاب)
    $topInstructors = $dbClean->query("
        SELECT u.name, u.email, COUNT(DISTINCT ce.student_id) as students_count,
               COUNT(DISTINCT c.id) as courses_count
        FROM users u 
        LEFT JOIN courses c ON u.id = c.instructor_id 
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id 
        WHERE u.role = 'instructor' AND c.status = 'published'
        GROUP BY u.id, u.name, u.email 
        ORDER BY students_count DESC 
        LIMIT 10
    ");

    // أحدث الأنشطة
    $recentActivities = $dbClean->query("
        SELECT al.*, u.name as user_name 
        FROM activity_logs al 
        LEFT JOIN users u ON al.user_id = u.id 
        ORDER BY al.created_at DESC 
        LIMIT 20
    ");

} catch (Exception $e) {
    error_log("System report error: " . $e->getMessage());
    $userStats = $courseStats = $enrollmentStats = $paymentStats = $revenueStats = 
    $mealStats = $fileStats = $activityStats = [];
    $topCourses = $topInstructors = $recentActivities = [];
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير النظام الشامل - لوحة التحكم</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card.primary { border-left-color: #667eea; }
        .stats-card.success { border-left-color: #28a745; }
        .stats-card.warning { border-left-color: #ffc107; }
        .stats-card.danger { border-left-color: #dc3545; }
        .stats-card.info { border-left-color: #17a2b8; }
        .stats-card.secondary { border-left-color: #6c757d; }
        .stats-card.dark { border-left-color: #343a40; }
        
        .data-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .section-title {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                background: white !important;
            }
            
            .main-header {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
            }
            
            .stats-card, .data-card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="main-header no-print">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-2">
                        <i class="fas fa-chart-line me-3"></i>
                        تقرير النظام الشامل
                    </h1>
                    <p class="lead mb-0">تقرير مفصل عن جميع إحصائيات وبيانات النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <button onclick="window.print()" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-print me-2"></i>
                        طباعة التقرير
                    </button>
                    <a href="dashboard.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- معلومات التقرير -->
        <div class="data-card">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="section-title">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات التقرير
                    </h5>
                    <p><strong>تاريخ التقرير:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                    <p><strong>المنشئ:</strong> <?php echo htmlspecialchars($_SESSION['name']); ?></p>
                    <p><strong>نوع التقرير:</strong> تقرير شامل للنظام</p>
                </div>
                <div class="col-md-6">
                    <h5 class="section-title">
                        <i class="fas fa-database me-2"></i>
                        حالة النظام
                    </h5>
                    <p><strong>حالة قاعدة البيانات:</strong> <span class="badge bg-success">متصلة</span></p>
                    <p><strong>إجمالي الجداول:</strong> <?php echo count($dbClean->query("SHOW TABLES")); ?></p>
                    <p><strong>آخر تحديث:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                </div>
            </div>
        </div>
