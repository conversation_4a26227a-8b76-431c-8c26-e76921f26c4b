<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../config/zoom.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$success = '';
$error = '';

// جلب قائمة الكورسات النشطة للمدرب
// جلب قائمة الكورسات النشطة للمدرب
try {
    $instructor_id = $_SESSION['user_id'];
    
    // التحقق من صلاحية المدرب
    $stmt = $conn->prepare("SELECT id FROM users WHERE id = ? AND role = 'instructor' AND status = 'active'");
    $stmt->execute([$instructor_id]);
    if (!$stmt->fetch()) {
        throw new Exception('غير مصرح لك بجدولة جلسات');
    }

    // جلب الكورسات النشطة
    $stmt = $conn->prepare("SELECT id, title FROM courses WHERE instructor_id = ? AND status = 'active'");
    $stmt->execute([$instructor_id]);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($courses)) {
        $error = 'لا توجد كورسات نشطة لجدولة جلسات لها';
    }
} catch (Exception $e) {
    $error = 'حدث خطأ أثناء جلب قائمة الكورسات';
    error_log($e->getMessage());
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $instructor_id = $_SESSION['user_id'];
        $course_id = isset($_POST['course_id']) ? (int)$_POST['course_id'] : 0;
        $title = isset($_POST['title']) ? trim($_POST['title']) : '';
        $date = isset($_POST['date']) ? $_POST['date'] : '';
        $time = isset($_POST['time']) ? $_POST['time'] : '';
        $duration = isset($_POST['duration']) ? (int)$_POST['duration'] : 60;
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';

        // التحقق من البيانات المطلوبة
        if (empty($course_id) || empty($title) || empty($date) || empty($time) || empty($duration)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }

        // التحقق من ملكية الكورس
        $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ? AND status = 'active'");
        $stmt->execute([$course_id, $instructor_id]);
        if (!$stmt->fetch()) {
            throw new Exception('غير مصرح لك بإضافة جلسات لهذا الكورس');
        }

        // التحقق من التاريخ والوقت
        $start_time = date('Y-m-d H:i:s', strtotime("$date $time"));
        if (strtotime($start_time) <= time()) {
            throw new Exception('يجب أن يكون موعد الجلسة في المستقبل');
        }

        // التحقق من مدة الجلسة
        if ($duration < 15 || $duration > 180) {
            throw new Exception('مدة الجلسة يجب أن تكون بين 15 و 180 دقيقة');
        }

        // إنشاء معرف اجتماع مؤقت
        $zoom_meeting_id = 'meeting_' . time() . '_' . rand(1000, 9999);
        $zoom_join_url = 'https://zoom.us/j/' . $zoom_meeting_id;

        // بدء المعاملة
        $conn->beginTransaction();

        try {
            // حفظ بيانات الجلسة
            $stmt = $conn->prepare("INSERT INTO sessions (course_id, title, description, start_time, duration, zoom_meeting_id, zoom_meeting_password, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, 'scheduled', NOW())");
            $stmt->execute([
                $course_id,
                $title,
                $description,
                $start_time,
                $duration,
                $zoom_meeting_id,
                '123456'
            ]);

            $session_id = $conn->lastInsertId();

            // إرسال إشعارات للطلاب المسجلين في الكورس
            try {
                $stmt = $conn->prepare("INSERT INTO notifications (user_id, title, message, type, category, created_at)
                                      SELECT ce.student_id, ?, ?, 'info', 'session', NOW()
                                      FROM course_enrollments ce
                                      WHERE ce.course_id = ? AND ce.status = 'active'");
                $stmt->execute([
                    'جلسة جديدة',
                    "تم إضافة جلسة جديدة في كورس: $title",
                    $course_id
                ]);
            } catch (PDOException $e) {
                // تجاهل أخطاء الإشعارات
                error_log("Notification error: " . $e->getMessage());
            }

            // تسجيل النشاط
            try {
                $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, details, created_at) VALUES (?, ?, ?, NOW())");
                $stmt->execute([$instructor_id, 'جدولة جلسة', "تم جدولة جلسة جديدة: $title"]);
            } catch (PDOException $e) {
                // تجاهل أخطاء تسجيل النشاط
                error_log("Activity log error: " . $e->getMessage());
            }

            $conn->commit();

            $success = 'تم جدولة الجلسة بنجاح! معرف الجلسة: ' . $session_id;
            // header("Location: session-details.php?id=" . $session_id);
            // exit();

        } catch (Exception $e) {
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            throw $e;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
        error_log($e->getMessage());
    }
}

// تضمين الـ header بعد معالجة النموذج
$pageTitle = 'جدولة جلسة جديدة';
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">جدولة جلسة جديدة</h5>
                </div>
                <div class="card-body">
                    <?php if ($success): ?>
                        <div class="alert alert-success" role="alert">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger" role="alert">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="form-group mb-3">
                            <label for="course_id">اختر الكورس</label>
                            <select class="form-control" id="course_id" name="course_id" required>
                                <option value="">اختر الكورس</option>
                                <?php foreach ($courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>" <?php echo (isset($_POST['course_id']) && $_POST['course_id'] == $course['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="title">عنوان الجلسة</label>
                            <input type="text" class="form-control" id="title" name="title" required
                                value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>">
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="date">تاريخ الجلسة</label>
                                    <input type="date" class="form-control" id="date" name="date" required
                                        min="<?php echo date('Y-m-d'); ?>"
                                        value="<?php echo isset($_POST['date']) ? htmlspecialchars($_POST['date']) : ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="time">وقت الجلسة</label>
                                    <input type="time" class="form-control" id="time" name="time" required
                                        value="<?php echo isset($_POST['time']) ? htmlspecialchars($_POST['time']) : ''; ?>">
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="duration">مدة الجلسة (بالدقائق)</label>
                            <input type="number" class="form-control" id="duration" name="duration" min="15" max="240" required
                                value="<?php echo isset($_POST['duration']) ? htmlspecialchars($_POST['duration']) : '60'; ?>">
                        </div>

                        <div class="form-group mb-3">
                            <label for="description">وصف الجلسة</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">جدولة الجلسة</button>
                            <a href="dashboard.php" class="btn btn-secondary">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>