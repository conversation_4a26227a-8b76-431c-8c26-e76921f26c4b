<?php
require_once 'config/database.php';
require_once 'includes/session_config.php';

echo "<h1>حالة النظام - نظام إدارة التعلم</h1>";

try {
    // إحصائيات عامة
    echo "<h2>📊 الإحصائيات العامة</h2>";
    
    $stats = [];
    
    // عدد المستخدمين
    $stmt = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    $user_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>المستخدمين:</h3>";
    echo "<ul>";
    foreach ($user_stats as $stat) {
        $role_names = [
            'admin' => 'المدراء',
            'instructor' => 'المدربين', 
            'student' => 'الطلاب'
        ];
        $role_name = $role_names[$stat['role']] ?? $stat['role'];
        echo "<li><strong>$role_name:</strong> " . $stat['count'] . "</li>";
    }
    echo "</ul>";
    
    // عدد الكورسات
    $stmt = $conn->query("SELECT status, COUNT(*) as count FROM courses GROUP BY status");
    $course_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>الكورسات:</h3>";
    echo "<ul>";
    foreach ($course_stats as $stat) {
        echo "<li><strong>" . ucfirst($stat['status']) . ":</strong> " . $stat['count'] . "</li>";
    }
    echo "</ul>";
    
    // التسجيلات
    $stmt = $conn->query("SELECT COUNT(*) FROM course_enrollments");
    $enrollment_count = $stmt->fetchColumn();
    echo "<h3>التسجيلات:</h3>";
    echo "<ul><li><strong>إجمالي التسجيلات:</strong> $enrollment_count</li></ul>";
    
    // طلبات الانضمام
    $stmt = $conn->query("SELECT status, COUNT(*) as count FROM join_requests GROUP BY status");
    $request_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>طلبات الانضمام:</h3>";
    echo "<ul>";
    foreach ($request_stats as $stat) {
        $status_names = [
            'pending' => 'معلقة',
            'approved' => 'موافق عليها',
            'rejected' => 'مرفوضة'
        ];
        $status_name = $status_names[$stat['status']] ?? $stat['status'];
        echo "<li><strong>$status_name:</strong> " . $stat['count'] . "</li>";
    }
    echo "</ul>";
    
    // حالة تسجيل الدخول الحالي
    echo "<h2>👤 حالة تسجيل الدخول</h2>";
    if (isset($_SESSION['user_id'])) {
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $current_user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($current_user) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
            echo "<h4>✅ مسجل الدخول</h4>";
            echo "<ul>";
            echo "<li><strong>الاسم:</strong> " . htmlspecialchars($current_user['username']) . "</li>";
            echo "<li><strong>البريد الإلكتروني:</strong> " . htmlspecialchars($current_user['email']) . "</li>";
            echo "<li><strong>الدور:</strong> " . htmlspecialchars($current_user['role']) . "</li>";
            echo "</ul>";
            echo "</div>";
            
            // إحصائيات خاصة بالمستخدم
            if ($current_user['role'] === 'student') {
                $stmt = $conn->prepare("SELECT COUNT(*) FROM course_enrollments WHERE student_id = ?");
                $stmt->execute([$current_user['id']]);
                $my_enrollments = $stmt->fetchColumn();
                
                $stmt = $conn->prepare("SELECT COUNT(*) FROM join_requests WHERE student_id = ?");
                $stmt->execute([$current_user['id']]);
                $my_requests = $stmt->fetchColumn();
                
                echo "<h4>إحصائياتي:</h4>";
                echo "<ul>";
                echo "<li><strong>الكورسات المسجل بها:</strong> $my_enrollments</li>";
                echo "<li><strong>طلبات الانضمام:</strong> $my_requests</li>";
                echo "</ul>";
            }
            
            if ($current_user['role'] === 'instructor') {
                $stmt = $conn->prepare("SELECT COUNT(*) FROM courses WHERE instructor_id = ?");
                $stmt->execute([$current_user['id']]);
                $my_courses = $stmt->fetchColumn();
                
                $stmt = $conn->prepare("
                    SELECT COUNT(*) FROM join_requests jr 
                    INNER JOIN courses c ON jr.course_id = c.id 
                    WHERE c.instructor_id = ? AND jr.status = 'pending'
                ");
                $stmt->execute([$current_user['id']]);
                $pending_requests = $stmt->fetchColumn();
                
                echo "<h4>إحصائياتي:</h4>";
                echo "<ul>";
                echo "<li><strong>كورساتي:</strong> $my_courses</li>";
                echo "<li><strong>طلبات الانضمام المعلقة:</strong> $pending_requests</li>";
                echo "</ul>";
            }
        }
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ غير مسجل الدخول</h4>";
        echo "<p><a href='login.php'>تسجيل الدخول</a></p>";
        echo "</div>";
    }
    
    // اختبارات النظام
    echo "<h2>🧪 اختبارات النظام</h2>";
    
    echo "<h3>صفحات الطلاب:</h3>";
    echo "<ul>";
    echo "<li><a href='student/dashboard.php' target='_blank'>لوحة تحكم الطالب</a></li>";
    echo "<li><a href='student/courses.php' target='_blank'>كورساتي</a></li>";
    echo "<li><a href='student/browse-courses.php' target='_blank'>تصفح الكورسات</a></li>";
    echo "</ul>";
    
    echo "<h3>صفحات المدربين:</h3>";
    echo "<ul>";
    echo "<li><a href='instructor/dashboard.php' target='_blank'>لوحة تحكم المدرب</a></li>";
    echo "<li><a href='instructor/courses.php' target='_blank'>كورسات المدرب</a></li>";
    echo "<li><a href='instructor/course-join-requests.php?course_id=1' target='_blank'>طلبات الانضمام</a></li>";
    echo "</ul>";
    
    echo "<h3>صفحات الإدارة:</h3>";
    echo "<ul>";
    echo "<li><a href='admin/dashboard.php' target='_blank'>لوحة تحكم الإدارة</a></li>";
    echo "<li><a href='admin/manage-courses.php' target='_blank'>إدارة الكورسات</a></li>";
    echo "<li><a href='admin/manage-users.php' target='_blank'>إدارة المستخدمين</a></li>";
    echo "</ul>";
    
    echo "<h3>اختبارات خاصة:</h3>";
    echo "<ul>";
    echo "<li><a href='test-join-course.php' target='_blank'>إرسال طلب انضمام</a></li>";
    echo "<li><a href='quick-test-join.php' target='_blank'>اختبار سريع لطلبات الانضمام</a></li>";
    echo "<li><a href='check-student-enrollments.php' target='_blank'>فحص تسجيلات الطالب</a></li>";
    echo "</ul>";
    
    // معلومات تسجيل الدخول
    echo "<h2>🔑 معلومات تسجيل الدخول</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>الدور</th><th>اسم المستخدم</th><th>كلمة المرور</th><th>رابط تسجيل الدخول</th>";
    echo "</tr>";
    
    $test_users = [
        ['admin', 'admin', '123456', 'login.php'],
        ['instructor', 'instructor', '123456', 'login.php'],
        ['student', 'student', '123456', 'login.php']
    ];
    
    foreach ($test_users as $user) {
        echo "<tr>";
        echo "<td>" . $user[0] . "</td>";
        echo "<td>" . $user[1] . "</td>";
        echo "<td>" . $user[2] . "</td>";
        echo "<td><a href='" . $user[3] . "' target='_blank'>تسجيل دخول</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>✅ النظام جاهز للاستخدام!</h2>";
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px;'>";
    echo "<h4>الميزات المتاحة:</h4>";
    echo "<ul>";
    echo "<li>✅ تسجيل الدخول لجميع الأدوار</li>";
    echo "<li>✅ تصفح الكورسات للطلاب</li>";
    echo "<li>✅ عرض الكورسات المسجل بها الطالب</li>";
    echo "<li>✅ إرسال طلبات الانضمام</li>";
    echo "<li>✅ موافقة المدربين على طلبات الانضمام</li>";
    echo "<li>✅ تسجيل الطلاب تلقائياً عند الموافقة</li>";
    echo "<li>✅ إدارة الكورسات والمستخدمين</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
