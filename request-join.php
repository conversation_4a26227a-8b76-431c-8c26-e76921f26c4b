<?php
require_once 'includes/session_config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

$course_id = isset($_POST['course_id']) ? (int)$_POST['course_id'] : 0;
$message = trim($_POST['message'] ?? '');

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // التحقق من وجود الكورس
        $stmt = $conn->prepare("SELECT id, title, instructor_id FROM courses WHERE id = ? AND status = 'active'");
        $stmt->execute([$course_id]);
        $course = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$course) {
            $response['message'] = 'الكورس غير موجود أو غير متاح';
        } else {
            // التأكد من وجود جدول join_requests
            $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
            if ($stmt->rowCount() == 0) {
                // إنشاء الجدول
                $conn->exec("CREATE TABLE join_requests (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    student_id INT NOT NULL,
                    course_id INT NOT NULL,
                    message TEXT,
                    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed_at TIMESTAMP NULL,
                    processed_by INT NULL,
                    INDEX idx_student_id (student_id),
                    INDEX idx_course_id (course_id),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
            }
            
            // التحقق من عدم وجود طلب معلق
            $stmt = $conn->prepare("SELECT id FROM join_requests WHERE student_id = ? AND course_id = ? AND status = 'pending'");
            $stmt->execute([$_SESSION['user_id'], $course_id]);
            
            if ($stmt->fetch()) {
                $response['message'] = 'لديك طلب انضمام معلق بالفعل لهذا الكورس';
            } else {
                // التحقق من عدم التسجيل المسبق
                $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE student_id = ? AND course_id = ?");
                $stmt->execute([$_SESSION['user_id'], $course_id]);
                
                if ($stmt->fetch()) {
                    $response['message'] = 'أنت مسجل بالفعل في هذا الكورس';
                } else {
                    // إرسال طلب الانضمام
                    $stmt = $conn->prepare("
                        INSERT INTO join_requests (student_id, course_id, message) 
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$_SESSION['user_id'], $course_id, $message]);
                    
                    $response['success'] = true;
                    $response['message'] = 'تم إرسال طلب الانضمام بنجاح! سيتم مراجعته من قبل المدرب.';
                }
            }
        }
    } catch (PDOException $e) {
        $response['message'] = 'حدث خطأ أثناء إرسال الطلب: ' . $e->getMessage();
    }
}

// إرجاع الاستجابة
header('Content-Type: application/json');
echo json_encode($response);
?>
