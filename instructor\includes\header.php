<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? 'لوحة تحكم المدرب'; ?> - منصة التعلم</title>
    
    <!-- الخطوط المحسنة -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Bootstrap و Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- ملفات CSS المخصصة -->
    <link href="../assets/css/main.css" rel="stylesheet">
    <link href="../assets/css/components.css" rel="stylesheet">
    <link href="assets/css/modern-style.css" rel="stylesheet">
    <link href="assets/css/enhanced-ui.css" rel="stylesheet">
    
    <style>
        :root {
            --instructor-primary: #28a745;
            --instructor-secondary: #20c997;
            --instructor-accent: #17a2b8;
            --instructor-dark: #343a40;
            --instructor-light: #f8f9fa;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--gray-50);
        }

        .instructor-navbar {
            background: linear-gradient(135deg, var(--instructor-primary) 0%, var(--instructor-secondary) 100%);
            box-shadow: 0 2px 20px rgba(40, 167, 69, 0.15);
            padding: 0;
        }

        .navbar-brand-instructor {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white !important;
            font-weight: 700;
            font-size: 1.25rem;
            text-decoration: none;
            padding: 1rem;
        }

        .navbar-brand-instructor:hover {
            color: rgba(255,255,255,0.9) !important;
        }

        .brand-icon {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .nav-link-instructor {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            padding: 1rem 1.5rem !important;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-link-instructor:hover,
        .nav-link-instructor.active {
            color: white !important;
            background: rgba(255,255,255,0.1);
        }

        .nav-link-instructor::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: white;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link-instructor:hover::after,
        .nav-link-instructor.active::after {
            width: 80%;
        }

        .dropdown-menu-instructor {
            border: none;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border-radius: 12px;
            padding: 0.5rem 0;
            margin-top: 0.5rem;
        }

        .dropdown-item-instructor {
            padding: 0.75rem 1.5rem;
            color: var(--gray-700);
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .dropdown-item-instructor:hover {
            background: var(--instructor-light);
            color: var(--instructor-primary);
            transform: translateX(-5px);
        }

        .dropdown-item-instructor i {
            width: 20px;
            text-align: center;
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 8px;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quick-actions {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .quick-action-btn {
            background: linear-gradient(135deg, var(--instructor-primary), var(--instructor-secondary));
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 0.25rem;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .breadcrumb-instructor {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .breadcrumb-instructor .breadcrumb {
            margin: 0;
        }

        .breadcrumb-instructor .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
            color: var(--gray-500);
        }

        .mobile-menu-toggle {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        @media (max-width: 991px) {
            .navbar-collapse {
                background: rgba(255,255,255,0.95);
                margin-top: 1rem;
                border-radius: 12px;
                padding: 1rem;
            }

            .nav-link-instructor {
                color: var(--instructor-primary) !important;
                border-radius: 8px;
                margin: 0.25rem 0;
            }

            .nav-link-instructor:hover {
                background: var(--instructor-light);
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل الرئيسي -->
    <nav class="navbar navbar-expand-lg instructor-navbar sticky-top">
        <div class="container-fluid">
            <!-- الشعار -->
            <a class="navbar-brand-instructor" href="dashboard.php">
                <div class="brand-icon">
                    <i class="fas fa-chalkboard-teacher"></i>
                </div>
                <span>لوحة المدرب</span>
            </a>

            <!-- زر القائمة للهاتف -->
            <button class="navbar-toggler mobile-menu-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#instructorNavbar">
                <i class="fas fa-bars"></i>
            </button>

            <!-- القائمة الرئيسية -->
            <div class="collapse navbar-collapse" id="instructorNavbar">
                <ul class="navbar-nav me-auto">
                    <!-- الرئيسية -->
                    <li class="nav-item">
                        <a class="nav-link-instructor <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="dashboard.php">
                            <i class="fas fa-home"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>

                    <!-- إدارة الكورسات -->
                    <li class="nav-item dropdown">
                        <a class="nav-link-instructor dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-graduation-cap"></i>
                            <span>كورساتي</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-instructor">
                            <li><a class="dropdown-item-instructor" href="courses.php">
                                <i class="fas fa-list"></i>جميع الكورسات
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="add-course.php">
                                <i class="fas fa-plus"></i>إضافة كورس جديد
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="live-courses.php">
                                <i class="fas fa-video"></i>الكورسات المباشرة
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="recorded-courses.php">
                                <i class="fas fa-play-circle"></i>الكورسات المسجلة
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item-instructor" href="course-analytics.php">
                                <i class="fas fa-chart-bar"></i>إحصائيات الكورسات
                            </a></li>
                        </ul>
                    </li>

                    <!-- إدارة الطلاب -->
                    <li class="nav-item dropdown">
                        <a class="nav-link-instructor dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users"></i>
                            <span>الطلاب</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-instructor">
                            <li><a class="dropdown-item-instructor" href="students.php">
                                <i class="fas fa-user-graduate"></i>جميع الطلاب
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="enrollment-requests.php">
                                <i class="fas fa-user-clock"></i>طلبات الانضمام
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="student-progress.php">
                                <i class="fas fa-chart-line"></i>تقدم الطلاب
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item-instructor" href="bulk-actions.php">
                                <i class="fas fa-tasks"></i>إجراءات جماعية
                            </a></li>
                        </ul>
                    </li>

                    <!-- الدرجات والتقييم -->
                    <li class="nav-item dropdown">
                        <a class="nav-link-instructor dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-star"></i>
                            <span>التقييم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-instructor">
                            <li><a class="dropdown-item-instructor" href="grades.php">
                                <i class="fas fa-medal"></i>إدارة الدرجات
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="assignments.php">
                                <i class="fas fa-clipboard-list"></i>الواجبات
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="quizzes.php">
                                <i class="fas fa-question-circle"></i>الاختبارات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item-instructor" href="certificates.php">
                                <i class="fas fa-certificate"></i>الشهادات
                            </a></li>
                        </ul>
                    </li>

                    <!-- الجلسات والمحتوى -->
                    <li class="nav-item dropdown">
                        <a class="nav-link-instructor dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-play"></i>
                            <span>المحتوى</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-instructor">
                            <li><a class="dropdown-item-instructor" href="sessions.php">
                                <i class="fas fa-calendar"></i>الجلسات المباشرة
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="videos.php">
                                <i class="fas fa-film"></i>فيديوهات الكورسات
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="session-videos.php">
                                <i class="fas fa-video"></i>فيديوهات الجلسات
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="materials.php">
                                <i class="fas fa-file-alt"></i>المواد التعليمية
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="resources.php">
                                <i class="fas fa-download"></i>الملفات والموارد
                            </a></li>
                        </ul>
                    </li>

                    <!-- التقارير -->
                    <li class="nav-item">
                        <a class="nav-link-instructor" href="reports.php">
                            <i class="fas fa-chart-pie"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                </ul>

                <!-- القائمة اليمنى -->
                <ul class="navbar-nav">
                    <!-- الإشعارات -->
                    <li class="nav-item dropdown">
                        <a class="nav-link-instructor position-relative" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-instructor dropdown-menu-end" style="width: 300px;">
                            <li class="dropdown-header">الإشعارات الجديدة</li>
                            <li><a class="dropdown-item-instructor" href="#">
                                <i class="fas fa-user-plus text-success"></i>
                                <div>
                                    <strong>طالب جديد</strong><br>
                                    <small>انضم أحمد محمد لكورس البرمجة</small>
                                </div>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item-instructor text-center" href="notifications.php">
                                عرض جميع الإشعارات
                            </a></li>
                        </ul>
                    </li>

                    <!-- الملف الشخصي -->
                    <li class="nav-item dropdown">
                        <a class="nav-link-instructor dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="user-avatar">
                                <?php echo strtoupper(substr($_SESSION['user_name'] ?? 'M', 0, 1)); ?>
                            </div>
                            <span><?php echo $_SESSION['user_name'] ?? 'المدرب'; ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-instructor dropdown-menu-end">
                            <li><a class="dropdown-item-instructor" href="profile.php">
                                <i class="fas fa-user"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="settings.php">
                                <i class="fas fa-cog"></i>الإعدادات
                            </a></li>
                            <li><a class="dropdown-item-instructor" href="earnings.php">
                                <i class="fas fa-dollar-sign"></i>الأرباح
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item-instructor" href="../logout.php">
                                <i class="fas fa-sign-out-alt text-danger"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- الإجراءات السريعة -->
    <div class="container-fluid mt-3">
        <div class="quick-actions">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="mb-2">الإجراءات السريعة:</h6>
                    <a href="add-course.php" class="quick-action-btn">
                        <i class="fas fa-plus"></i>إضافة كورس
                    </a>
                    <a href="create-session.php" class="quick-action-btn">
                        <i class="fas fa-video"></i>جلسة مباشرة
                    </a>
                    <a href="upload-video.php" class="quick-action-btn">
                        <i class="fas fa-upload"></i>رفع فيديو
                    </a>
                    <a href="add-material.php" class="quick-action-btn">
                        <i class="fas fa-file-upload"></i>إضافة مادة
                    </a>
                </div>
                <div class="col-md-4 text-end">
                    <small class="text-muted">
                        آخر تسجيل دخول: <?php echo date('Y-m-d H:i'); ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- مسار التنقل -->
    <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
    <div class="container-fluid">
        <div class="breadcrumb-instructor">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">الرئيسية</a></li>
                    <?php foreach ($breadcrumbs as $crumb): ?>
                        <?php if (isset($crumb['url'])): ?>
                            <li class="breadcrumb-item"><a href="<?php echo $crumb['url']; ?>"><?php echo $crumb['title']; ?></a></li>
                        <?php else: ?>
                            <li class="breadcrumb-item active"><?php echo $crumb['title']; ?></li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>
    <?php endif; ?>

    <!-- بداية المحتوى -->
    <div class="container-fluid">
