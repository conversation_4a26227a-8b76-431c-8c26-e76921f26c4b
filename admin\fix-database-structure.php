<?php
session_start();
require_once '../includes/database_manager_clean.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

function logMessage($message, $type = 'info') {
    $color = $type === 'success' ? 'green' : ($type === 'error' ? 'red' : ($type === 'warning' ? 'orange' : 'blue'));
    echo "<div style='color: $color; margin: 5px 0;'>$message</div>";
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح هيكل قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-database me-2"></i>إصلاح هيكل قاعدة البيانات</h4>
            </div>
            <div class="card-body">
                <?php
                try {
                    $conn = DatabaseManager::getInstance()->getConnection();
                    
                    echo "<h5>🔧 بدء إصلاح هيكل قاعدة البيانات...</h5>";
                    
                    // 1. إصلاح جدول course_enrollments
                    echo "<h6>📚 إصلاح جدول course_enrollments</h6>";
                    
                    // التحقق من وجود الجدول
                    $stmt = $conn->query("SHOW TABLES LIKE 'course_enrollments'");
                    if ($stmt->rowCount() == 0) {
                        logMessage("إنشاء جدول course_enrollments...", 'info');
                        $conn->exec("
                            CREATE TABLE course_enrollments (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                course_id INT NOT NULL,
                                student_id INT NOT NULL,
                                enrollment_type ENUM('free', 'paid', 'scholarship') DEFAULT 'free',
                                payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
                                payment_amount DECIMAL(10,2) DEFAULT 0.00,
                                payment_method VARCHAR(50) DEFAULT NULL,
                                payment_reference VARCHAR(255) DEFAULT NULL,
                                progress_percentage DECIMAL(5,2) DEFAULT 0.00,
                                completion_date DATETIME DEFAULT NULL,
                                certificate_issued BOOLEAN DEFAULT FALSE,
                                certificate_url VARCHAR(500) DEFAULT NULL,
                                status ENUM('active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
                                enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                
                                INDEX idx_course_id (course_id),
                                INDEX idx_student_id (student_id),
                                INDEX idx_status (status),
                                INDEX idx_payment_status (payment_status),
                                UNIQUE KEY unique_enrollment (course_id, student_id)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                        ");
                        logMessage("✅ تم إنشاء جدول course_enrollments", 'success');
                    } else {
                        logMessage("جدول course_enrollments موجود، فحص الأعمدة...", 'info');
                        
                        // فحص وإضافة الأعمدة المفقودة
                        $columns_to_add = [
                            'payment_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
                            'payment_status' => "ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending'",
                            'payment_method' => 'VARCHAR(50) DEFAULT NULL',
                            'payment_reference' => 'VARCHAR(255) DEFAULT NULL',
                            'enrollment_type' => "ENUM('free', 'paid', 'scholarship') DEFAULT 'free'",
                            'progress_percentage' => 'DECIMAL(5,2) DEFAULT 0.00',
                            'completion_date' => 'DATETIME DEFAULT NULL',
                            'certificate_issued' => 'BOOLEAN DEFAULT FALSE',
                            'certificate_url' => 'VARCHAR(500) DEFAULT NULL',
                            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
                        ];
                        
                        foreach ($columns_to_add as $column => $definition) {
                            try {
                                $stmt = $conn->query("SHOW COLUMNS FROM course_enrollments LIKE '$column'");
                                if ($stmt->rowCount() == 0) {
                                    $conn->exec("ALTER TABLE course_enrollments ADD COLUMN $column $definition");
                                    logMessage("✅ تم إضافة عمود $column", 'success');
                                }
                            } catch (PDOException $e) {
                                logMessage("⚠️ خطأ في إضافة عمود $column: " . $e->getMessage(), 'warning');
                            }
                        }
                        
                        // تحديث البيانات الموجودة
                        try {
                            // تحديث payment_amount من أسعار الكورسات
                            $conn->exec("
                                UPDATE course_enrollments ce 
                                JOIN courses c ON ce.course_id = c.id 
                                SET ce.payment_amount = CASE 
                                    WHEN c.course_type = 'paid' THEN c.price 
                                    ELSE 0 
                                END
                                WHERE ce.payment_amount IS NULL OR ce.payment_amount = 0
                            ");
                            logMessage("✅ تم تحديث مبالغ الدفع", 'success');
                            
                            // تحديث حالة الدفع
                            $conn->exec("
                                UPDATE course_enrollments ce 
                                JOIN courses c ON ce.course_id = c.id 
                                SET ce.payment_status = CASE 
                                    WHEN c.course_type = 'free' THEN 'completed'
                                    WHEN c.course_type = 'paid' THEN 'completed'
                                    ELSE 'pending'
                                END
                                WHERE ce.payment_status IS NULL
                            ");
                            logMessage("✅ تم تحديث حالات الدفع", 'success');
                            
                            // تحديث نوع التسجيل
                            $conn->exec("
                                UPDATE course_enrollments ce 
                                JOIN courses c ON ce.course_id = c.id 
                                SET ce.enrollment_type = c.course_type
                                WHERE ce.enrollment_type IS NULL
                            ");
                            logMessage("✅ تم تحديث أنواع التسجيل", 'success');
                            
                        } catch (PDOException $e) {
                            logMessage("⚠️ خطأ في تحديث البيانات: " . $e->getMessage(), 'warning');
                        }
                    }
                    
                    // 2. إصلاح جدول system_settings
                    echo "<h6>⚙️ إصلاح جدول system_settings</h6>";
                    
                    $stmt = $conn->query("SHOW TABLES LIKE 'system_settings'");
                    if ($stmt->rowCount() == 0) {
                        $conn->exec("
                            CREATE TABLE system_settings (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                setting_key VARCHAR(100) UNIQUE NOT NULL,
                                setting_value TEXT,
                                setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                                description TEXT,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                        ");
                        logMessage("✅ تم إنشاء جدول system_settings", 'success');
                        
                        // إضافة الإعدادات الافتراضية
                        $default_settings = [
                            ['platform_commission', '30', 'number', 'نسبة عمولة المنصة (%)'],
                            ['site_name', 'منصة التعلم', 'string', 'اسم الموقع'],
                            ['site_description', 'منصة تعليمية متقدمة', 'string', 'وصف الموقع'],
                            ['max_file_size', '50', 'number', 'الحد الأقصى لحجم الملف (MB)'],
                            ['allowed_file_types', 'pdf,doc,docx,ppt,pptx,mp4,mp3', 'string', 'أنواع الملفات المسموحة']
                        ];
                        
                        $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
                        foreach ($default_settings as $setting) {
                            $stmt->execute($setting);
                        }
                        logMessage("✅ تم إضافة الإعدادات الافتراضية", 'success');
                    } else {
                        logMessage("✅ جدول system_settings موجود", 'success');
                    }
                    
                    // 3. فحص وإصلاح الفهارس
                    echo "<h6>📊 فحص وإصلاح الفهارس</h6>";
                    
                    try {
                        // إضافة فهارس مفقودة لجدول course_enrollments
                        $indexes = [
                            'idx_course_id' => 'course_id',
                            'idx_student_id' => 'student_id',
                            'idx_status' => 'status',
                            'idx_payment_status' => 'payment_status'
                        ];
                        
                        foreach ($indexes as $index_name => $column) {
                            try {
                                $conn->exec("ALTER TABLE course_enrollments ADD INDEX $index_name ($column)");
                                logMessage("✅ تم إضافة فهرس $index_name", 'success');
                            } catch (PDOException $e) {
                                // الفهرس موجود بالفعل
                            }
                        }
                        
                        // إضافة فهرس فريد للتسجيل
                        try {
                            $conn->exec("ALTER TABLE course_enrollments ADD UNIQUE KEY unique_enrollment (course_id, student_id)");
                            logMessage("✅ تم إضافة فهرس التسجيل الفريد", 'success');
                        } catch (PDOException $e) {
                            // الفهرس موجود بالفعل
                        }
                        
                    } catch (PDOException $e) {
                        logMessage("⚠️ خطأ في إضافة الفهارس: " . $e->getMessage(), 'warning');
                    }
                    
                    // 4. إحصائيات نهائية
                    echo "<h6>📈 إحصائيات قاعدة البيانات</h6>";
                    
                    $stats = [];
                    
                    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'");
                    $stats['students'] = $stmt->fetchColumn();
                    
                    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'instructor'");
                    $stats['instructors'] = $stmt->fetchColumn();
                    
                    $stmt = $conn->query("SELECT COUNT(*) FROM courses");
                    $stats['courses'] = $stmt->fetchColumn();
                    
                    $stmt = $conn->query("SELECT COUNT(*) FROM course_enrollments");
                    $stats['enrollments'] = $stmt->fetchColumn();
                    
                    $stmt = $conn->query("SELECT COUNT(*) FROM course_enrollments WHERE payment_status = 'completed'");
                    $stats['paid_enrollments'] = $stmt->fetchColumn();
                    
                    $stmt = $conn->query("SELECT COALESCE(SUM(payment_amount), 0) FROM course_enrollments WHERE payment_status = 'completed'");
                    $stats['total_revenue'] = $stmt->fetchColumn();
                    
                    echo "<div class='row mt-3'>";
                    echo "<div class='col-md-2'><div class='alert alert-info text-center'><strong>{$stats['students']}</strong><br>طلاب</div></div>";
                    echo "<div class='col-md-2'><div class='alert alert-warning text-center'><strong>{$stats['instructors']}</strong><br>مدربين</div></div>";
                    echo "<div class='col-md-2'><div class='alert alert-primary text-center'><strong>{$stats['courses']}</strong><br>كورسات</div></div>";
                    echo "<div class='col-md-2'><div class='alert alert-success text-center'><strong>{$stats['enrollments']}</strong><br>تسجيلات</div></div>";
                    echo "<div class='col-md-2'><div class='alert alert-info text-center'><strong>{$stats['paid_enrollments']}</strong><br>مدفوعات</div></div>";
                    echo "<div class='col-md-2'><div class='alert alert-success text-center'><strong>" . number_format($stats['total_revenue'], 2) . "</strong><br>إجمالي الإيرادات</div></div>";
                    echo "</div>";
                    
                    logMessage("🎉 تم إصلاح هيكل قاعدة البيانات بنجاح!", 'success');
                    
                } catch (PDOException $e) {
                    logMessage("❌ خطأ في قاعدة البيانات: " . $e->getMessage(), 'error');
                }
                ?>
                
                <div class="mt-4">
                    <a href="financial-reports.php" class="btn btn-primary">
                        <i class="fas fa-chart-line me-2"></i>عرض التقارير المالية
                    </a>
                    <a href="manage-users.php" class="btn btn-secondary">
                        <i class="fas fa-users me-2"></i>إدارة المستخدمين
                    </a>
                    <a href="dashboard.php" class="btn btn-success">
                        <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
