<?php
/**
 * مدير قاعدة البيانات الشامل
 * Comprehensive Database Manager
 * يدعم جميع عمليات CRUD والعمليات المتقدمة
 * ===============================================
 */

require_once 'config/database.php';

class DatabaseManager {
    private $conn;
    private $lastInsertId;
    private $affectedRows;
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    /**
     * ===================================================================
     * عمليات CREATE (إنشاء)
     * ===================================================================
     */
    
    /**
     * إدراج سجل جديد
     */
    public function create($table, $data, $returnId = true) {
        try {
            $columns = array_keys($data);
            $placeholders = ':' . implode(', :', $columns);
            
            $sql = "INSERT INTO {$table} (" . implode(', ', $columns) . ") VALUES ({$placeholders})";
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute($data);
            
            if ($result) {
                $this->lastInsertId = $this->conn->lastInsertId();
                $this->affectedRows = $stmt->rowCount();
                
                // تسجيل النشاط
                $this->logActivity('create', $table, $this->lastInsertId, $data);
                
                return $returnId ? $this->lastInsertId : true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            $this->logError("Create Error in {$table}: " . $e->getMessage(), $data);
            return false;
        }
    }
    
    /**
     * إدراج متعدد
     */
    public function createMultiple($table, $dataArray) {
        try {
            $this->conn->beginTransaction();
            
            $insertedIds = [];
            foreach ($dataArray as $data) {
                $id = $this->create($table, $data);
                if ($id) {
                    $insertedIds[] = $id;
                } else {
                    throw new Exception("Failed to insert record");
                }
            }
            
            $this->conn->commit();
            return $insertedIds;
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            $this->logError("Bulk Create Error in {$table}: " . $e->getMessage(), $dataArray);
            return false;
        }
    }
    
    /**
     * ===================================================================
     * عمليات READ (قراءة)
     * ===================================================================
     */
    
    /**
     * الحصول على سجل واحد بالمعرف
     */
    public function find($table, $id, $columns = '*') {
        try {
            $sql = "SELECT {$columns} FROM {$table} WHERE id = ? LIMIT 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            $this->logError("Find Error in {$table}: " . $e->getMessage(), ['id' => $id]);
            return false;
        }
    }
    
    /**
     * الحصول على سجل واحد بشروط مخصصة
     */
    public function findWhere($table, $conditions, $columns = '*') {
        try {
            $whereClause = $this->buildWhereClause($conditions);
            $sql = "SELECT {$columns} FROM {$table} WHERE {$whereClause['sql']} LIMIT 1";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($whereClause['params']);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            $this->logError("FindWhere Error in {$table}: " . $e->getMessage(), $conditions);
            return false;
        }
    }
    
    /**
     * الحصول على جميع السجلات
     */
    public function getAll($table, $columns = '*', $orderBy = 'id DESC', $limit = null) {
        try {
            $sql = "SELECT {$columns} FROM {$table}";
            
            if ($orderBy) {
                $sql .= " ORDER BY {$orderBy}";
            }
            
            if ($limit) {
                $sql .= " LIMIT {$limit}";
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            $this->logError("GetAll Error in {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على سجلات بشروط
     */
    public function getWhere($table, $conditions, $columns = '*', $orderBy = null, $limit = null, $offset = null) {
        try {
            $whereClause = $this->buildWhereClause($conditions);
            $sql = "SELECT {$columns} FROM {$table} WHERE {$whereClause['sql']}";
            
            if ($orderBy) {
                $sql .= " ORDER BY {$orderBy}";
            }
            
            if ($limit) {
                $sql .= " LIMIT {$limit}";
                if ($offset) {
                    $sql .= " OFFSET {$offset}";
                }
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($whereClause['params']);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            $this->logError("GetWhere Error in {$table}: " . $e->getMessage(), $conditions);
            return [];
        }
    }
    
    /**
     * البحث في النصوص
     */
    public function search($table, $searchTerm, $searchColumns, $conditions = [], $columns = '*', $limit = 50) {
        try {
            $searchConditions = [];
            $params = [];
            
            // بناء شروط البحث
            foreach ($searchColumns as $column) {
                $searchConditions[] = "{$column} LIKE ?";
                $params[] = "%{$searchTerm}%";
            }
            
            $sql = "SELECT {$columns} FROM {$table} WHERE (" . implode(' OR ', $searchConditions) . ")";
            
            // إضافة شروط إضافية
            if (!empty($conditions)) {
                $whereClause = $this->buildWhereClause($conditions);
                $sql .= " AND {$whereClause['sql']}";
                $params = array_merge($params, $whereClause['params']);
            }
            
            $sql .= " LIMIT {$limit}";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            $this->logError("Search Error in {$table}: " . $e->getMessage(), ['term' => $searchTerm]);
            return [];
        }
    }
    
    /**
     * عد السجلات
     */
    public function count($table, $conditions = []) {
        try {
            $sql = "SELECT COUNT(*) as total FROM {$table}";
            $params = [];
            
            if (!empty($conditions)) {
                $whereClause = $this->buildWhereClause($conditions);
                $sql .= " WHERE {$whereClause['sql']}";
                $params = $whereClause['params'];
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['total'];
            
        } catch (PDOException $e) {
            $this->logError("Count Error in {$table}: " . $e->getMessage(), $conditions);
            return 0;
        }
    }
    
    /**
     * ===================================================================
     * عمليات UPDATE (تحديث)
     * ===================================================================
     */
    
    /**
     * تحديث سجل بالمعرف
     */
    public function update($table, $id, $data) {
        try {
            $oldData = $this->find($table, $id);
            
            $setParts = [];
            foreach (array_keys($data) as $column) {
                $setParts[] = "{$column} = :{$column}";
            }
            
            $sql = "UPDATE {$table} SET " . implode(', ', $setParts) . " WHERE id = :id";
            $data['id'] = $id;
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute($data);
            
            if ($result) {
                $this->affectedRows = $stmt->rowCount();
                
                // تسجيل النشاط
                $this->logActivity('update', $table, $id, $data, $oldData);
                
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            $this->logError("Update Error in {$table}: " . $e->getMessage(), $data);
            return false;
        }
    }
    
    /**
     * تحديث بشروط مخصصة
     */
    public function updateWhere($table, $conditions, $data) {
        try {
            $whereClause = $this->buildWhereClause($conditions);
            
            $setParts = [];
            foreach (array_keys($data) as $column) {
                $setParts[] = "{$column} = :{$column}";
            }
            
            $sql = "UPDATE {$table} SET " . implode(', ', $setParts) . " WHERE {$whereClause['sql']}";
            $params = array_merge($data, $whereClause['params']);
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute($params);
            
            if ($result) {
                $this->affectedRows = $stmt->rowCount();
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            $this->logError("UpdateWhere Error in {$table}: " . $e->getMessage(), $data);
            return false;
        }
    }
    
    /**
     * زيادة قيمة عمود رقمي
     */
    public function increment($table, $id, $column, $amount = 1) {
        try {
            $sql = "UPDATE {$table} SET {$column} = {$column} + ? WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$amount, $id]);
            
            if ($result) {
                $this->affectedRows = $stmt->rowCount();
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            $this->logError("Increment Error in {$table}: " . $e->getMessage(), ['id' => $id, 'column' => $column]);
            return false;
        }
    }
    
    /**
     * تقليل قيمة عمود رقمي
     */
    public function decrement($table, $id, $column, $amount = 1) {
        return $this->increment($table, $id, $column, -$amount);
    }
    
    /**
     * ===================================================================
     * عمليات DELETE (حذف)
     * ===================================================================
     */
    
    /**
     * حذف سجل بالمعرف
     */
    public function delete($table, $id) {
        try {
            $oldData = $this->find($table, $id);
            
            $sql = "DELETE FROM {$table} WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $this->affectedRows = $stmt->rowCount();
                
                // تسجيل النشاط
                $this->logActivity('delete', $table, $id, null, $oldData);
                
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            $this->logError("Delete Error in {$table}: " . $e->getMessage(), ['id' => $id]);
            return false;
        }
    }
    
    /**
     * حذف بشروط مخصصة
     */
    public function deleteWhere($table, $conditions) {
        try {
            $whereClause = $this->buildWhereClause($conditions);
            $sql = "DELETE FROM {$table} WHERE {$whereClause['sql']}";
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute($whereClause['params']);
            
            if ($result) {
                $this->affectedRows = $stmt->rowCount();
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            $this->logError("DeleteWhere Error in {$table}: " . $e->getMessage(), $conditions);
            return false;
        }
    }
    
    /**
     * حذف ناعم (تعيين deleted_at)
     */
    public function softDelete($table, $id) {
        return $this->update($table, $id, ['deleted_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * استعادة من الحذف الناعم
     */
    public function restore($table, $id) {
        return $this->update($table, $id, ['deleted_at' => null]);
    }
    
    /**
     * ===================================================================
     * دوال مساعدة
     * ===================================================================
     */
    
    /**
     * بناء جملة WHERE
     */
    private function buildWhereClause($conditions) {
        $whereParts = [];
        $params = [];
        
        foreach ($conditions as $key => $value) {
            if (is_array($value)) {
                // للقيم المتعددة (IN clause)
                $placeholders = str_repeat('?,', count($value) - 1) . '?';
                $whereParts[] = "{$key} IN ({$placeholders})";
                $params = array_merge($params, $value);
            } elseif (strpos($key, ' ') !== false) {
                // للعمليات المخصصة مثل 'age >' أو 'name LIKE'
                $whereParts[] = $key;
                $params[] = $value;
            } else {
                // للمقارنة العادية
                $whereParts[] = "{$key} = ?";
                $params[] = $value;
            }
        }
        
        return [
            'sql' => implode(' AND ', $whereParts),
            'params' => $params
        ];
    }
    
    /**
     * تسجيل النشاط
     */
    private function logActivity($action, $table, $entityId, $newData = null, $oldData = null) {
        try {
            $userId = $_SESSION['user_id'] ?? null;
            
            $logData = [
                'user_id' => $userId,
                'action' => $action,
                'entity_type' => $table,
                'entity_id' => $entityId,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'request_url' => $_SERVER['REQUEST_URI'] ?? null,
                'request_method' => $_SERVER['REQUEST_METHOD'] ?? null
            ];
            
            if ($oldData) {
                $logData['old_values'] = json_encode($oldData);
            }
            
            if ($newData) {
                $logData['new_values'] = json_encode($newData);
            }
            
            $this->create('activity_logs', $logData, false);
            
        } catch (Exception $e) {
            // تجاهل أخطاء تسجيل النشاط لتجنب التأثير على العملية الأساسية
            error_log("Activity log error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل الأخطاء
     */
    private function logError($message, $data = null) {
        $logMessage = date('Y-m-d H:i:s') . " - " . $message;
        if ($data) {
            $logMessage .= " - Data: " . json_encode($data);
        }
        
        error_log($logMessage);
    }
    
    /**
     * الحصول على آخر معرف مدرج
     */
    public function getLastInsertId() {
        return $this->lastInsertId;
    }
    
    /**
     * الحصول على عدد الصفوف المتأثرة
     */
    public function getAffectedRows() {
        return $this->affectedRows;
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->conn->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->conn->rollback();
    }
    
    /**
     * تنفيذ استعلام مخصص
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            $this->logError("Custom Query Error: " . $e->getMessage(), ['sql' => $sql, 'params' => $params]);
            return false;
        }
    }
    
    /**
     * التحقق من وجود سجل
     */
    public function exists($table, $conditions) {
        return $this->count($table, $conditions) > 0;
    }
}

// إنشاء مثيل عام
$db = new DatabaseManager();

// دوال مساعدة سريعة
function dbCreate($table, $data) {
    global $db;
    return $db->create($table, $data);
}

function dbFind($table, $id) {
    global $db;
    return $db->find($table, $id);
}

function dbUpdate($table, $id, $data) {
    global $db;
    return $db->update($table, $id, $data);
}

function dbDelete($table, $id) {
    global $db;
    return $db->delete($table, $id);
}

function dbGetAll($table, $orderBy = 'id DESC') {
    global $db;
    return $db->getAll($table, '*', $orderBy);
}

function dbGetWhere($table, $conditions) {
    global $db;
    return $db->getWhere($table, $conditions);
}

function dbCount($table, $conditions = []) {
    global $db;
    return $db->count($table, $conditions);
}

function dbSearch($table, $term, $columns) {
    global $db;
    return $db->search($table, $term, $columns);
}
?>
