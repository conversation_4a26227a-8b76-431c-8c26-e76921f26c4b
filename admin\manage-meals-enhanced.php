<?php
session_start();
require_once '../includes/database_manager_clean.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// معالجة العمليات
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_status':
            $mealId = (int)$_POST['meal_id'];
            $newStatus = $_POST['status'];
            
            if ($dbClean->update('meals', $mealId, ['status' => $newStatus])) {
                $message = 'تم تحديث حالة الوجبة بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في تحديث حالة الوجبة';
                $messageType = 'danger';
            }
            break;
            
        case 'delete_meal':
            $mealId = (int)$_POST['meal_id'];
            
            if ($dbClean->delete('meals', $mealId)) {
                $message = 'تم حذف الوجبة بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في حذف الوجبة';
                $messageType = 'danger';
            }
            break;
    }
}

// جلب البيانات من قاعدة البيانات
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// فلاتر البحث
$search = $_GET['search'] ?? '';
$categoryFilter = $_GET['category'] ?? '';
$statusFilter = $_GET['status'] ?? '';
$mealTypeFilter = $_GET['meal_type'] ?? '';

// بناء شروط البحث
$conditions = [];
if (!empty($search)) {
    $meals = $dbClean->search('meals', $search, ['name', 'description'], $conditions, '*', $limit);
    $totalMeals = count($meals);
} else {
    if (!empty($categoryFilter)) {
        $conditions['category_id'] = $categoryFilter;
    }
    if (!empty($statusFilter)) {
        $conditions['status'] = $statusFilter;
    }
    if (!empty($mealTypeFilter)) {
        $conditions['meal_type'] = $mealTypeFilter;
    }
    
    $meals = $dbClean->getWhere('meals', $conditions, '*', 'created_at DESC', $limit);
    $totalMeals = $dbClean->count('meals', $conditions);
}

// جلب بيانات إضافية لكل وجبة
$mealsWithDetails = [];
foreach ($meals as $meal) {
    // جلب بيانات الفئة
    $category = $dbClean->find('meal_categories', $meal['category_id']);
    $meal['category_name'] = $category['name'] ?? 'غير محدد';
    
    // جلب بيانات المنشئ
    $creator = $dbClean->find('users', $meal['created_by']);
    $meal['creator_name'] = $creator['name'] ?? 'غير محدد';
    
    $mealsWithDetails[] = $meal;
}

// إحصائيات الوجبات
$mealStats = [
    'total' => $dbClean->count('meals'),
    'published' => $dbClean->count('meals', ['status' => 'published']),
    'draft' => $dbClean->count('meals', ['status' => 'draft']),
    'archived' => $dbClean->count('meals', ['status' => 'archived']),
    'breakfast' => $dbClean->count('meals', ['meal_type' => 'breakfast']),
    'lunch' => $dbClean->count('meals', ['meal_type' => 'lunch']),
    'dinner' => $dbClean->count('meals', ['meal_type' => 'dinner']),
    'snack' => $dbClean->count('meals', ['meal_type' => 'snack'])
];

// جلب فئات الوجبات للفلترة
$mealCategories = $dbClean->getAll('meal_categories', 'id, name', 'name ASC');

$totalPages = ceil($totalMeals / $limit);
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الوجبات - لوحة التحكم</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card.primary { border-left-color: #667eea; }
        .stats-card.success { border-left-color: #28a745; }
        .stats-card.warning { border-left-color: #ffc107; }
        .stats-card.danger { border-left-color: #dc3545; }
        .stats-card.info { border-left-color: #17a2b8; }
        .stats-card.secondary { border-left-color: #6c757d; }
        .stats-card.dark { border-left-color: #343a40; }
        .stats-card.light { border-left-color: #f8f9fa; }
        
        .data-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .meal-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .meal-card:hover {
            transform: translateY(-5px);
        }
        
        .meal-image {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 10px;
        }
        
        .nutrition-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            margin: 0.125rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .btn-action {
            padding: 0.25rem 0.5rem;
            margin: 0 0.125rem;
            border-radius: 5px;
            font-size: 0.875rem;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 0.75rem 1rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .pagination {
            justify-content: center;
            margin-top: 2rem;
        }
        
        .page-link {
            border-radius: 10px;
            margin: 0 0.25rem;
            border: 2px solid #e2e8f0;
            color: #667eea;
        }
        
        .page-link:hover, .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-2">
                        <i class="fas fa-utensils me-3"></i>
                        إدارة الوجبات الصحية
                    </h1>
                    <p class="lead mb-0">إدارة شاملة لجميع الوجبات مع البيانات الغذائية والإحصائيات المفصلة</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="dashboard.php" class="btn btn-light btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- رسائل النظام -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الوجبات -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card primary">
                    <div class="text-center">
                        <h3 class="text-primary mb-1"><?php echo number_format($mealStats['total']); ?></h3>
                        <p class="text-muted mb-0">إجمالي الوجبات</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card success">
                    <div class="text-center">
                        <h3 class="text-success mb-1"><?php echo number_format($mealStats['published']); ?></h3>
                        <p class="text-muted mb-0">منشورة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card warning">
                    <div class="text-center">
                        <h3 class="text-warning mb-1"><?php echo number_format($mealStats['draft']); ?></h3>
                        <p class="text-muted mb-0">مسودة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card secondary">
                    <div class="text-center">
                        <h3 class="text-secondary mb-1"><?php echo number_format($mealStats['archived']); ?></h3>
                        <p class="text-muted mb-0">مؤرشفة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات حسب نوع الوجبة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card info">
                    <div class="text-center">
                        <h3 class="text-info mb-1"><?php echo number_format($mealStats['breakfast']); ?></h3>
                        <p class="text-muted mb-0">إفطار</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card danger">
                    <div class="text-center">
                        <h3 class="text-danger mb-1"><?php echo number_format($mealStats['lunch']); ?></h3>
                        <p class="text-muted mb-0">غداء</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card dark">
                    <div class="text-center">
                        <h3 class="text-dark mb-1"><?php echo number_format($mealStats['dinner']); ?></h3>
                        <p class="text-muted mb-0">عشاء</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card light">
                    <div class="text-center">
                        <h3 class="text-primary mb-1"><?php echo number_format($mealStats['snack']); ?></h3>
                        <p class="text-muted mb-0">وجبة خفيفة</p>
                    </div>
                </div>
            </div>
        </div>
