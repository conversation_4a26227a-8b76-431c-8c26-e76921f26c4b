<?php
/**
 * نظام الواجبات والاختبارات المحسن
 * Enhanced Assignments and Quizzes System
 * =======================================
 */

require_once 'config/config.php';
require_once 'includes/file_upload_system.php';

class AssignmentsSystem {
    private $conn;
    private $uploadSystem;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->uploadSystem = new FileUploadSystem($database_connection);
    }
    
    /**
     * إنشاء واجب جديد
     */
    public function createAssignment($data) {
        try {
            $this->conn->beginTransaction();
            
            // التحقق من صحة البيانات
            $this->validateAssignmentData($data);
            
            // إدراج الواجب
            $stmt = $this->conn->prepare("
                INSERT INTO assignments (
                    course_id, lesson_id, title, description, instructions,
                    submission_format, allowed_file_types, max_file_size,
                    total_points, weight_percentage, due_date,
                    late_submission_allowed, late_penalty_percentage, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['course_id'],
                $data['lesson_id'] ?? null,
                $data['title'],
                $data['description'],
                $data['instructions'] ?? null,
                $data['submission_format'] ?? 'both',
                json_encode($data['allowed_file_types'] ?? ['pdf', 'doc', 'docx']),
                $data['max_file_size'] ?? 10485760, // 10MB
                $data['total_points'] ?? 100,
                $data['weight_percentage'] ?? 0,
                $data['due_date'] ?? null,
                $data['late_submission_allowed'] ?? true,
                $data['late_penalty_percentage'] ?? 10,
                $data['status'] ?? 'draft'
            ]);
            
            $assignmentId = $this->conn->lastInsertId();
            
            // رفع الملفات المرفقة إن وجدت
            if (!empty($data['attachment_files'])) {
                $attachments = $this->handleAttachmentFiles($data['attachment_files'], $assignmentId, $data['instructor_id']);
                
                // تحديث الواجب بالملفات المرفقة
                $stmt = $this->conn->prepare("UPDATE assignments SET attachment_files = ? WHERE id = ?");
                $stmt->execute([json_encode($attachments), $assignmentId]);
            }
            
            $this->conn->commit();
            
            return [
                'success' => true,
                'assignment_id' => $assignmentId,
                'message' => 'تم إنشاء الواجب بنجاح'
            ];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تسليم واجب من طالب
     */
    public function submitAssignment($assignmentId, $studentId, $submissionData) {
        try {
            $this->conn->beginTransaction();
            
            // التحقق من الواجب
            $assignment = $this->getAssignment($assignmentId);
            if (!$assignment) {
                throw new Exception('الواجب غير موجود');
            }
            
            // التحقق من التسجيل في الكورس
            if (!$this->isStudentEnrolled($studentId, $assignment['course_id'])) {
                throw new Exception('غير مسجل في هذا الكورس');
            }
            
            // التحقق من التسليم المسبق
            $existingSubmission = $this->getStudentSubmission($assignmentId, $studentId);
            if ($existingSubmission && $existingSubmission['status'] === 'graded') {
                throw new Exception('تم تقييم هذا الواجب مسبقاً');
            }
            
            // التحقق من تاريخ التسليم
            $isLate = $this->isSubmissionLate($assignment['due_date']);
            if ($isLate && !$assignment['late_submission_allowed']) {
                throw new Exception('انتهى موعد تسليم الواجب');
            }
            
            // معالجة الملفات المرفقة
            $submittedFiles = [];
            if (!empty($submissionData['files'])) {
                $submittedFiles = $this->handleSubmissionFiles($submissionData['files'], $assignmentId, $studentId);
            }
            
            // حفظ أو تحديث التسليم
            if ($existingSubmission) {
                $stmt = $this->conn->prepare("
                    UPDATE assignment_submissions 
                    SET submission_text = ?, submitted_files = ?, is_late = ?, 
                        status = 'submitted', submitted_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $submissionData['text'] ?? null,
                    json_encode($submittedFiles),
                    $isLate,
                    $existingSubmission['id']
                ]);
                $submissionId = $existingSubmission['id'];
            } else {
                $stmt = $this->conn->prepare("
                    INSERT INTO assignment_submissions (
                        assignment_id, student_id, submission_text, submitted_files,
                        is_late, status
                    ) VALUES (?, ?, ?, ?, ?, 'submitted')
                ");
                $stmt->execute([
                    $assignmentId,
                    $studentId,
                    $submissionData['text'] ?? null,
                    json_encode($submittedFiles),
                    $isLate
                ]);
                $submissionId = $this->conn->lastInsertId();
            }
            
            $this->conn->commit();
            
            return [
                'success' => true,
                'submission_id' => $submissionId,
                'is_late' => $isLate,
                'message' => 'تم تسليم الواجب بنجاح'
            ];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تقييم واجب
     */
    public function gradeAssignment($submissionId, $instructorId, $gradeData) {
        try {
            // التحقق من التسليم
            $submission = $this->getSubmission($submissionId);
            if (!$submission) {
                throw new Exception('التسليم غير موجود');
            }
            
            // التحقق من صلاحية المدرب
            $assignment = $this->getAssignment($submission['assignment_id']);
            if (!$this->isInstructorOfCourse($instructorId, $assignment['course_id'])) {
                throw new Exception('ليس لديك صلاحية لتقييم هذا الواجب');
            }
            
            // حساب الدرجة مع خصم التأخير
            $finalGrade = $gradeData['grade'];
            if ($submission['is_late'] && $assignment['late_penalty_percentage'] > 0) {
                $penalty = ($finalGrade * $assignment['late_penalty_percentage']) / 100;
                $finalGrade = max(0, $finalGrade - $penalty);
            }
            
            // تحديث التقييم
            $stmt = $this->conn->prepare("
                UPDATE assignment_submissions 
                SET grade = ?, feedback = ?, graded_by = ?, graded_at = NOW(), status = 'graded'
                WHERE id = ?
            ");
            $stmt->execute([
                $finalGrade,
                $gradeData['feedback'] ?? null,
                $instructorId,
                $submissionId
            ]);
            
            // تحديث تقدم الطالب
            $this->updateStudentProgress($submission['student_id'], $assignment['course_id']);
            
            return [
                'success' => true,
                'final_grade' => $finalGrade,
                'message' => 'تم تقييم الواجب بنجاح'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * الحصول على واجبات الكورس
     */
    public function getCourseAssignments($courseId, $studentId = null) {
        try {
            $sql = "
                SELECT a.*, 
                       (SELECT COUNT(*) FROM assignment_submissions WHERE assignment_id = a.id) as total_submissions,
                       (SELECT COUNT(*) FROM assignment_submissions WHERE assignment_id = a.id AND status = 'graded') as graded_submissions
            ";
            
            if ($studentId) {
                $sql .= ", (SELECT status FROM assignment_submissions WHERE assignment_id = a.id AND student_id = ?) as student_status,
                          (SELECT grade FROM assignment_submissions WHERE assignment_id = a.id AND student_id = ?) as student_grade";
            }
            
            $sql .= " FROM assignments a WHERE a.course_id = ? AND a.status = 'published' ORDER BY a.due_date ASC";
            
            $stmt = $this->conn->prepare($sql);
            
            if ($studentId) {
                $stmt->execute([$studentId, $studentId, $courseId]);
            } else {
                $stmt->execute([$courseId]);
            }
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * الحصول على تسليمات الواجب
     */
    public function getAssignmentSubmissions($assignmentId, $instructorId = null) {
        try {
            // التحقق من الصلاحية
            if ($instructorId) {
                $assignment = $this->getAssignment($assignmentId);
                if (!$this->isInstructorOfCourse($instructorId, $assignment['course_id'])) {
                    throw new Exception('ليس لديك صلاحية لعرض هذه التسليمات');
                }
            }
            
            $stmt = $this->conn->prepare("
                SELECT s.*, u.name as student_name, u.email as student_email
                FROM assignment_submissions s
                JOIN users u ON s.student_id = u.id
                WHERE s.assignment_id = ?
                ORDER BY s.submitted_at DESC
            ");
            $stmt->execute([$assignmentId]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * دوال مساعدة
     */
    private function validateAssignmentData($data) {
        if (empty($data['title'])) {
            throw new Exception('عنوان الواجب مطلوب');
        }
        
        if (empty($data['description'])) {
            throw new Exception('وصف الواجب مطلوب');
        }
        
        if (empty($data['course_id'])) {
            throw new Exception('معرف الكورس مطلوب');
        }
        
        if (!empty($data['due_date']) && strtotime($data['due_date']) < time()) {
            throw new Exception('تاريخ التسليم يجب أن يكون في المستقبل');
        }
    }
    
    private function getAssignment($assignmentId) {
        $stmt = $this->conn->prepare("SELECT * FROM assignments WHERE id = ?");
        $stmt->execute([$assignmentId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getSubmission($submissionId) {
        $stmt = $this->conn->prepare("SELECT * FROM assignment_submissions WHERE id = ?");
        $stmt->execute([$submissionId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getStudentSubmission($assignmentId, $studentId) {
        $stmt = $this->conn->prepare("
            SELECT * FROM assignment_submissions 
            WHERE assignment_id = ? AND student_id = ?
        ");
        $stmt->execute([$assignmentId, $studentId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function isStudentEnrolled($studentId, $courseId) {
        $stmt = $this->conn->prepare("
            SELECT COUNT(*) FROM course_enrollments 
            WHERE student_id = ? AND course_id = ? AND status = 'active'
        ");
        $stmt->execute([$studentId, $courseId]);
        return $stmt->fetchColumn() > 0;
    }
    
    private function isInstructorOfCourse($instructorId, $courseId) {
        $stmt = $this->conn->prepare("
            SELECT COUNT(*) FROM courses 
            WHERE id = ? AND instructor_id = ?
        ");
        $stmt->execute([$courseId, $instructorId]);
        return $stmt->fetchColumn() > 0;
    }
    
    private function isSubmissionLate($dueDate) {
        if (!$dueDate) return false;
        return strtotime($dueDate) < time();
    }
    
    private function handleAttachmentFiles($files, $assignmentId, $instructorId) {
        $attachments = [];
        
        foreach ($files as $file) {
            $result = $this->uploadSystem->uploadFile($file, 'assignment', $assignmentId, $instructorId);
            if ($result['success']) {
                $attachments[] = [
                    'file_id' => $result['file_id'],
                    'file_name' => $result['file_name'],
                    'file_url' => $result['file_url'],
                    'original_name' => $file['name']
                ];
            }
        }
        
        return $attachments;
    }
    
    private function handleSubmissionFiles($files, $assignmentId, $studentId) {
        $submittedFiles = [];
        
        foreach ($files as $file) {
            $result = $this->uploadSystem->uploadFile($file, 'assignment', $assignmentId, $studentId);
            if ($result['success']) {
                $submittedFiles[] = [
                    'file_id' => $result['file_id'],
                    'file_name' => $result['file_name'],
                    'file_url' => $result['file_url'],
                    'original_name' => $file['name']
                ];
            }
        }
        
        return $submittedFiles;
    }
    
    private function updateStudentProgress($studentId, $courseId) {
        try {
            // حساب عدد الواجبات المكتملة
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) FROM assignment_submissions s
                JOIN assignments a ON s.assignment_id = a.id
                WHERE s.student_id = ? AND a.course_id = ? AND s.status = 'graded'
            ");
            $stmt->execute([$studentId, $courseId]);
            $completedAssignments = $stmt->fetchColumn();
            
            // تحديث تقدم الطالب
            $stmt = $this->conn->prepare("
                UPDATE course_enrollments 
                SET assignments_completed = ?
                WHERE student_id = ? AND course_id = ?
            ");
            $stmt->execute([$completedAssignments, $studentId, $courseId]);
            
        } catch (PDOException $e) {
            error_log("Error updating student progress: " . $e->getMessage());
        }
    }
}

/**
 * نظام الاختبارات
 */
class QuizSystem {
    private $conn;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
    }
    
    /**
     * إنشاء اختبار جديد
     */
    public function createQuiz($data) {
        try {
            $this->conn->beginTransaction();
            
            // التحقق من صحة البيانات
            $this->validateQuizData($data);
            
            // إدراج الاختبار
            $stmt = $this->conn->prepare("
                INSERT INTO quizzes (
                    course_id, lesson_id, title, description, instructions,
                    quiz_type, time_limit, attempts_allowed, passing_score,
                    randomize_questions, show_results, show_correct_answers,
                    weight_percentage, available_from, available_until, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['course_id'],
                $data['lesson_id'] ?? null,
                $data['title'],
                $data['description'] ?? null,
                $data['instructions'] ?? null,
                $data['quiz_type'] ?? 'practice',
                $data['time_limit'] ?? null,
                $data['attempts_allowed'] ?? 1,
                $data['passing_score'] ?? 70,
                $data['randomize_questions'] ?? false,
                $data['show_results'] ?? true,
                $data['show_correct_answers'] ?? true,
                $data['weight_percentage'] ?? 0,
                $data['available_from'] ?? null,
                $data['available_until'] ?? null,
                $data['status'] ?? 'draft'
            ]);
            
            $quizId = $this->conn->lastInsertId();
            
            // إضافة الأسئلة
            if (!empty($data['questions'])) {
                $this->addQuizQuestions($quizId, $data['questions']);
            }
            
            $this->conn->commit();
            
            return [
                'success' => true,
                'quiz_id' => $quizId,
                'message' => 'تم إنشاء الاختبار بنجاح'
            ];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إضافة أسئلة للاختبار
     */
    public function addQuizQuestions($quizId, $questions) {
        try {
            $totalPoints = 0;
            
            foreach ($questions as $index => $question) {
                $stmt = $this->conn->prepare("
                    INSERT INTO quiz_questions (
                        quiz_id, question_text, question_type, options,
                        correct_answer, explanation, points, sort_order
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $quizId,
                    $question['text'],
                    $question['type'],
                    json_encode($question['options'] ?? null),
                    $question['correct_answer'] ?? null,
                    $question['explanation'] ?? null,
                    $question['points'] ?? 1,
                    $index + 1
                ]);
                
                $totalPoints += $question['points'] ?? 1;
            }
            
            // تحديث إجمالي النقاط
            $stmt = $this->conn->prepare("UPDATE quizzes SET total_points = ? WHERE id = ?");
            $stmt->execute([$totalPoints, $quizId]);
            
            return true;
            
        } catch (PDOException $e) {
            throw new Exception('فشل في إضافة الأسئلة: ' . $e->getMessage());
        }
    }
    
    /**
     * بدء محاولة اختبار
     */
    public function startQuizAttempt($quizId, $studentId) {
        try {
            // التحقق من الاختبار
            $quiz = $this->getQuiz($quizId);
            if (!$quiz) {
                throw new Exception('الاختبار غير موجود');
            }
            
            // التحقق من التسجيل في الكورس
            if (!$this->isStudentEnrolled($studentId, $quiz['course_id'])) {
                throw new Exception('غير مسجل في هذا الكورس');
            }
            
            // التحقق من عدد المحاولات
            $attemptCount = $this->getStudentAttemptCount($quizId, $studentId);
            if ($attemptCount >= $quiz['attempts_allowed']) {
                throw new Exception('تم استنفاد عدد المحاولات المسموحة');
            }
            
            // التحقق من توقيت الاختبار
            if (!$this->isQuizAvailable($quiz)) {
                throw new Exception('الاختبار غير متاح حالياً');
            }
            
            // إنشاء محاولة جديدة
            $stmt = $this->conn->prepare("
                INSERT INTO quiz_attempts (quiz_id, student_id, attempt_number, status)
                VALUES (?, ?, ?, 'in_progress')
            ");
            $stmt->execute([$quizId, $studentId, $attemptCount + 1]);
            
            $attemptId = $this->conn->lastInsertId();
            
            return [
                'success' => true,
                'attempt_id' => $attemptId,
                'quiz' => $quiz,
                'questions' => $this->getQuizQuestions($quizId, $quiz['randomize_questions']),
                'time_limit' => $quiz['time_limit']
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تسليم إجابات الاختبار
     */
    public function submitQuizAnswers($attemptId, $answers) {
        try {
            // الحصول على المحاولة
            $attempt = $this->getQuizAttempt($attemptId);
            if (!$attempt || $attempt['status'] !== 'in_progress') {
                throw new Exception('المحاولة غير صالحة');
            }
            
            // الحصول على الاختبار والأسئلة
            $quiz = $this->getQuiz($attempt['quiz_id']);
            $questions = $this->getQuizQuestions($attempt['quiz_id']);
            
            // تقييم الإجابات
            $score = $this->gradeQuizAnswers($questions, $answers);
            $percentage = ($score / $quiz['total_points']) * 100;
            $isPassed = $percentage >= $quiz['passing_score'];
            
            // حساب الوقت المستغرق
            $timeTaken = time() - strtotime($attempt['started_at']);
            
            // تحديث المحاولة
            $stmt = $this->conn->prepare("
                UPDATE quiz_attempts 
                SET answers = ?, score = ?, total_points = ?, percentage = ?, 
                    is_passed = ?, submitted_at = NOW(), time_taken = ?, status = 'submitted'
                WHERE id = ?
            ");
            $stmt->execute([
                json_encode($answers),
                $score,
                $quiz['total_points'],
                $percentage,
                $isPassed,
                $timeTaken,
                $attemptId
            ]);
            
            // تحديث تقدم الطالب
            if ($isPassed) {
                $this->updateStudentQuizProgress($attempt['student_id'], $quiz['course_id']);
            }
            
            return [
                'success' => true,
                'score' => $score,
                'total_points' => $quiz['total_points'],
                'percentage' => $percentage,
                'is_passed' => $isPassed,
                'time_taken' => $timeTaken,
                'show_results' => $quiz['show_results'],
                'show_correct_answers' => $quiz['show_correct_answers']
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * دوال مساعدة
     */
    private function validateQuizData($data) {
        if (empty($data['title'])) {
            throw new Exception('عنوان الاختبار مطلوب');
        }
        
        if (empty($data['course_id'])) {
            throw new Exception('معرف الكورس مطلوب');
        }
    }
    
    private function getQuiz($quizId) {
        $stmt = $this->conn->prepare("SELECT * FROM quizzes WHERE id = ?");
        $stmt->execute([$quizId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getQuizQuestions($quizId, $randomize = false) {
        $orderBy = $randomize ? 'RAND()' : 'sort_order ASC';
        $stmt = $this->conn->prepare("
            SELECT * FROM quiz_questions 
            WHERE quiz_id = ? AND is_active = 1 
            ORDER BY $orderBy
        ");
        $stmt->execute([$quizId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getQuizAttempt($attemptId) {
        $stmt = $this->conn->prepare("SELECT * FROM quiz_attempts WHERE id = ?");
        $stmt->execute([$attemptId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getStudentAttemptCount($quizId, $studentId) {
        $stmt = $this->conn->prepare("
            SELECT COUNT(*) FROM quiz_attempts 
            WHERE quiz_id = ? AND student_id = ?
        ");
        $stmt->execute([$quizId, $studentId]);
        return $stmt->fetchColumn();
    }
    
    private function isStudentEnrolled($studentId, $courseId) {
        $stmt = $this->conn->prepare("
            SELECT COUNT(*) FROM course_enrollments 
            WHERE student_id = ? AND course_id = ? AND status = 'active'
        ");
        $stmt->execute([$studentId, $courseId]);
        return $stmt->fetchColumn() > 0;
    }
    
    private function isQuizAvailable($quiz) {
        $now = time();
        
        if ($quiz['available_from'] && strtotime($quiz['available_from']) > $now) {
            return false;
        }
        
        if ($quiz['available_until'] && strtotime($quiz['available_until']) < $now) {
            return false;
        }
        
        return true;
    }
    
    private function gradeQuizAnswers($questions, $answers) {
        $totalScore = 0;
        
        foreach ($questions as $question) {
            $questionId = $question['id'];
            $studentAnswer = $answers[$questionId] ?? null;
            
            if ($this->isAnswerCorrect($question, $studentAnswer)) {
                $totalScore += $question['points'];
            }
        }
        
        return $totalScore;
    }
    
    private function isAnswerCorrect($question, $studentAnswer) {
        if (!$studentAnswer) return false;
        
        switch ($question['question_type']) {
            case 'multiple_choice':
            case 'true_false':
                return $studentAnswer === $question['correct_answer'];
                
            case 'short_answer':
                return strtolower(trim($studentAnswer)) === strtolower(trim($question['correct_answer']));
                
            case 'essay':
                // يحتاج تقييم يدوي
                return false;
                
            default:
                return false;
        }
    }
    
    private function updateStudentQuizProgress($studentId, $courseId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) FROM quiz_attempts qa
                JOIN quizzes q ON qa.quiz_id = q.id
                WHERE qa.student_id = ? AND q.course_id = ? AND qa.is_passed = 1
            ");
            $stmt->execute([$studentId, $courseId]);
            $completedQuizzes = $stmt->fetchColumn();
            
            $stmt = $this->conn->prepare("
                UPDATE course_enrollments 
                SET quizzes_completed = ?
                WHERE student_id = ? AND course_id = ?
            ");
            $stmt->execute([$completedQuizzes, $studentId, $courseId]);
            
        } catch (PDOException $e) {
            error_log("Error updating quiz progress: " . $e->getMessage());
        }
    }
}

// دوال مساعدة سريعة
function createAssignment($data) {
    global $conn;
    $system = new AssignmentsSystem($conn);
    return $system->createAssignment($data);
}

function submitAssignment($assignmentId, $studentId, $submissionData) {
    global $conn;
    $system = new AssignmentsSystem($conn);
    return $system->submitAssignment($assignmentId, $studentId, $submissionData);
}

function gradeAssignment($submissionId, $instructorId, $gradeData) {
    global $conn;
    $system = new AssignmentsSystem($conn);
    return $system->gradeAssignment($submissionId, $instructorId, $gradeData);
}

function createQuiz($data) {
    global $conn;
    $system = new QuizSystem($conn);
    return $system->createQuiz($data);
}

function startQuizAttempt($quizId, $studentId) {
    global $conn;
    $system = new QuizSystem($conn);
    return $system->startQuizAttempt($quizId, $studentId);
}

function submitQuizAnswers($attemptId, $answers) {
    global $conn;
    $system = new QuizSystem($conn);
    return $system->submitQuizAnswers($attemptId, $answers);
}
?>
