<?php
// إعدادات النظام الشاملة والمحسنة
// ===================================

// إعدادات النظام الأساسية
define('SITE_URL', 'http://localhost/zoom');
define('SITE_NAME', 'منصة التعلم الإلكتروني');
define('SITE_DESCRIPTION', 'منصة تعليمية متقدمة للتعلم الإلكتروني');
define('SITE_KEYWORDS', 'تعلم, كورسات, تدريب, تعليم إلكتروني');
define('SITE_AUTHOR', 'فريق التطوير');
define('SITE_VERSION', '2.0.0');
define('SITE_LOGO', '/assets/images/logo.png');
define('SITE_FAVICON', '/assets/images/favicon.ico');

// إعدادات الأمان المحسنة
define('ENCRYPTION_KEY', hash('sha256', 'zoom-learning-encryption-key-2024'));
define('JWT_SECRET', hash('sha256', 'zoom-learning-jwt-secret-2024'));
define('CSRF_TOKEN_EXPIRE', 3600); // ساعة واحدة
define('SESSION_TIMEOUT', 7200); // ساعتان
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة
define('PASSWORD_MIN_LENGTH', 8);
define('REQUIRE_STRONG_PASSWORD', true);

// إعدادات Zoom API المحسنة
define('ZOOM_API_KEY', 'YOUR_ZOOM_API_KEY');
define('ZOOM_API_SECRET', 'YOUR_ZOOM_API_SECRET');
define('ZOOM_BASE_URL', 'https://api.zoom.us/v2');
define('ZOOM_JWT_EXPIRE', 3600);
define('ZOOM_WEBHOOK_SECRET', 'your_zoom_webhook_secret');

// إعدادات البريد الإلكتروني المحسنة
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_SECURE', 'tls');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'منصة التعلم الإلكتروني');
define('REPLY_TO_EMAIL', '<EMAIL>');
define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات الدفع المحسنة
define('STRIPE_PUBLIC_KEY', 'pk_test_your_stripe_public_key');
define('STRIPE_SECRET_KEY', 'sk_test_your_stripe_secret_key');
define('STRIPE_WEBHOOK_SECRET', 'whsec_your_webhook_secret');
define('PAYPAL_CLIENT_ID', 'your_paypal_client_id');
define('PAYPAL_CLIENT_SECRET', 'your_paypal_client_secret');
define('PAYPAL_MODE', 'sandbox'); // sandbox أو live
define('DEFAULT_CURRENCY', 'USD');
define('SUPPORTED_CURRENCIES', ['USD', 'EUR', 'SAR', 'AED']);
define('PLATFORM_COMMISSION_RATE', 30); // نسبة العمولة الافتراضية

// إعدادات التخزين المحسنة
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('TEMP_PATH', __DIR__ . '/../temp/');
define('CACHE_PATH', __DIR__ . '/../cache/');
define('LOGS_PATH', __DIR__ . '/../logs/');
define('BACKUP_PATH', __DIR__ . '/../backups/');

// أحجام الملفات المسموحة
define('MAX_FILE_SIZE', 500 * 1024 * 1024); // 500MB
define('MAX_VIDEO_SIZE', 2 * 1024 * 1024 * 1024); // 2GB للفيديوهات
define('MAX_IMAGE_SIZE', 10 * 1024 * 1024); // 10MB للصور
define('MAX_DOCUMENT_SIZE', 50 * 1024 * 1024); // 50MB للمستندات
define('MAX_AUDIO_SIZE', 100 * 1024 * 1024); // 100MB للصوتيات

// أنواع الملفات المسموحة
define('ALLOWED_VIDEO_TYPES', ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v']);
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'txt', 'rtf', 'xls', 'xlsx']);
define('ALLOWED_AUDIO_TYPES', ['mp3', 'wav', 'ogg', 'm4a', 'aac']);

// إعدادات الأداء والتخزين المؤقت
define('ENABLE_CACHE', true);
define('CACHE_EXPIRE', 3600); // ساعة واحدة
define('ENABLE_GZIP', true);
define('ENABLE_MINIFY', true);
define('ENABLE_CDN', false);
define('CDN_URL', '');

// إعدادات الأمان المتقدمة
define('ENABLE_2FA', false); // سيتم تفعيلها لاحقاً
define('ENABLE_IP_WHITELIST', false);
define('ENABLE_RATE_LIMITING', true);
define('RATE_LIMIT_REQUESTS', 100);
define('RATE_LIMIT_WINDOW', 3600); // ساعة واحدة
define('ENABLE_BRUTE_FORCE_PROTECTION', true);
define('ENABLE_SQL_INJECTION_PROTECTION', true);
define('ENABLE_XSS_PROTECTION', true);

// إعدادات PWA
define('PWA_NAME', 'منصة التعلم');
define('PWA_SHORT_NAME', 'التعلم');
define('PWA_THEME_COLOR', '#667eea');
define('PWA_BACKGROUND_COLOR', '#ffffff');
define('PWA_DISPLAY', 'standalone');
define('PWA_ORIENTATION', 'portrait');

// إعدادات الإشعارات
define('ENABLE_PUSH_NOTIFICATIONS', true);
define('VAPID_PUBLIC_KEY', 'your_vapid_public_key');
define('VAPID_PRIVATE_KEY', 'your_vapid_private_key');
define('FCM_SERVER_KEY', 'your_fcm_server_key');

// إعدادات WhatsApp API
define('WHATSAPP_API_URL', 'https://api.whatsapp.com/send');
define('WHATSAPP_BUSINESS_NUMBER', '+966500000000');
define('WHATSAPP_API_TOKEN', 'your_whatsapp_api_token');

// إعدادات SMS
define('SMS_PROVIDER', 'twilio'); // twilio, nexmo, etc.
define('SMS_API_KEY', 'your_sms_api_key');
define('SMS_API_SECRET', 'your_sms_api_secret');
define('SMS_FROM_NUMBER', '+966500000000');

// إعدادات التحليلات
define('GOOGLE_ANALYTICS_ID', 'GA_MEASUREMENT_ID');
define('FACEBOOK_PIXEL_ID', 'your_facebook_pixel_id');
define('ENABLE_ANALYTICS', true);

// إعدادات SEO
define('DEFAULT_META_TITLE', 'منصة التعلم الإلكتروني');
define('DEFAULT_META_DESCRIPTION', 'منصة تعليمية متقدمة للتعلم الإلكتروني');
define('DEFAULT_META_KEYWORDS', 'تعلم, كورسات, تدريب, تعليم إلكتروني');
define('ENABLE_SITEMAP', true);
define('ENABLE_ROBOTS_TXT', true);

// إعدادات اللغة والتوطين
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// إعدادات الواجهة
define('DEFAULT_THEME', 'default');
define('ENABLE_DARK_MODE', true);
define('ENABLE_RTL', true);
define('ITEMS_PER_PAGE', 20);
define('MAX_SEARCH_RESULTS', 100);

// إعدادات الفيديو
define('VIDEO_QUALITY_OPTIONS', ['360p', '480p', '720p', '1080p']);
define('DEFAULT_VIDEO_QUALITY', '720p');
define('ENABLE_VIDEO_TRANSCODING', false);
define('VIDEO_THUMBNAIL_SIZE', '320x180');

// إعدادات الصوت
define('AUDIO_QUALITY_OPTIONS', ['64kbps', '128kbps', '192kbps', '320kbps']);
define('DEFAULT_AUDIO_QUALITY', '128kbps');

// إعدادات النسخ الاحتياطي
define('ENABLE_AUTO_BACKUP', true);
define('BACKUP_FREQUENCY', 'daily'); // daily, weekly, monthly
define('BACKUP_RETENTION_DAYS', 30);
define('BACKUP_INCLUDE_UPLOADS', true);

// إعدادات التطوير
define('DEVELOPMENT_MODE', false);
define('DEBUG_MODE', false);
define('ENABLE_ERROR_REPORTING', false);
define('LOG_LEVEL', 'ERROR'); // DEBUG, INFO, WARNING, ERROR

// إعدادات API
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 1000);
define('API_RATE_LIMIT_WINDOW', 3600);
define('ENABLE_API_DOCUMENTATION', true);

// إعدادات الشهادات
define('CERTIFICATE_TEMPLATE_PATH', __DIR__ . '/../templates/certificates/');
define('CERTIFICATE_FONT_PATH', __DIR__ . '/../assets/fonts/');
define('ENABLE_DIGITAL_CERTIFICATES', true);

// إعدادات التقييم
define('ENABLE_COURSE_RATINGS', true);
define('ENABLE_INSTRUCTOR_RATINGS', true);
define('MIN_RATING', 1);
define('MAX_RATING', 5);

// إعدادات الدردشة
define('ENABLE_LIVE_CHAT', true);
define('CHAT_MESSAGE_LIMIT', 1000);
define('CHAT_FILE_UPLOAD_LIMIT', 10 * 1024 * 1024); // 10MB

// إعدادات الألعاب والتحفيز
define('ENABLE_GAMIFICATION', true);
define('POINTS_PER_LESSON', 10);
define('POINTS_PER_QUIZ', 50);
define('POINTS_PER_ASSIGNMENT', 100);

// إعدادات التكامل مع الأنظمة الخارجية
define('ENABLE_GOOGLE_CLASSROOM', false);
define('ENABLE_MICROSOFT_TEAMS', false);
define('ENABLE_CANVAS_LMS', false);

// دالة للحصول على جميع الإعدادات
function getAllSystemSettings() {
    return [
        'site' => [
            'name' => SITE_NAME,
            'description' => SITE_DESCRIPTION,
            'version' => SITE_VERSION,
            'url' => SITE_URL
        ],
        'security' => [
            'session_timeout' => SESSION_TIMEOUT,
            'max_login_attempts' => MAX_LOGIN_ATTEMPTS,
            'lockout_time' => LOGIN_LOCKOUT_TIME
        ],
        'upload' => [
            'max_file_size' => MAX_FILE_SIZE,
            'max_video_size' => MAX_VIDEO_SIZE,
            'max_image_size' => MAX_IMAGE_SIZE
        ],
        'features' => [
            'enable_cache' => ENABLE_CACHE,
            'enable_2fa' => ENABLE_2FA,
            'enable_ratings' => ENABLE_COURSE_RATINGS
        ]
    ];
}

// دالة للتحقق من صحة الإعدادات
function validateSystemSettings() {
    $errors = [];
    
    // التحقق من المجلدات المطلوبة
    $required_dirs = [UPLOAD_PATH, TEMP_PATH, CACHE_PATH, LOGS_PATH];
    foreach ($required_dirs as $dir) {
        if (!is_dir($dir) || !is_writable($dir)) {
            $errors[] = "المجلد غير موجود أو غير قابل للكتابة: $dir";
        }
    }
    
    // التحقق من إعدادات البريد الإلكتروني
    if (empty(SMTP_USERNAME) || empty(SMTP_PASSWORD)) {
        $errors[] = "إعدادات البريد الإلكتروني غير مكتملة";
    }
    
    return $errors;
}

// إنشاء المجلدات المطلوبة
function createRequiredDirectories() {
    $required_dirs = [
        UPLOAD_PATH,
        TEMP_PATH,
        CACHE_PATH,
        LOGS_PATH,
        BACKUP_PATH,
        UPLOAD_PATH . 'videos/',
        UPLOAD_PATH . 'images/',
        UPLOAD_PATH . 'documents/',
        UPLOAD_PATH . 'audio/',
        UPLOAD_PATH . 'thumbnails/',
        CERTIFICATE_TEMPLATE_PATH
    ];

    foreach ($required_dirs as $dir) {
        if (!file_exists($dir)) {
            if (mkdir($dir, 0755, true)) {
                // إنشاء ملف .htaccess للحماية
                $htaccess_content = "Options -Indexes\nDeny from all\n<Files *.php>\nAllow from all\n</Files>";
                file_put_contents($dir . '.htaccess', $htaccess_content);
                
                // إنشاء ملف index.php فارغ
                file_put_contents($dir . 'index.php', '<?php // Access denied ?>');
            }
        }
    }
}

// تشغيل إنشاء المجلدات
createRequiredDirectories();
?>
