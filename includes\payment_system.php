<?php
/**
 * نظام الدفع المحسن
 * Enhanced Payment System
 * =======================
 */

require_once 'config/config.php';
require_once 'notification_system.php';

class PaymentSystem {
    private $conn;
    private $notificationSystem;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->notificationSystem = new NotificationSystem($database_connection);
        $this->createPaymentTables();
    }
    
    /**
     * إنشاء جداول الدفع
     */
    private function createPaymentTables() {
        try {
            // جدول المدفوعات المحسن
            $sql = "CREATE TABLE IF NOT EXISTS payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                payment_method ENUM('stripe', 'paypal', 'bank_transfer', 'wallet') NOT NULL,
                transaction_id VARCHAR(255) NOT NULL UNIQUE,
                payment_intent_id VARCHAR(255) NULL,
                session_id VARCHAR(255) NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'USD',
                platform_fee DECIMAL(10,2) DEFAULT 0,
                instructor_amount DECIMAL(10,2) DEFAULT 0,
                status ENUM('pending', 'processing', 'completed', 'failed', 'refunded', 'cancelled') DEFAULT 'pending',
                payment_date TIMESTAMP NULL,
                refund_date TIMESTAMP NULL,
                refund_reason TEXT NULL,
                gateway_response JSON NULL,
                metadata JSON NULL,
                notes TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                INDEX idx_user (user_id),
                INDEX idx_course (course_id),
                INDEX idx_status (status),
                INDEX idx_transaction (transaction_id),
                INDEX idx_payment_date (payment_date),
                INDEX idx_method (payment_method)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // جدول محفظة المستخدم
            $sql = "CREATE TABLE IF NOT EXISTS user_wallets (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL UNIQUE,
                balance DECIMAL(10,2) DEFAULT 0.00,
                currency VARCHAR(3) DEFAULT 'USD',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user (user_id),
                INDEX idx_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // جدول معاملات المحفظة
            $sql = "CREATE TABLE IF NOT EXISTS wallet_transactions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                wallet_id INT NOT NULL,
                type ENUM('credit', 'debit') NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                description VARCHAR(255) NOT NULL,
                reference_type ENUM('payment', 'refund', 'commission', 'withdrawal', 'deposit') NOT NULL,
                reference_id INT NULL,
                balance_before DECIMAL(10,2) NOT NULL,
                balance_after DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (wallet_id) REFERENCES user_wallets(id) ON DELETE CASCADE,
                INDEX idx_wallet (wallet_id),
                INDEX idx_type (type),
                INDEX idx_reference (reference_type, reference_id),
                INDEX idx_created (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // جدول كوبونات الخصم
            $sql = "CREATE TABLE IF NOT EXISTS discount_coupons (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(50) NOT NULL UNIQUE,
                type ENUM('percentage', 'fixed') NOT NULL,
                value DECIMAL(10,2) NOT NULL,
                minimum_amount DECIMAL(10,2) DEFAULT 0,
                maximum_discount DECIMAL(10,2) NULL,
                usage_limit INT DEFAULT NULL,
                used_count INT DEFAULT 0,
                valid_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                valid_until TIMESTAMP NULL,
                applicable_courses JSON NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_code (code),
                INDEX idx_active (is_active),
                INDEX idx_valid (valid_from, valid_until)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
        } catch (PDOException $e) {
            error_log("Error creating payment tables: " . $e->getMessage());
        }
    }
    
    /**
     * إنشاء جلسة دفع Stripe
     */
    public function createStripeSession($userId, $courseId, $amount, $currency = 'USD') {
        try {
            // التحقق من إعدادات Stripe
            if (empty(STRIPE_SECRET_KEY) || STRIPE_SECRET_KEY === 'sk_test_your_stripe_secret_key') {
                throw new Exception('Stripe not configured');
            }
            
            // الحصول على معلومات المستخدم والكورس
            $user = $this->getUserInfo($userId);
            $course = $this->getCourseInfo($courseId);
            
            if (!$user || !$course) {
                throw new Exception('User or course not found');
            }
            
            // حساب العمولة
            $platformFee = $this->calculatePlatformFee($amount);
            $instructorAmount = $amount - $platformFee;
            
            // إنشاء معرف المعاملة
            $transactionId = $this->generateTransactionId();
            
            // بيانات جلسة Stripe
            $sessionData = [
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => strtolower($currency),
                        'product_data' => [
                            'name' => $course['title'],
                            'description' => $course['description'] ?? '',
                            'images' => [$course['thumbnail'] ?? '']
                        ],
                        'unit_amount' => $amount * 100 // Stripe uses cents
                    ],
                    'quantity' => 1
                ]],
                'mode' => 'payment',
                'success_url' => SITE_URL . '/payment-success.php?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => SITE_URL . '/payment-cancel.php',
                'customer_email' => $user['email'],
                'metadata' => [
                    'user_id' => $userId,
                    'course_id' => $courseId,
                    'transaction_id' => $transactionId,
                    'platform_fee' => $platformFee,
                    'instructor_amount' => $instructorAmount
                ]
            ];
            
            // إرسال الطلب إلى Stripe (محاكاة)
            $response = $this->mockStripeRequest('POST', '/checkout/sessions', $sessionData);
            
            // حفظ المعاملة في قاعدة البيانات
            $paymentId = $this->createPaymentRecord([
                'user_id' => $userId,
                'course_id' => $courseId,
                'payment_method' => 'stripe',
                'transaction_id' => $transactionId,
                'session_id' => $response['id'],
                'amount' => $amount,
                'currency' => $currency,
                'platform_fee' => $platformFee,
                'instructor_amount' => $instructorAmount,
                'status' => 'pending',
                'gateway_response' => json_encode($response)
            ]);
            
            return [
                'success' => true,
                'session_id' => $response['id'],
                'checkout_url' => $response['url'],
                'payment_id' => $paymentId
            ];
            
        } catch (Exception $e) {
            error_log("Stripe Session Error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء طلب دفع PayPal
     */
    public function createPayPalOrder($userId, $courseId, $amount, $currency = 'USD') {
        try {
            // التحقق من إعدادات PayPal
            if (empty(PAYPAL_CLIENT_ID) || PAYPAL_CLIENT_ID === 'your_paypal_client_id') {
                throw new Exception('PayPal not configured');
            }
            
            // الحصول على معلومات المستخدم والكورس
            $user = $this->getUserInfo($userId);
            $course = $this->getCourseInfo($courseId);
            
            if (!$user || !$course) {
                throw new Exception('User or course not found');
            }
            
            // حساب العمولة
            $platformFee = $this->calculatePlatformFee($amount);
            $instructorAmount = $amount - $platformFee;
            
            // إنشاء معرف المعاملة
            $transactionId = $this->generateTransactionId();
            
            // بيانات طلب PayPal
            $orderData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [[
                    'amount' => [
                        'currency_code' => $currency,
                        'value' => number_format($amount, 2, '.', '')
                    ],
                    'description' => $course['title'],
                    'custom_id' => $transactionId
                ]],
                'application_context' => [
                    'return_url' => SITE_URL . '/payment-success.php',
                    'cancel_url' => SITE_URL . '/payment-cancel.php',
                    'brand_name' => SITE_NAME,
                    'locale' => 'ar-SA',
                    'landing_page' => 'BILLING',
                    'user_action' => 'PAY_NOW'
                ]
            ];
            
            // إرسال الطلب إلى PayPal (محاكاة)
            $response = $this->mockPayPalRequest('POST', '/v2/checkout/orders', $orderData);
            
            // حفظ المعاملة في قاعدة البيانات
            $paymentId = $this->createPaymentRecord([
                'user_id' => $userId,
                'course_id' => $courseId,
                'payment_method' => 'paypal',
                'transaction_id' => $transactionId,
                'payment_intent_id' => $response['id'],
                'amount' => $amount,
                'currency' => $currency,
                'platform_fee' => $platformFee,
                'instructor_amount' => $instructorAmount,
                'status' => 'pending',
                'gateway_response' => json_encode($response)
            ]);
            
            // البحث عن رابط الموافقة
            $approvalUrl = '';
            foreach ($response['links'] as $link) {
                if ($link['rel'] === 'approve') {
                    $approvalUrl = $link['href'];
                    break;
                }
            }
            
            return [
                'success' => true,
                'order_id' => $response['id'],
                'approval_url' => $approvalUrl,
                'payment_id' => $paymentId
            ];
            
        } catch (Exception $e) {
            error_log("PayPal Order Error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * معالجة الدفع الناجح
     */
    public function processSuccessfulPayment($transactionId, $gatewayData = []) {
        try {
            // الحصول على معلومات المعاملة
            $payment = $this->getPaymentByTransaction($transactionId);
            if (!$payment) {
                throw new Exception('Payment not found');
            }
            
            // تحديث حالة المعاملة
            $this->updatePaymentStatus($payment['id'], 'completed', $gatewayData);
            
            // تسجيل الطالب في الكورس
            $this->enrollStudentInCourse($payment['user_id'], $payment['course_id']);
            
            // إضافة المبلغ إلى محفظة المدرب
            $this->addToInstructorWallet($payment['course_id'], $payment['instructor_amount']);
            
            // إرسال إشعارات
            $this->sendPaymentNotifications($payment);
            
            // تسجيل النشاط
            $this->logActivity($payment['user_id'], 'payment_completed', [
                'course_id' => $payment['course_id'],
                'amount' => $payment['amount'],
                'currency' => $payment['currency']
            ]);
            
            return [
                'success' => true,
                'message' => 'Payment processed successfully'
            ];
            
        } catch (Exception $e) {
            error_log("Payment Processing Error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تطبيق كوبون خصم
     */
    public function applyCoupon($code, $courseId, $amount) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM discount_coupons 
                WHERE code = ? AND is_active = TRUE 
                AND (valid_until IS NULL OR valid_until > NOW())
                AND (usage_limit IS NULL OR used_count < usage_limit)
            ");
            $stmt->execute([$code]);
            $coupon = $stmt->fetch();
            
            if (!$coupon) {
                return [
                    'success' => false,
                    'error' => 'كوبون الخصم غير صالح أو منتهي الصلاحية'
                ];
            }
            
            // التحقق من الحد الأدنى للمبلغ
            if ($amount < $coupon['minimum_amount']) {
                return [
                    'success' => false,
                    'error' => 'المبلغ أقل من الحد الأدنى المطلوب للكوبون'
                ];
            }
            
            // التحقق من انطباق الكوبون على الكورس
            if ($coupon['applicable_courses']) {
                $applicableCourses = json_decode($coupon['applicable_courses'], true);
                if (!in_array($courseId, $applicableCourses)) {
                    return [
                        'success' => false,
                        'error' => 'هذا الكوبون غير صالح لهذا الكورس'
                    ];
                }
            }
            
            // حساب الخصم
            $discount = 0;
            if ($coupon['type'] === 'percentage') {
                $discount = ($amount * $coupon['value']) / 100;
                if ($coupon['maximum_discount'] && $discount > $coupon['maximum_discount']) {
                    $discount = $coupon['maximum_discount'];
                }
            } else {
                $discount = min($coupon['value'], $amount);
            }
            
            $finalAmount = max(0, $amount - $discount);
            
            return [
                'success' => true,
                'discount' => $discount,
                'final_amount' => $finalAmount,
                'coupon_id' => $coupon['id']
            ];
            
        } catch (Exception $e) {
            error_log("Coupon Application Error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ أثناء تطبيق الكوبون'
            ];
        }
    }
    
    /**
     * حساب عمولة المنصة
     */
    private function calculatePlatformFee($amount) {
        $commissionRate = (float)getSystemSetting('platform_commission', PLATFORM_COMMISSION_RATE);
        return ($amount * $commissionRate) / 100;
    }
    
    /**
     * إنشاء معرف معاملة فريد
     */
    private function generateTransactionId() {
        return 'TXN_' . time() . '_' . rand(1000, 9999);
    }
    
    /**
     * إنشاء سجل دفع في قاعدة البيانات
     */
    private function createPaymentRecord($data) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO payments 
                (user_id, course_id, payment_method, transaction_id, payment_intent_id, 
                 session_id, amount, currency, platform_fee, instructor_amount, status, 
                 gateway_response, metadata) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['user_id'],
                $data['course_id'],
                $data['payment_method'],
                $data['transaction_id'],
                $data['payment_intent_id'] ?? null,
                $data['session_id'] ?? null,
                $data['amount'],
                $data['currency'],
                $data['platform_fee'],
                $data['instructor_amount'],
                $data['status'],
                $data['gateway_response'] ?? null,
                $data['metadata'] ?? null
            ]);
            
            return $this->conn->lastInsertId();
        } catch (PDOException $e) {
            error_log("Error creating payment record: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * محاكاة طلب Stripe
     */
    private function mockStripeRequest($method, $endpoint, $data) {
        // محاكاة استجابة Stripe
        return [
            'id' => 'cs_test_' . uniqid(),
            'object' => 'checkout.session',
            'url' => SITE_URL . '/mock-stripe-checkout.php?session_id=cs_test_' . uniqid(),
            'payment_status' => 'unpaid',
            'status' => 'open'
        ];
    }
    
    /**
     * محاكاة طلب PayPal
     */
    private function mockPayPalRequest($method, $endpoint, $data) {
        // محاكاة استجابة PayPal
        $orderId = uniqid('ORDER_');
        return [
            'id' => $orderId,
            'status' => 'CREATED',
            'links' => [
                [
                    'href' => SITE_URL . '/mock-paypal-checkout.php?order_id=' . $orderId,
                    'rel' => 'approve',
                    'method' => 'GET'
                ]
            ]
        ];
    }
    
    // دوال مساعدة أخرى...
    private function getUserInfo($userId) {
        try {
            $stmt = $this->conn->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            return null;
        }
    }
    
    private function getCourseInfo($courseId) {
        try {
            $stmt = $this->conn->prepare("SELECT * FROM courses WHERE id = ?");
            $stmt->execute([$courseId]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            return null;
        }
    }
    
    private function getPaymentByTransaction($transactionId) {
        try {
            $stmt = $this->conn->prepare("SELECT * FROM payments WHERE transaction_id = ?");
            $stmt->execute([$transactionId]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            return null;
        }
    }
    
    private function updatePaymentStatus($paymentId, $status, $gatewayData = []) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE payments 
                SET status = ?, payment_date = NOW(), gateway_response = ? 
                WHERE id = ?
            ");
            $stmt->execute([$status, json_encode($gatewayData), $paymentId]);
        } catch (PDOException $e) {
            error_log("Error updating payment status: " . $e->getMessage());
        }
    }
    
    private function enrollStudentInCourse($userId, $courseId) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO course_enrollments (student_id, course_id, status, enrolled_at) 
                VALUES (?, ?, 'active', NOW())
                ON DUPLICATE KEY UPDATE status = 'active', enrolled_at = NOW()
            ");
            $stmt->execute([$userId, $courseId]);
        } catch (PDOException $e) {
            error_log("Error enrolling student: " . $e->getMessage());
        }
    }
    
    private function addToInstructorWallet($courseId, $amount) {
        // تنفيذ إضافة المبلغ إلى محفظة المدرب
        // سيتم تطوير هذه الوظيفة لاحقاً
    }
    
    private function sendPaymentNotifications($payment) {
        // إرسال إشعارات الدفع
        $this->notificationSystem->sendNotification(
            $payment['user_id'],
            'email',
            'payment',
            'payment_success',
            [
                'user_name' => 'المستخدم',
                'amount' => $payment['amount'],
                'currency' => $payment['currency'],
                'course_title' => 'الكورس',
                'transaction_id' => $payment['transaction_id']
            ]
        );
    }
    
    private function logActivity($userId, $action, $data) {
        // تسجيل النشاط
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO activity_logs (user_id, action, details, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$userId, $action, json_encode($data)]);
        } catch (PDOException $e) {
            error_log("Error logging activity: " . $e->getMessage());
        }
    }
}

// دوال مساعدة سريعة
function processQuickPayment($userId, $courseId, $amount, $method = 'stripe') {
    global $conn;
    
    try {
        $paymentSystem = new PaymentSystem($conn);
        
        if ($method === 'stripe') {
            return $paymentSystem->createStripeSession($userId, $courseId, $amount);
        } elseif ($method === 'paypal') {
            return $paymentSystem->createPayPalOrder($userId, $courseId, $amount);
        }
        
        return ['success' => false, 'error' => 'Unsupported payment method'];
    } catch (Exception $e) {
        error_log("Quick Payment Error: " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}
?>
