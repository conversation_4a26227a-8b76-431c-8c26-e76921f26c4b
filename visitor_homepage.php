<?php
session_start();
require_once 'config/database.php';
require_once 'config/config_enhanced.php';
require_once 'includes/functions.php';

// الحصول على إحصائيات المنصة
try {
    $stats_stmt = $conn->query("
        SELECT
            (SELECT COUNT(*) FROM courses WHERE status = 'active') as total_courses,
            (SELECT COUNT(*) FROM users WHERE role = 'instructor' AND status = 'active') as total_instructors,
            (SELECT COUNT(*) FROM users WHERE role = 'student' AND status = 'active') as total_students,
            (SELECT COUNT(*) FROM course_lessons WHERE status = 'published') as total_lessons,
            (SELECT COUNT(*) FROM course_enrollments WHERE status = 'active') as total_enrollments,
            (SELECT COUNT(*) FROM course_enrollments WHERE status = 'completed') as total_graduates
    ");
    $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $stats = [
        'total_courses' => 0,
        'total_instructors' => 0,
        'total_students' => 0,
        'total_lessons' => 0,
        'total_enrollments' => 0,
        'total_graduates' => 0
    ];
}

// الحصول على الكورسات المميزة
try {
    $featured_stmt = $conn->query("
        SELECT c.*, u.name as instructor_name, cat.name as category_name,
               (SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id) as avg_rating,
               (SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id) as enrolled_count
        FROM courses c
        LEFT JOIN users u ON c.instructor_id = u.id
        LEFT JOIN categories cat ON c.category_id = cat.id
        WHERE c.status = 'active' AND c.featured = 1
        ORDER BY c.rating DESC, c.total_students DESC
        LIMIT 6
    ");
    $featured_courses = $featured_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $featured_courses = [];
}

// الحصول على التصنيفات الرئيسية
try {
    $categories_stmt = $conn->query("
        SELECT cat.*, COUNT(c.id) as courses_count
        FROM categories cat
        LEFT JOIN courses c ON cat.id = c.category_id AND c.status = 'active'
        WHERE cat.is_active = 1 AND cat.parent_id IS NULL
        GROUP BY cat.id
        ORDER BY courses_count DESC
        LIMIT 8
    ");
    $categories = $categories_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $categories = [];
}

// الحصول على أفضل المدربين
try {
    $instructors_stmt = $conn->query("
        SELECT u.*,
               COUNT(c.id) as total_courses,
               AVG(cr.rating) as avg_rating,
               SUM(c.total_students) as total_students
        FROM users u
        LEFT JOIN courses c ON u.id = c.instructor_id AND c.status = 'active'
        LEFT JOIN course_reviews cr ON c.id = cr.course_id
        WHERE u.role = 'instructor' AND u.status = 'active'
        GROUP BY u.id
        HAVING total_courses > 0
        ORDER BY avg_rating DESC, total_students DESC
        LIMIT 4
    ");
    $top_instructors = $instructors_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $top_instructors = [];
}

// الحصول على آراء الطلاب
try {
    $testimonials_stmt = $conn->query("
        SELECT cr.*, u.name as student_name, c.title as course_title
        FROM course_reviews cr
        JOIN users u ON cr.student_id = u.id
        JOIN courses c ON cr.course_id = c.id
        WHERE cr.rating >= 4 AND cr.review_text IS NOT NULL
        ORDER BY cr.created_at DESC
        LIMIT 6
    ");
    $testimonials = $testimonials_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $testimonials = [];
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="منصة التعلم الإلكتروني الرائدة - تعلم مهارات جديدة مع أفضل المدربين المحترفين">
    <meta name="keywords" content="تعلم إلكتروني, كورسات أونلاين, تدريب, شهادات معتمدة, مهارات">
    <meta name="author" content="منصة التعلم الإلكتروني">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="منصة التعلم الإلكتروني - تعلم مهارات المستقبل">
    <meta property="og:description" content="انضم إلى آلاف الطلاب واكتسب مهارات جديدة مع أفضل المدربين">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:type" content="website">

    <title>منصة التعلم الإلكتروني - تعلم مهارات المستقبل</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="/assets/images/apple-touch-icon.png">

    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #4facfe;
            --warning-color: #43e97b;
            --danger-color: #f5576c;

            --dark-color: #2d3748;
            --light-color: #f8fafc;
            --gray-100: #f7fafc;
            --gray-200: #edf2f7;
            --gray-300: #e2e8f0;
            --gray-400: #cbd5e0;
            --gray-500: #a0aec0;
            --gray-600: #718096;
            --gray-700: #4a5568;
            --gray-800: #2d3748;
            --gray-900: #1a202c;

            --border-radius: 15px;
            --border-radius-lg: 20px;
            --border-radius-xl: 25px;

            --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
            --shadow: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-md: 0 8px 25px rgba(0,0,0,0.15);
            --shadow-lg: 0 15px 35px rgba(0,0,0,0.1);
            --shadow-xl: 0 25px 50px rgba(0,0,0,0.25);

            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: var(--light-color);
            overflow-x: hidden;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-logo {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        .loading-text {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 2rem;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Header */
        .navbar-modern {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            transition: var(--transition);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .navbar-modern.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-md);
            padding: 0.5rem 0;
        }

        .navbar-brand-modern {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 800;
            font-size: 1.5rem;
            transition: var(--transition);
        }

        .navbar-brand-modern:hover {
            color: var(--primary-color);
        }

        .navbar-brand-modern img {
            height: 45px;
            margin-left: 10px;
        }

        .navbar-nav-modern {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .nav-link-modern {
            color: var(--dark-color);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
        }

        .nav-link-modern:hover {
            color: var(--primary-color);
            background: rgba(102, 126, 234, 0.1);
        }

        .nav-link-modern.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .btn-auth {
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
        }

        .btn-login {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-login:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-register {
            background: var(--primary-gradient);
            color: white;
            margin-right: 1rem;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        /* Mobile Menu */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark-color);
            cursor: pointer;
        }

        .mobile-menu {
            display: none;
            position: fixed;
            top: 0;
            right: -100%;
            width: 300px;
            height: 100vh;
            background: white;
            box-shadow: var(--shadow-xl);
            transition: right 0.3s ease;
            z-index: 1001;
            padding: 2rem;
        }

        .mobile-menu.active {
            right: 0;
        }

        .mobile-menu-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .mobile-menu-overlay.active {
            opacity: 1;
        }

        /* Hero Section */
        .hero-section {
            background: var(--primary-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding-top: 100px;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255,255,255,0.05) 0%, transparent 50%);
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
        }

        .hero-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            line-height: 1.2;
            margin-bottom: 1.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-subtitle {
            font-size: clamp(1.1rem, 2vw, 1.3rem);
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.95;
            max-width: 600px;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .btn-hero {
            padding: 1rem 2rem;
            border-radius: var(--border-radius-lg);
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-hero-primary {
            background: white;
            color: var(--primary-color);
        }

        .btn-hero-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            color: var(--primary-color);
        }

        .btn-hero-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-hero-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-3px);
        }

        .hero-stats {
            display: flex;
            gap: 3rem;
            flex-wrap: wrap;
        }

        .hero-stat {
            text-align: center;
        }

        .hero-stat-number {
            display: block;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #fff, rgba(255,255,255,0.8));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .hero-image {
            position: relative;
            z-index: 2;
        }

        .hero-image img {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-xl);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Floating Elements */
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 8s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        /* Section Styles */
        .section-modern {
            padding: 5rem 0;
            position: relative;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1rem;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: var(--gray-600);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        /* Cards */
        .card-modern {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: var(--transition);
            border: none;
            height: 100%;
        }

        .card-modern:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-xl);
        }

        .card-image {
            position: relative;
            overflow: hidden;
            height: 250px;
        }

        .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .card-modern:hover .card-image img {
            transform: scale(1.1);
        }

        .card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.7));
            display: flex;
            align-items: flex-end;
            padding: 1.5rem;
            opacity: 0;
            transition: var(--transition);
        }

        .card-modern:hover .card-overlay {
            opacity: 1;
        }

        .card-body-modern {
            padding: 2rem;
        }

        .card-title-modern {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .card-text-modern {
            color: var(--gray-600);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar-nav-modern {
                display: none;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .mobile-menu {
                display: block;
            }

            .mobile-menu-overlay {
                display: block;
            }

            .hero-section {
                padding-top: 80px;
                text-align: center;
            }

            .hero-buttons {
                justify-content: center;
            }

            .hero-stats {
                justify-content: center;
                gap: 2rem;
            }

            .section-modern {
                padding: 3rem 0;
            }
        }

        @media (max-width: 576px) {
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn-hero {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }

            .hero-stats {
                gap: 1.5rem;
            }

            .hero-stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>

<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="loading-text">منصة التعلم الإلكتروني</div>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="mb-0">القائمة</h5>
            <button class="btn btn-link p-0" id="closeMobileMenu">
                <i class="fas fa-times fa-lg"></i>
            </button>
        </div>

        <nav class="mobile-nav">
            <a href="#home" class="nav-link-modern d-block mb-3">الرئيسية</a>
            <a href="#courses" class="nav-link-modern d-block mb-3">الكورسات</a>
            <a href="#instructors" class="nav-link-modern d-block mb-3">المدربين</a>
            <a href="#about" class="nav-link-modern d-block mb-3">من نحن</a>
            <a href="#contact" class="nav-link-modern d-block mb-3">تواصل معنا</a>
        </nav>

        <div class="mt-4">
            <a href="login.php" class="btn btn-login d-block mb-2 text-center">تسجيل الدخول</a>
            <a href="register.php" class="btn btn-register d-block text-center">إنشاء حساب</a>
        </div>
    </div>

    <!-- Header -->
    <nav class="navbar-modern" id="navbar">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center w-100">
                <!-- Brand -->
                <a href="#" class="navbar-brand-modern">
                    <img src="/assets/images/logo.png" alt="Logo" onerror="this.style.display='none'">
                    <span>منصة التعلم</span>
                </a>

                <!-- Desktop Navigation -->
                <div class="navbar-nav-modern">
                    <a href="#home" class="nav-link-modern active">الرئيسية</a>
                    <a href="#courses" class="nav-link-modern">الكورسات</a>
                    <a href="#instructors" class="nav-link-modern">المدربين</a>
                    <a href="#about" class="nav-link-modern">من نحن</a>
                    <a href="#contact" class="nav-link-modern">تواصل معنا</a>
                </div>

                <!-- Auth Buttons -->
                <div class="d-flex align-items-center">
                    <a href="register.php" class="btn-auth btn-register">إنشاء حساب</a>
                    <a href="login.php" class="btn-auth btn-login">تسجيل الدخول</a>

                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <!-- Floating Elements -->
        <div class="floating-element">
            <i class="fas fa-book fa-3x"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-graduation-cap fa-4x"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-laptop-code fa-2x"></i>
        </div>

        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6" data-aos="fade-right">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            تعلم مهارات <span class="text-warning">المستقبل</span>
                            <br>مع أفضل المدربين
                        </h1>

                        <p class="hero-subtitle">
                            انضم إلى آلاف الطلاب واكتسب مهارات جديدة في البرمجة، التصميم، التسويق الرقمي وأكثر.
                            تعلم بمرونة وفي أي وقت تشاء مع شهادات معتمدة.
                        </p>

                        <div class="hero-buttons">
                            <a href="courses.php" class="btn-hero btn-hero-primary">
                                <i class="fas fa-play"></i>
                                ابدأ التعلم الآن
                            </a>
                            <a href="#courses" class="btn-hero btn-hero-secondary">
                                <i class="fas fa-search"></i>
                                استكشف الكورسات
                            </a>
                        </div>

                        <div class="hero-stats">
                            <div class="hero-stat">
                                <span class="hero-stat-number"><?php echo number_format($stats['total_courses']); ?>+</span>
                                <span class="hero-stat-label">كورس متاح</span>
                            </div>
                            <div class="hero-stat">
                                <span class="hero-stat-number"><?php echo number_format($stats['total_students']); ?>+</span>
                                <span class="hero-stat-label">طالب نشط</span>
                            </div>
                            <div class="hero-stat">
                                <span class="hero-stat-number"><?php echo number_format($stats['total_instructors']); ?>+</span>
                                <span class="hero-stat-label">مدرب محترف</span>
                            </div>
                            <div class="hero-stat">
                                <span class="hero-stat-number"><?php echo number_format($stats['total_graduates']); ?>+</span>
                                <span class="hero-stat-label">خريج معتمد</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6" data-aos="fade-left">
                    <div class="hero-image">
                        <img src="/assets/images/hero-image.jpg" alt="التعلم الإلكتروني"
                             onerror="this.src='https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="section-modern bg-light" id="features">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">لماذا تختار منصتنا؟</h2>
                <p class="section-subtitle">
                    نوفر لك تجربة تعليمية متميزة مع أحدث التقنيات وأفضل المدربين
                </p>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-modern text-center">
                        <div class="card-body-modern">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px; background: var(--primary-gradient); border-radius: 50%; color: white; font-size: 2rem;">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                            </div>
                            <h4 class="card-title-modern">محتوى عالي الجودة</h4>
                            <p class="card-text-modern">
                                فيديوهات عالية الدقة ومحتوى محدث باستمرار من أفضل المدربين في المجال
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-modern text-center">
                        <div class="card-body-modern">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px; background: var(--success-gradient); border-radius: 50%; color: white; font-size: 2rem;">
                                    <i class="fas fa-certificate"></i>
                                </div>
                            </div>
                            <h4 class="card-title-modern">شهادات معتمدة</h4>
                            <p class="card-text-modern">
                                احصل على شهادات معتمدة عند إتمام الكورسات لتعزز من فرصك المهنية
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-modern text-center">
                        <div class="card-body-modern">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px; background: var(--warning-gradient); border-radius: 50%; color: white; font-size: 2rem;">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                            <h4 class="card-title-modern">تعلم بمرونة</h4>
                            <p class="card-text-modern">
                                تعلم في أي وقت ومن أي مكان بالسرعة التي تناسبك مع إمكانية الوصول مدى الحياة
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-modern text-center">
                        <div class="card-body-modern">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px; background: var(--secondary-gradient); border-radius: 50%; color: white; font-size: 2rem;">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <h4 class="card-title-modern">مجتمع تفاعلي</h4>
                            <p class="card-text-modern">
                                انضم لمجتمع من المتعلمين والمدربين وشارك خبراتك واستفد من تجارب الآخرين
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
                    <div class="card-modern text-center">
                        <div class="card-body-modern">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px; background: var(--accent-color); border-radius: 50%; color: white; font-size: 2rem;">
                                    <i class="fas fa-headset"></i>
                                </div>
                            </div>
                            <h4 class="card-title-modern">دعم فني 24/7</h4>
                            <p class="card-text-modern">
                                فريق دعم متاح على مدار الساعة لمساعدتك في أي استفسار أو مشكلة تقنية
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
                    <div class="card-modern text-center">
                        <div class="card-body-modern">
                            <div class="mb-4">
                                <div class="d-inline-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px; background: var(--danger-color); border-radius: 50%; color: white; font-size: 2rem;">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                            </div>
                            <h4 class="card-title-modern">تطبيق جوال</h4>
                            <p class="card-text-modern">
                                تعلم أثناء التنقل مع تطبيقنا المتاح على جميع الأجهزة الذكية
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="section-modern" id="categories">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">تصنيفات الكورسات</h2>
                <p class="section-subtitle">
                    اختر من بين مجموعة واسعة من التصنيفات المتخصصة
                </p>
            </div>

            <div class="row g-4">
                <?php foreach ($categories as $index => $category): ?>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo ($index + 1) * 100; ?>">
                    <div class="card-modern category-card">
                        <div class="card-body-modern text-center">
                            <div class="category-icon mb-3" style="color: <?php echo $category['color']; ?>;">
                                <i class="<?php echo $category['icon'] ?? 'fas fa-book'; ?> fa-3x"></i>
                            </div>
                            <h5 class="card-title-modern"><?php echo htmlspecialchars($category['name']); ?></h5>
                            <p class="card-text-modern"><?php echo htmlspecialchars($category['description'] ?? ''); ?></p>
                            <div class="mt-3">
                                <span class="badge" style="background: <?php echo $category['color']; ?>20; color: <?php echo $category['color']; ?>;">
                                    <?php echo $category['courses_count']; ?> كورس
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <div class="text-center mt-5" data-aos="fade-up">
                <a href="courses.php" class="btn btn-primary btn-lg px-5">
                    <i class="fas fa-search me-2"></i>
                    استكشف جميع الكورسات
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Courses Section -->
    <section class="section-modern bg-light" id="courses">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">الكورسات المميزة</h2>
                <p class="section-subtitle">
                    أفضل الكورسات المختارة بعناية من قبل خبرائنا
                </p>
            </div>

            <div class="row g-4">
                <?php foreach ($featured_courses as $index => $course): ?>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo ($index + 1) * 100; ?>">
                    <div class="card-modern course-card">
                        <div class="card-image">
                            <img src="<?php echo $course['thumbnail'] ?: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'; ?>"
                                 alt="<?php echo htmlspecialchars($course['title']); ?>"
                                 class="card-img-top">

                            <div class="card-overlay">
                                <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn btn-light">
                                    <i class="fas fa-eye me-2"></i>
                                    عرض التفاصيل
                                </a>
                            </div>

                            <?php if ($course['price'] > 0): ?>
                                <div class="position-absolute top-0 end-0 m-3">
                                    <span class="badge bg-success fs-6"><?php echo number_format($course['price']); ?> $</span>
                                </div>
                            <?php else: ?>
                                <div class="position-absolute top-0 end-0 m-3">
                                    <span class="badge bg-warning fs-6">مجاني</span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="card-body-modern">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-primary"><?php echo htmlspecialchars($course['category_name']); ?></span>
                                <div class="rating">
                                    <?php
                                    $rating = $course['avg_rating'] ?: 0;
                                    for ($i = 1; $i <= 5; $i++):
                                    ?>
                                        <i class="fas fa-star <?php echo $i <= $rating ? 'text-warning' : 'text-muted'; ?>"></i>
                                    <?php endfor; ?>
                                    <span class="ms-1 text-muted">(<?php echo $course['avg_rating'] ? number_format($course['avg_rating'], 1) : '0'; ?>)</span>
                                </div>
                            </div>

                            <h5 class="card-title-modern">
                                <a href="course-details.php?id=<?php echo $course['id']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </a>
                            </h5>

                            <p class="card-text-modern">
                                <?php echo htmlspecialchars(substr($course['short_description'] ?: $course['description'], 0, 100)); ?>...
                            </p>

                            <div class="d-flex justify-content-between align-items-center">
                                <div class="instructor-info">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        <?php echo htmlspecialchars($course['instructor_name']); ?>
                                    </small>
                                </div>
                                <div class="course-stats">
                                    <small class="text-muted">
                                        <i class="fas fa-users me-1"></i>
                                        <?php echo number_format($course['enrolled_count']); ?> طالب
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <div class="text-center mt-5" data-aos="fade-up">
                <a href="courses.php" class="btn btn-outline-primary btn-lg px-5">
                    <i class="fas fa-graduation-cap me-2"></i>
                    عرض جميع الكورسات
                </a>
            </div>
        </div>
    </section>

    <!-- Top Instructors Section -->
    <section class="section-modern" id="instructors">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">أفضل المدربين</h2>
                <p class="section-subtitle">
                    تعلم من خبراء المجال والمدربين المعتمدين
                </p>
            </div>

            <div class="row g-4">
                <?php foreach ($top_instructors as $index => $instructor): ?>
                <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo ($index + 1) * 100; ?>">
                    <div class="card-modern instructor-card text-center">
                        <div class="card-body-modern">
                            <div class="instructor-avatar mb-3">
                                <img src="<?php echo $instructor['profile_picture'] ?: 'https://ui-avatars.com/api/?name=' . urlencode($instructor['name']) . '&size=120&background=667eea&color=fff'; ?>"
                                     alt="<?php echo htmlspecialchars($instructor['name']); ?>"
                                     class="rounded-circle"
                                     style="width: 120px; height: 120px; object-fit: cover;">
                            </div>

                            <h5 class="card-title-modern"><?php echo htmlspecialchars($instructor['name']); ?></h5>

                            <p class="text-muted mb-3"><?php echo htmlspecialchars($instructor['specialization'] ?: 'مدرب محترف'); ?></p>

                            <div class="instructor-stats mb-3">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="stat-number"><?php echo $instructor['total_courses']; ?></div>
                                        <div class="stat-label">كورس</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-number"><?php echo number_format($instructor['total_students'] ?: 0); ?></div>
                                        <div class="stat-label">طالب</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-number"><?php echo $instructor['avg_rating'] ? number_format($instructor['avg_rating'], 1) : '5.0'; ?></div>
                                        <div class="stat-label">تقييم</div>
                                    </div>
                                </div>
                            </div>

                            <a href="instructor-profile.php?id=<?php echo $instructor['id']; ?>" class="btn btn-outline-primary">
                                <i class="fas fa-user me-2"></i>
                                عرض الملف الشخصي
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <?php if (!empty($testimonials)): ?>
    <section class="section-modern bg-light" id="testimonials">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">آراء طلابنا</h2>
                <p class="section-subtitle">
                    اكتشف تجارب طلابنا الناجحة معنا
                </p>
            </div>

            <div class="row g-4">
                <?php foreach (array_slice($testimonials, 0, 3) as $index => $testimonial): ?>
                <div class="col-lg-4" data-aos="fade-up" data-aos-delay="<?php echo ($index + 1) * 100; ?>">
                    <div class="card-modern testimonial-card">
                        <div class="card-body-modern">
                            <div class="testimonial-rating mb-3">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?php echo $i <= $testimonial['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                <?php endfor; ?>
                            </div>

                            <p class="testimonial-text">
                                "<?php echo htmlspecialchars(substr($testimonial['review_text'], 0, 150)); ?>..."
                            </p>

                            <div class="testimonial-author">
                                <strong><?php echo htmlspecialchars($testimonial['student_name']); ?></strong>
                                <div class="text-muted small">
                                    كورس: <?php echo htmlspecialchars($testimonial['course_title']); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- CTA Section -->
    <section class="section-modern" style="background: var(--primary-gradient);">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center text-white" data-aos="fade-up">
                    <h2 class="display-4 fw-bold mb-4">ابدأ رحلتك التعليمية اليوم</h2>
                    <p class="lead mb-5">
                        انضم إلى آلاف الطلاب الذين غيروا حياتهم المهنية من خلال منصتنا
                    </p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="register.php" class="btn btn-light btn-lg px-5">
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء حساب مجاني
                        </a>
                        <a href="courses.php" class="btn btn-outline-light btn-lg px-5">
                            <i class="fas fa-search me-2"></i>
                            تصفح الكورسات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row g-4">
                <!-- Company Info -->
                <div class="col-lg-4">
                    <div class="footer-brand mb-4">
                        <h4 class="fw-bold">منصة التعلم الإلكتروني</h4>
                        <p class="text-muted">
                            منصة تعليمية رائدة تهدف إلى تمكين الأفراد من اكتساب مهارات جديدة
                            وتطوير قدراتهم المهنية من خلال كورسات عالية الجودة.
                        </p>
                    </div>

                    <div class="social-links">
                        <h6 class="fw-semibold mb-3">تابعنا على</h6>
                        <div class="d-flex gap-3">
                            <a href="#" class="social-link">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-semibold mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled footer-links">
                        <li><a href="#home">الرئيسية</a></li>
                        <li><a href="courses.php">الكورسات</a></li>
                        <li><a href="#instructors">المدربين</a></li>
                        <li><a href="#about">من نحن</a></li>
                        <li><a href="#contact">تواصل معنا</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-semibold mb-3">التصنيفات</h6>
                    <ul class="list-unstyled footer-links">
                        <?php foreach (array_slice($categories, 0, 5) as $category): ?>
                        <li>
                            <a href="courses.php?category=<?php echo $category['id']; ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <!-- Support -->
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-semibold mb-3">الدعم</h6>
                    <ul class="list-unstyled footer-links">
                        <li><a href="help.php">مركز المساعدة</a></li>
                        <li><a href="faq.php">الأسئلة الشائعة</a></li>
                        <li><a href="privacy.php">سياسة الخصوصية</a></li>
                        <li><a href="terms.php">شروط الاستخدام</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-semibold mb-3">معلومات التواصل</h6>
                    <div class="contact-info">
                        <div class="contact-item mb-2">
                            <i class="fas fa-envelope me-2"></i>
                            <small><EMAIL></small>
                        </div>
                        <div class="contact-item mb-2">
                            <i class="fas fa-phone me-2"></i>
                            <small>+966 50 000 0000</small>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <small>المملكة العربية السعودية</small>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="my-4 border-secondary">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; <?php echo date('Y'); ?> منصة التعلم الإلكتروني. جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-badges">
                        <img src="/assets/images/badges/ssl-badge.png" alt="SSL Secured" height="30" class="me-2" onerror="this.style.display='none'">
                        <img src="/assets/images/badges/payment-badge.png" alt="Secure Payment" height="30" onerror="this.style.display='none'">
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Loading Screen
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loadingScreen');
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 1000);
        });

        // Navbar Scroll Effect
        const navbar = document.getElementById('navbar');
        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Mobile Menu
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const mobileMenu = document.getElementById('mobileMenu');
        const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
        const closeMobileMenu = document.getElementById('closeMobileMenu');

        function openMobileMenu() {
            mobileMenu.classList.add('active');
            mobileMenuOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeMobileMenuFunc() {
            mobileMenu.classList.remove('active');
            mobileMenuOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        mobileMenuToggle.addEventListener('click', openMobileMenu);
        closeMobileMenu.addEventListener('click', closeMobileMenuFunc);
        mobileMenuOverlay.addEventListener('click', closeMobileMenuFunc);

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    closeMobileMenuFunc();
                }
            });
        });

        // Back to Top Button
        const backToTop = document.getElementById('backToTop');

        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        backToTop.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Active Navigation Link
        const navLinks = document.querySelectorAll('.nav-link-modern');
        const sections = document.querySelectorAll('section[id]');

        function updateActiveNav() {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 150;
                if (window.scrollY >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        }

        window.addEventListener('scroll', updateActiveNav);

        // Typing Effect for Hero Title
        function typeWriter(element, text, speed = 100) {
            let i = 0;
            element.innerHTML = '';

            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }

            type();
        }

        // Initialize typing effect when page loads
        setTimeout(() => {
            const heroTitle = document.querySelector('.hero-title');
            if (heroTitle) {
                const originalText = heroTitle.textContent;
                typeWriter(heroTitle, originalText, 50);
            }
        }, 1500);

        // Counter Animation
        function animateCounters() {
            const counters = document.querySelectorAll('.hero-stat-number');

            counters.forEach(counter => {
                const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
                const increment = target / 100;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target.toLocaleString() + '+';
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current).toLocaleString() + '+';
                    }
                }, 20);
            });
        }

        // Start counter animation when hero section is visible
        const heroSection = document.getElementById('home');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    setTimeout(animateCounters, 2000);
                    observer.unobserve(entry.target);
                }
            });
        });

        if (heroSection) {
            observer.observe(heroSection);
        }

        // Parallax Effect
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.floating-element');

            parallaxElements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.1);
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });

        // Course Card Hover Effects
        document.querySelectorAll('.course-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Search Functionality (if search box exists)
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const query = this.value.trim();
                    if (query) {
                        window.location.href = `search.php?q=${encodeURIComponent(query)}`;
                    }
                }
            });
        }

        // Newsletter Subscription (if form exists)
        const newsletterForm = document.querySelector('.newsletter-form');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const email = this.querySelector('input[type="email"]').value;

                // Here you would typically send the email to your backend
                alert('شكراً لاشتراكك في النشرة الإخبارية!');
                this.reset();
            });
        }
    </script>

    <style>
        /* Additional Styles */
        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--primary-gradient);
            color: white;
            border-radius: 50%;
            text-decoration: none;
            transition: var(--transition);
        }

        .social-link:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
            color: white;
        }

        .footer-links li {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: var(--gray-400);
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }

        .contact-item {
            display: flex;
            align-items: center;
            color: var(--gray-400);
        }

        .back-to-top {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 50px;
            height: 50px;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            z-index: 1000;
        }

        .back-to-top.show {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .category-card:hover {
            transform: translateY(-5px);
        }

        .instructor-card:hover {
            transform: translateY(-5px);
        }

        .testimonial-card {
            border-left: 4px solid var(--primary-color);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--gray-600);
        }

        /* Mobile Responsive Improvements */
        @media (max-width: 768px) {
            .hero-stats {
                justify-content: center;
                gap: 1.5rem;
            }

            .hero-stat-number {
                font-size: 2rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .back-to-top {
                bottom: 20px;
                left: 20px;
                width: 45px;
                height: 45px;
            }
        }
    </style>
</body>
</html>