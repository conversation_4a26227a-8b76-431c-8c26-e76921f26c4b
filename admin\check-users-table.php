<?php
require_once 'includes/simple_db.php';

try {
    echo "<h2>فحص هيكل جدول users</h2>";
    
    // عرض هيكل الجدول
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    echo "<h3>الأعمدة الموجودة:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // إضافة الأعمدة المفقودة
    $missing_columns = [
        'education_level' => "VARCHAR(50) DEFAULT NULL",
        'university' => "VARCHAR(255) DEFAULT NULL",
        'major' => "VARCHAR(255) DEFAULT NULL",
        'certifications' => "TEXT DEFAULT NULL",
        'skills' => "TEXT DEFAULT NULL",
        'languages' => "VARCHAR(255) DEFAULT NULL",
        'hourly_rate' => "DECIMAL(10,2) DEFAULT NULL",
        'availability' => "VARCHAR(50) DEFAULT 'flexible'",
        'teaching_style' => "TEXT DEFAULT NULL",
        'preferred_subjects' => "TEXT DEFAULT NULL"
    ];
    
    echo "<h3>إضافة الأعمدة المفقودة:</h3>";
    
    foreach ($missing_columns as $column_name => $definition) {
        // التحقق من وجود العمود
        $check = $conn->query("SHOW COLUMNS FROM users LIKE '$column_name'");
        if ($check->rowCount() == 0) {
            try {
                $sql = "ALTER TABLE users ADD COLUMN $column_name $definition";
                $conn->exec($sql);
                echo "<p style='color: green;'>✓ تم إضافة العمود: $column_name</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ خطأ في إضافة العمود $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>✓ العمود موجود بالفعل: $column_name</p>";
        }
    }
    
    echo "<h3 style='color: green;'>تم الانتهاء من فحص وإصلاح جدول users</h3>";
    echo "<p><a href='add-instructor.php'>اختبار إضافة مدرب</a></p>";
    echo "<p><a href='add-course.php'>اختبار إضافة كورس</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>خطأ: " . $e->getMessage() . "</h3>";
}
?>
