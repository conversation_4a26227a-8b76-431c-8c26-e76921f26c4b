/**
 * Service Worker المحسن للمنصة التعليمية
 * Enhanced Learning Platform Service Worker
 * ========================================
 */

const CACHE_VERSION = '2.0.0';
const STATIC_CACHE = `static-v${CACHE_VERSION}`;
const DYNAMIC_CACHE = `dynamic-v${CACHE_VERSION}`;
const IMAGE_CACHE = `images-v${CACHE_VERSION}`;
const API_CACHE = `api-v${CACHE_VERSION}`;

// الملفات الأساسية للتخزين المؤقت
const STATIC_ASSETS = [
    '/',
    '/index.php',
    '/index_enhanced.php',
    '/courses.php',
    '/courses_enhanced.php',
    '/search.php',
    '/login.php',
    '/register.php',
    '/offline.html',
    '/manifest.json',
    
    // ملفات CSS
    '/assets/css/main.css',
    '/assets/css/responsive.css',
    
    // ملفات JavaScript
    '/assets/js/main.js',
    '/assets/js/main_enhanced.js',
    '/assets/js/responsive.js',
    
    // الصور الأساسية
    '/assets/images/logo.png',
    '/assets/images/icons/icon-192x192.png',
    '/assets/images/icons/icon-512x512.png',
    '/assets/images/favicon.ico',
    
    // مكتبات خارجية
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap',
    'https://unpkg.com/aos@2.3.1/dist/aos.css',
    'https://unpkg.com/aos@2.3.1/dist/aos.js'
];

// أنماط URL للتخزين المؤقت
const CACHE_PATTERNS = {
    images: /\.(jpg|jpeg|png|gif|webp|svg|ico)$/i,
    fonts: /\.(woff|woff2|ttf|eot)$/i,
    api: /\/api\//,
    uploads: /\/uploads\//,
    videos: /\.(mp4|avi|mov|wmv|flv)$/i
};

// إعدادات التخزين المؤقت
const CACHE_CONFIG = {
    maxAge: {
        static: 30 * 24 * 60 * 60 * 1000, // 30 يوم
        dynamic: 7 * 24 * 60 * 60 * 1000, // 7 أيام
        images: 30 * 24 * 60 * 60 * 1000, // 30 يوم
        api: 5 * 60 * 1000 // 5 دقائق
    },
    maxEntries: {
        dynamic: 50,
        images: 100,
        api: 20
    }
};

/**
 * تثبيت Service Worker
 */
self.addEventListener('install', event => {
    console.log('[SW] Installing Enhanced Service Worker v' + CACHE_VERSION);
    
    event.waitUntil(
        Promise.all([
            // تخزين الملفات الأساسية
            caches.open(STATIC_CACHE).then(cache => {
                console.log('[SW] Caching static assets...');
                return cache.addAll(STATIC_ASSETS.map(url => {
                    return new Request(url, { cache: 'reload' });
                }));
            }),
            
            // إنشاء التخزين المؤقت الأخرى
            caches.open(DYNAMIC_CACHE),
            caches.open(IMAGE_CACHE),
            caches.open(API_CACHE)
        ]).then(() => {
            console.log('[SW] Installation complete');
            return self.skipWaiting();
        }).catch(error => {
            console.error('[SW] Installation failed:', error);
        })
    );
});

/**
 * تفعيل Service Worker
 */
self.addEventListener('activate', event => {
    console.log('[SW] Activating Enhanced Service Worker v' + CACHE_VERSION);
    
    event.waitUntil(
        Promise.all([
            // حذف التخزين المؤقت القديم
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (!cacheName.includes(CACHE_VERSION)) {
                            console.log('[SW] Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            }),
            
            // تنظيف التخزين المؤقت
            cleanupCaches(),
            
            // السيطرة على جميع العملاء
            self.clients.claim()
        ]).then(() => {
            console.log('[SW] Activation complete');
            notifyClients('SW_UPDATED');
        }).catch(error => {
            console.error('[SW] Activation failed:', error);
        })
    );
});

/**
 * اعتراض الطلبات
 */
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);
    
    // تجاهل الطلبات غير HTTP/HTTPS
    if (!request.url.startsWith('http')) {
        return;
    }
    
    // تجاهل طلبات غير GET للAPI
    if (request.method !== 'GET' && CACHE_PATTERNS.api.test(url.pathname)) {
        event.respondWith(handleAPIRequest(request));
        return;
    }
    
    // تجاهل الطلبات غير GET الأخرى
    if (request.method !== 'GET') {
        return;
    }
    
    // اختيار استراتيجية التخزين المؤقت
    if (isStaticAsset(request)) {
        event.respondWith(cacheFirst(request, STATIC_CACHE));
    } else if (isImageRequest(request)) {
        event.respondWith(cacheFirst(request, IMAGE_CACHE));
    } else if (isAPIRequest(request)) {
        event.respondWith(networkFirst(request, API_CACHE));
    } else {
        event.respondWith(staleWhileRevalidate(request, DYNAMIC_CACHE));
    }
});

/**
 * معالجة طلبات API غير GET
 */
async function handleAPIRequest(request) {
    try {
        return await fetch(request);
    } catch (error) {
        return new Response(JSON.stringify({
            error: 'Network unavailable',
            offline: true
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

/**
 * استراتيجية Cache First
 */
async function cacheFirst(request, cacheName) {
    try {
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            // تحديث في الخلفية إذا كان قديماً
            if (isExpired(cachedResponse)) {
                updateInBackground(request, cache);
            }
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('[SW] Cache First failed:', error);
        return getOfflineResponse(request);
    }
}

/**
 * استراتيجية Network First
 */
async function networkFirst(request, cacheName) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(cacheName);
            cache.put(request, networkResponse.clone());
            
            // تنظيف التخزين المؤقت
            cleanupCache(cache, CACHE_CONFIG.maxEntries.api);
        }
        
        return networkResponse;
    } catch (error) {
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        return new Response(JSON.stringify({
            error: 'Network unavailable',
            offline: true,
            cached: false
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

/**
 * استراتيجية Stale While Revalidate
 */
async function staleWhileRevalidate(request, cacheName) {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
            cleanupCache(cache, CACHE_CONFIG.maxEntries.dynamic);
        }
        return networkResponse;
    }).catch(() => cachedResponse);
    
    return cachedResponse || fetchPromise;
}

/**
 * تحديث في الخلفية
 */
function updateInBackground(request, cache) {
    fetch(request).then(response => {
        if (response.ok) {
            cache.put(request, response);
        }
    }).catch(error => {
        console.log('[SW] Background update failed:', error);
    });
}

/**
 * التحقق من انتهاء صلاحية الاستجابة
 */
function isExpired(response) {
    const dateHeader = response.headers.get('date');
    if (!dateHeader) return false;
    
    const responseDate = new Date(dateHeader);
    const now = new Date();
    const age = now.getTime() - responseDate.getTime();
    
    return age > CACHE_CONFIG.maxAge.static;
}

/**
 * تنظيف التخزين المؤقت
 */
async function cleanupCache(cache, maxEntries) {
    const keys = await cache.keys();
    if (keys.length > maxEntries) {
        const keysToDelete = keys.slice(0, keys.length - maxEntries);
        await Promise.all(keysToDelete.map(key => cache.delete(key)));
    }
}

/**
 * تنظيف جميع التخزين المؤقت
 */
async function cleanupCaches() {
    const cacheNames = [DYNAMIC_CACHE, IMAGE_CACHE, API_CACHE];
    
    for (const cacheName of cacheNames) {
        try {
            const cache = await caches.open(cacheName);
            const maxEntries = CACHE_CONFIG.maxEntries[cacheName.split('-')[0]] || 50;
            await cleanupCache(cache, maxEntries);
        } catch (error) {
            console.error('[SW] Cache cleanup failed:', error);
        }
    }
}

/**
 * التحقق من نوع الطلب
 */
function isStaticAsset(request) {
    const url = new URL(request.url);
    return STATIC_ASSETS.includes(url.pathname) || 
           url.origin !== location.origin ||
           CACHE_PATTERNS.fonts.test(url.pathname);
}

function isImageRequest(request) {
    const url = new URL(request.url);
    return CACHE_PATTERNS.images.test(url.pathname) || 
           CACHE_PATTERNS.uploads.test(url.pathname);
}

function isAPIRequest(request) {
    const url = new URL(request.url);
    return CACHE_PATTERNS.api.test(url.pathname);
}

/**
 * الحصول على استجابة offline
 */
function getOfflineResponse(request) {
    const url = new URL(request.url);
    
    if (isImageRequest(request)) {
        return getPlaceholderImage();
    }
    
    if (request.headers.get('accept').includes('text/html')) {
        return caches.match('/offline.html') || 
               new Response('الصفحة غير متاحة في وضع عدم الاتصال', {
                   status: 503,
                   headers: { 'Content-Type': 'text/html; charset=utf-8' }
               });
    }
    
    return new Response('غير متاح في وضع عدم الاتصال', {
        status: 503,
        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
    });
}

/**
 * الحصول على صورة بديلة
 */
function getPlaceholderImage() {
    return new Response(
        '<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#f0f0f0"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999">صورة غير متاحة</text></svg>',
        { headers: { 'Content-Type': 'image/svg+xml' } }
    );
}

/**
 * إشعار العملاء
 */
function notifyClients(type, data = {}) {
    self.clients.matchAll().then(clients => {
        clients.forEach(client => {
            client.postMessage({
                type,
                version: CACHE_VERSION,
                ...data
            });
        });
    });
}

/**
 * معالجة الرسائل من العملاء
 */
self.addEventListener('message', event => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'CACHE_URLS':
            if (data && data.urls) {
                cacheUrls(data.urls);
            }
            break;
            
        case 'CLEAR_CACHE':
            clearAllCaches();
            break;
            
        case 'GET_CACHE_INFO':
            getCacheInfo().then(info => {
                event.ports[0].postMessage(info);
            });
            break;
    }
});

/**
 * تخزين URLs إضافية
 */
async function cacheUrls(urls) {
    try {
        const cache = await caches.open(DYNAMIC_CACHE);
        await cache.addAll(urls);
        console.log('[SW] Additional URLs cached');
    } catch (error) {
        console.error('[SW] Failed to cache additional URLs:', error);
    }
}

/**
 * مسح جميع التخزين المؤقت
 */
async function clearAllCaches() {
    try {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
        console.log('[SW] All caches cleared');
        notifyClients('CACHE_CLEARED');
    } catch (error) {
        console.error('[SW] Failed to clear caches:', error);
    }
}

/**
 * الحصول على معلومات التخزين المؤقت
 */
async function getCacheInfo() {
    try {
        const cacheNames = await caches.keys();
        const info = {};
        
        for (const name of cacheNames) {
            const cache = await caches.open(name);
            const keys = await cache.keys();
            info[name] = {
                count: keys.length,
                urls: keys.map(req => req.url)
            };
        }
        
        return info;
    } catch (error) {
        console.error('[SW] Failed to get cache info:', error);
        return {};
    }
}

/**
 * معالجة إشعارات Push
 */
self.addEventListener('push', event => {
    if (!event.data) return;
    
    try {
        const data = event.data.json();
        const options = {
            body: data.body || 'إشعار جديد',
            icon: data.icon || '/assets/images/icons/icon-192x192.png',
            badge: data.badge || '/assets/images/icons/badge-72x72.png',
            tag: data.tag || 'general',
            data: data.data || {},
            actions: data.actions || [],
            requireInteraction: data.requireInteraction || false
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title || 'منصة التعلم', options)
        );
    } catch (error) {
        console.error('[SW] Push notification error:', error);
    }
});

/**
 * معالجة النقر على الإشعارات
 */
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    const data = event.notification.data;
    const url = data.url || '/';
    
    event.waitUntil(
        clients.matchAll({ type: 'window' }).then(clientList => {
            // البحث عن نافذة مفتوحة
            for (const client of clientList) {
                if (client.url === url && 'focus' in client) {
                    return client.focus();
                }
            }
            
            // فتح نافذة جديدة
            if (clients.openWindow) {
                return clients.openWindow(url);
            }
        })
    );
});

/**
 * معالجة إغلاق الإشعارات
 */
self.addEventListener('notificationclose', event => {
    console.log('[SW] Notification closed:', event.notification.tag);
});

console.log('[SW] Enhanced Service Worker loaded v' + CACHE_VERSION);
