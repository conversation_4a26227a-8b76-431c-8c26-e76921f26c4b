/**
 * JavaScript محسن للتجاوب والتفاعل
 * Enhanced Responsive JavaScript
 * ================================
 */

class ResponsiveManager {
    constructor() {
        this.init();
        this.bindEvents();
        this.detectDevice();
        this.initializeComponents();
    }

    init() {
        // تهيئة المتغيرات
        this.isMobile = window.innerWidth <= 768;
        this.isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
        this.isDesktop = window.innerWidth > 1024;
        this.touchDevice = 'ontouchstart' in window;
        
        // إضافة classes للـ body حسب نوع الجهاز
        document.body.classList.add(
            this.isMobile ? 'is-mobile' : 
            this.isTablet ? 'is-tablet' : 'is-desktop'
        );
        
        if (this.touchDevice) {
            document.body.classList.add('is-touch');
        }
    }

    detectDevice() {
        // كشف نوع الجهاز والمتصفح
        const userAgent = navigator.userAgent.toLowerCase();
        
        const devices = {
            android: /android/.test(userAgent),
            ios: /iphone|ipad|ipod/.test(userAgent),
            windows: /windows/.test(userAgent),
            mac: /macintosh/.test(userAgent),
            linux: /linux/.test(userAgent)
        };
        
        const browsers = {
            chrome: /chrome/.test(userAgent),
            firefox: /firefox/.test(userAgent),
            safari: /safari/.test(userAgent) && !/chrome/.test(userAgent),
            edge: /edge/.test(userAgent),
            ie: /msie|trident/.test(userAgent)
        };
        
        // إضافة classes للـ body
        Object.keys(devices).forEach(device => {
            if (devices[device]) {
                document.body.classList.add(`device-${device}`);
            }
        });
        
        Object.keys(browsers).forEach(browser => {
            if (browsers[browser]) {
                document.body.classList.add(`browser-${browser}`);
            }
        });
    }

    bindEvents() {
        // مراقبة تغيير حجم الشاشة
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // مراقبة التوجه (orientation)
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });

        // مراقبة التمرير
        window.addEventListener('scroll', this.throttle(() => {
            this.handleScroll();
        }, 16));

        // إدارة القائمة الجانبية
        this.initSidebar();
        
        // إدارة النماذج
        this.initForms();
        
        // إدارة الجداول
        this.initTables();
        
        // إدارة الأزرار
        this.initButtons();
    }

    handleResize() {
        const oldIsMobile = this.isMobile;
        const oldIsTablet = this.isTablet;
        
        this.isMobile = window.innerWidth <= 768;
        this.isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
        this.isDesktop = window.innerWidth > 1024;
        
        // تحديث classes
        document.body.classList.remove('is-mobile', 'is-tablet', 'is-desktop');
        document.body.classList.add(
            this.isMobile ? 'is-mobile' : 
            this.isTablet ? 'is-tablet' : 'is-desktop'
        );
        
        // إعادة تهيئة المكونات إذا تغير نوع الجهاز
        if (oldIsMobile !== this.isMobile || oldIsTablet !== this.isTablet) {
            this.reinitializeComponents();
        }
        
        // إرسال حدث مخصص
        window.dispatchEvent(new CustomEvent('responsiveResize', {
            detail: {
                isMobile: this.isMobile,
                isTablet: this.isTablet,
                isDesktop: this.isDesktop,
                width: window.innerWidth,
                height: window.innerHeight
            }
        }));
    }

    handleOrientationChange() {
        // إعادة حساب الأبعاد بعد تغيير التوجه
        this.handleResize();
        
        // إصلاح مشاكل iOS viewport
        if (this.isIOS()) {
            document.body.style.height = window.innerHeight + 'px';
            setTimeout(() => {
                document.body.style.height = '';
            }, 500);
        }
    }

    handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // إضافة/إزالة class للهيدر عند التمرير
        const header = document.querySelector('.header-responsive');
        if (header) {
            if (scrollTop > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        }
        
        // إخفاء/إظهار عناصر عند التمرير في الجوال
        if (this.isMobile) {
            const hideOnScroll = document.querySelectorAll('.hide-on-scroll-mobile');
            hideOnScroll.forEach(element => {
                if (scrollTop > 100) {
                    element.style.transform = 'translateY(-100%)';
                } else {
                    element.style.transform = 'translateY(0)';
                }
            });
        }
    }

    initSidebar() {
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        const sidebar = document.querySelector('.sidebar-responsive');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSidebar();
            });
        }
        
        if (overlay) {
            overlay.addEventListener('click', () => {
                this.closeSidebar();
            });
        }
        
        // إغلاق القائمة بالضغط على Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && sidebar && sidebar.classList.contains('active')) {
                this.closeSidebar();
            }
        });
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar-responsive');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebar) {
            sidebar.classList.toggle('active');
            if (overlay) {
                overlay.classList.toggle('active');
            }
            
            // منع التمرير عند فتح القائمة في الجوال
            if (this.isMobile) {
                document.body.style.overflow = sidebar.classList.contains('active') ? 'hidden' : '';
            }
        }
    }

    closeSidebar() {
        const sidebar = document.querySelector('.sidebar-responsive');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebar) {
            sidebar.classList.remove('active');
            if (overlay) {
                overlay.classList.remove('active');
            }
            
            // استعادة التمرير
            document.body.style.overflow = '';
        }
    }

    initForms() {
        // تحسين النماذج للجوال
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            // إضافة تحقق من صحة البيانات
            this.addFormValidation(form);
            
            // تحسين حقول الإدخال للجوال
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                this.enhanceInput(input);
            });
        });
    }

    addFormValidation(form) {
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        
        form.addEventListener('submit', (e) => {
            if (!this.validateForm(form)) {
                e.preventDefault();
                return false;
            }
            
            // إضافة حالة التحميل للزر
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            }
        });
    }

    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });
        
        return isValid;
    }

    showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid');
        
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    enhanceInput(input) {
        // إضافة تأثيرات بصرية
        input.addEventListener('focus', () => {
            input.parentNode.classList.add('focused');
        });
        
        input.addEventListener('blur', () => {
            input.parentNode.classList.remove('focused');
            if (input.value) {
                input.parentNode.classList.add('has-value');
            } else {
                input.parentNode.classList.remove('has-value');
            }
        });
        
        // تحسين لوحة المفاتيح للجوال
        if (this.isMobile) {
            if (input.type === 'email') {
                input.setAttribute('inputmode', 'email');
            } else if (input.type === 'tel') {
                input.setAttribute('inputmode', 'tel');
            } else if (input.type === 'number') {
                input.setAttribute('inputmode', 'numeric');
            }
        }
    }

    initTables() {
        const tables = document.querySelectorAll('.table-mobile');
        
        tables.forEach(table => {
            if (this.isMobile) {
                this.convertTableToCards(table);
            }
        });
    }

    convertTableToCards(table) {
        const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent);
        const rows = table.querySelectorAll('tbody tr');
        
        const cardsContainer = document.createElement('div');
        cardsContainer.className = 'table-mobile-cards';
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const card = document.createElement('div');
            card.className = 'card-responsive';
            
            let cardContent = '<div class="card-body-responsive">';
            
            cells.forEach((cell, index) => {
                if (headers[index] && cell.textContent.trim()) {
                    cardContent += `
                        <div class="table-card-row">
                            <strong>${headers[index]}:</strong>
                            <span>${cell.innerHTML}</span>
                        </div>
                    `;
                }
            });
            
            cardContent += '</div>';
            card.innerHTML = cardContent;
            cardsContainer.appendChild(card);
        });
        
        table.parentNode.appendChild(cardsContainer);
        table.style.display = 'none';
    }

    initButtons() {
        // إضافة تأثيرات للأزرار
        const buttons = document.querySelectorAll('.btn-responsive');
        
        buttons.forEach(button => {
            // تأثير الضغط
            button.addEventListener('touchstart', () => {
                button.classList.add('pressed');
            });
            
            button.addEventListener('touchend', () => {
                setTimeout(() => {
                    button.classList.remove('pressed');
                }, 150);
            });
            
            // منع الضغط المزدوج
            let isProcessing = false;
            button.addEventListener('click', (e) => {
                if (isProcessing) {
                    e.preventDefault();
                    return false;
                }
                
                isProcessing = true;
                setTimeout(() => {
                    isProcessing = false;
                }, 1000);
            });
        });
    }

    initializeComponents() {
        // تهيئة المكونات التفاعلية
        this.initTooltips();
        this.initModals();
        this.initDropdowns();
        this.initTabs();
        this.initAccordions();
    }

    reinitializeComponents() {
        // إعادة تهيئة المكونات عند تغيير حجم الشاشة
        this.initTables();
        this.initializeComponents();
    }

    initTooltips() {
        // تهيئة التلميحات
        const tooltips = document.querySelectorAll('[data-tooltip]');
        
        tooltips.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                if (!this.isMobile) {
                    this.showTooltip(e.target);
                }
            });
            
            element.addEventListener('mouseleave', (e) => {
                this.hideTooltip(e.target);
            });
            
            // للأجهزة اللمسية
            element.addEventListener('touchstart', (e) => {
                if (this.isMobile) {
                    this.toggleTooltip(e.target);
                }
            });
        });
    }

    showTooltip(element) {
        const text = element.getAttribute('data-tooltip');
        if (!text) return;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip-responsive';
        tooltip.textContent = text;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        
        element._tooltip = tooltip;
    }

    hideTooltip(element) {
        if (element._tooltip) {
            element._tooltip.remove();
            element._tooltip = null;
        }
    }

    toggleTooltip(element) {
        if (element._tooltip) {
            this.hideTooltip(element);
        } else {
            this.showTooltip(element);
        }
    }

    // دوال مساعدة
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    isAndroid() {
        return /Android/.test(navigator.userAgent);
    }

    // API عامة
    static getInstance() {
        if (!ResponsiveManager.instance) {
            ResponsiveManager.instance = new ResponsiveManager();
        }
        return ResponsiveManager.instance;
    }
}

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.responsiveManager = ResponsiveManager.getInstance();
});

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ResponsiveManager;
}
