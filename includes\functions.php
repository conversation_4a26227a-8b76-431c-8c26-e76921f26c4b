<?php
// سيتم تضمين activity_logger.php في الملفات التي تحتاجه فقط

function redirect($url) {
    echo "<script>window.location.href = '$url';</script>";
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

function isInstructor() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'instructor';
}

function isStudent() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'student';
}

// دالة تسجيل أنشطة المستخدمين - تم نقلها إلى simple_db.php لتجنب التضارب

// دالة تسجيل محاولات تسجيل الدخول
function logLoginAttempt($email, $success, $ip_address = null) {
    global $conn;

    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255),
                success BOOLEAN,
                ip_address VARCHAR(45),
                attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_ip (ip_address),
                INDEX idx_attempt_time (attempt_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        $stmt = $conn->prepare("
            INSERT INTO login_attempts (email, success, ip_address)
            VALUES (?, ?, ?)
        ");

        $ip = $ip_address ?: ($_SERVER['REMOTE_ADDR'] ?? '');
        $stmt->execute([$email, $success, $ip]);

    } catch (Exception $e) {
        error_log("Error logging login attempt: " . $e->getMessage());
    }
}

// دالة تسجيل تسجيل الخروج
function logLogout($user_id) {
    logUserActivity($user_id, 'تسجيل خروج', 'تم تسجيل الخروج من النظام');
}

function getUserStatistics($userId) {
    global $conn;
    
    try {
        // Get total logins
        $stmt = $conn->prepare(
            "SELECT COUNT(*) FROM activity_logs 
             WHERE user_id = ? AND action = 'تسجيل دخول ناجح'"
        );
        $stmt->execute([$userId]);
        $totalLogins = $stmt->fetchColumn();
        
        // Get last login
        $stmt = $conn->prepare("SELECT last_login FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $lastLogin = $stmt->fetchColumn();
        
        // Get total sessions attended
        $stmt = $conn->prepare(
            "SELECT COUNT(*) FROM sessions s 
             JOIN session_attendees sa ON s.id = sa.session_id 
             WHERE sa.user_id = ? AND s.start_time <= NOW()"
        );
        $stmt->execute([$userId]);
        $totalSessions = $stmt->fetchColumn();
        
        return [
            'total_logins' => $totalLogins,
            'last_login' => $lastLogin,
            'total_sessions' => $totalSessions
        ];
    } catch (PDOException $e) {
        error_log("Error getting user statistics: " . $e->getMessage());
        return false;
    }
}

function sanitize($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function prepareAndExecute($query, $params = [], $conn = null) {
    if (!$conn) {
        global $conn;
    }
    if (!$conn instanceof PDO) {
        error_log("Database connection error: Invalid connection object");
        throw new Exception('خطأ في الاتصال بقاعدة البيانات');
    }
    try {
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            $error = $conn->errorInfo();
            error_log("Query preparation error: " . implode(", ", $error));
            throw new Exception('خطأ في تحضير الاستعلام: ' . $error[2]);
        }
        if (!$stmt->execute($params)) {
            $error = $stmt->errorInfo();
            error_log("Query execution error: " . implode(", ", $error));
            throw new Exception('خطأ في تنفيذ الاستعلام: ' . $error[2]);
        }
        return $stmt;
    } catch (PDOException $e) {
        error_log("Database error in query: {$query}");
        error_log("Parameters: " . print_r($params, true));
        error_log("Error message: " . $e->getMessage());
        throw new Exception('حدث خطأ في قاعدة البيانات: ' . $e->getMessage());
    }
}

function sendEmail($to, $subject, $message) {
    // Just log the email for now
    error_log("Email would be sent to: $to\nSubject: $subject\nMessage: $message");
    return true;
}

function generateWhatsAppLink($phone, $message) {
    $cleanPhone = preg_replace('/[^0-9]/', '', $phone);
    return "https://wa.me/" . $cleanPhone . "?text=" . urlencode($message);
}

function checkLoginAttempts($email, $ip) {
    global $conn;

    try {
        // إنشاء جدول blocked_ips إذا لم يكن موجود
        $conn->exec("
            CREATE TABLE IF NOT EXISTS blocked_ips (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ip VARCHAR(45) NOT NULL,
                blocked_until TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_ip (ip),
                INDEX idx_blocked_until (blocked_until)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // Clean up old attempts (older than 15 minutes)
        $stmt = $conn->prepare("DELETE FROM login_attempts WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 15 MINUTE)");
        $stmt->execute();

        // Count recent failed attempts
        $stmt = $conn->prepare(
            "SELECT COUNT(*) FROM login_attempts
             WHERE (email = ? OR ip_address = ?)
             AND success = FALSE
             AND attempt_time > DATE_SUB(NOW(), INTERVAL 15 MINUTE)"
        );
        $stmt->execute([$email, $ip]);
        $attempts = $stmt->fetchColumn();

        // Check if IP is blocked
        $stmt = $conn->prepare("SELECT blocked_until FROM blocked_ips WHERE ip = ? AND blocked_until > NOW()");
        $stmt->execute([$ip]);
        $blockedUntil = $stmt->fetchColumn();

        if ($blockedUntil) {
            throw new Exception('تم حظر عنوان IP الخاص بك مؤقتًا. يرجى المحاولة مرة أخرى لاحقًا.');
        }

        if ($attempts >= 5) {
            // Block the IP for 15 minutes
            $stmt = $conn->prepare(
                "INSERT INTO blocked_ips (ip, blocked_until)
                 VALUES (?, DATE_ADD(NOW(), INTERVAL 15 MINUTE))
                 ON DUPLICATE KEY UPDATE blocked_until = VALUES(blocked_until)"
            );
            $stmt->execute([$ip]);

            throw new Exception('تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى المحاولة مرة أخرى بعد 15 دقيقة.');
        }

        return true;
    } catch (PDOException $e) {
        error_log("Error checking login attempts: " . $e->getMessage());
        throw new Exception('حدث خطأ أثناء التحقق من محاولات تسجيل الدخول');
    }
}

// تم نقل دالة recordLoginAttempt إلى activity_logger.php

// دالة تسجيل الأنشطة - تم نقلها إلى activity-logs.php لتجنب التضارب

// دالة إرسال إشعار
function sendNotification($user_id, $title, $message, $type = 'info', $category = 'system', $action_url = null) {
    global $conn;

    try {
        // التحقق من وجود جدول notifications
        $stmt = $conn->query("SHOW TABLES LIKE 'notifications'");
        if ($stmt->rowCount() == 0) {
            return false;
        }

        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, title, message, type, category, action_url)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$user_id, $title, $message, $type, $category, $action_url]);

        return true;

    } catch (PDOException $e) {
        error_log("Error sending notification: " . $e->getMessage());
        return false;
    }
}

// دالة جلب إعداد النظام (مع فحص التعريف المسبق)
if (!function_exists('getSystemSetting')) {
function getSystemSetting($key, $default = null) {
    global $conn;

    try {
        // التحقق من وجود جدول system_settings
        $stmt = $conn->query("SHOW TABLES LIKE 'system_settings'");
        if ($stmt->rowCount() == 0) {
            return $default;
        }

        $stmt = $conn->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $value = $stmt->fetchColumn();

        return $value !== false ? $value : $default;

    } catch (PDOException $e) {
        error_log("Error getting system setting: " . $e->getMessage());
        return $default;
    }
}
}

// دالة تحديث إعداد النظام (مع فحص التعريف المسبق)
if (!function_exists('updateSystemSetting')) {
function updateSystemSetting($key, $value, $user_id = null) {
    global $conn;

    try {
        // التحقق من وجود جدول system_settings
        $stmt = $conn->query("SHOW TABLES LIKE 'system_settings'");
        if ($stmt->rowCount() == 0) {
            return false;
        }

        $stmt = $conn->prepare("
            INSERT INTO system_settings (setting_key, setting_value, updated_by)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE
            setting_value = VALUES(setting_value),
            updated_by = VALUES(updated_by),
            updated_at = CURRENT_TIMESTAMP
        ");
        $stmt->execute([$key, $value, $user_id]);

        return true;

    } catch (PDOException $e) {
        error_log("Error updating system setting: " . $e->getMessage());
        return false;
    }
}

// دالة حساب العمولة
function calculateCommission($amount, $commission_rate = null) {
    if ($commission_rate === null) {
        $commission_rate = (float)getSystemSetting('commission_rate', 30);
    }

    $commission_amount = ($amount * $commission_rate) / 100;
    $instructor_amount = $amount - $commission_amount;

    return [
        'commission_rate' => $commission_rate,
        'commission_amount' => round($commission_amount, 2),
        'instructor_amount' => round($instructor_amount, 2)
    ];
}

// دالة إنشاء عمولة عند اكتمال الدفع
function createCommission($payment_id, $course_id, $instructor_id, $amount) {
    global $conn;

    try {
        // حساب العمولة
        $commission_data = calculateCommission($amount);

        // التحقق من عدم وجود عمولة مسبقة لنفس الدفعة
        $check_stmt = $conn->prepare("SELECT id FROM commissions WHERE payment_id = ?");
        $check_stmt->execute([$payment_id]);

        if ($check_stmt->rowCount() > 0) {
            return false; // العمولة موجودة مسبقاً
        }

        // إنشاء العمولة
        $stmt = $conn->prepare("
            INSERT INTO commissions (instructor_id, course_id, payment_id, course_price, commission_rate, commission_amount, instructor_amount, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')
        ");

        $result = $stmt->execute([
            $instructor_id,
            $course_id,
            $payment_id,
            $amount,
            $commission_data['commission_rate'],
            $commission_data['commission_amount'],
            $commission_data['instructor_amount']
        ]);

        return $result ? $conn->lastInsertId() : false;

    } catch (PDOException $e) {
        error_log("Error creating commission: " . $e->getMessage());
        return false;
    }
}

} // إغلاق فحص function_exists للدالة updateSystemSetting
?>
