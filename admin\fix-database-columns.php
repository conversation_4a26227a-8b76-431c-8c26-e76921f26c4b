<?php
require_once '../includes/config.php';

try {
    echo "<h2>إصلاح أعمدة قاعدة البيانات</h2>";
    
    // إضافة العمود المفقود attendance_status إلى جدول session_attendees
    $sql = "ALTER TABLE session_attendees ADD COLUMN IF NOT EXISTS attendance_status ENUM('present', 'absent', 'late') DEFAULT 'present'";
    $conn->exec($sql);
    echo "<p>✓ تم إضافة عمود attendance_status إلى جدول session_attendees</p>";
    
    // إضافة العمود المفقود ip_address إلى جدول user_activities إذا كان موجود
    $check_table = $conn->query("SHOW TABLES LIKE 'user_activities'");
    if ($check_table->rowCount() > 0) {
        $check_column = $conn->query("SHOW COLUMNS FROM user_activities LIKE 'ip_address'");
        if ($check_column->rowCount() == 0) {
            $sql = "ALTER TABLE user_activities ADD COLUMN ip_address VARCHAR(45) AFTER details";
            $conn->exec($sql);
            echo "<p>✓ تم إضافة عمود ip_address إلى جدول user_activities</p>";
        } else {
            echo "<p>✓ عمود ip_address موجود بالفعل في جدول user_activities</p>";
        }
    }
    
    // إنشاء جدول activity_logs إذا لم يكن موجود
    $sql = "CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(255) NOT NULL,
        description TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_created_at (created_at),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $conn->exec($sql);
    echo "<p>✓ تم إنشاء/التحقق من جدول activity_logs</p>";
    
    // إنشاء جدول enrollments إذا لم يكن موجود
    $sql = "CREATE TABLE IF NOT EXISTS enrollments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        course_id INT NOT NULL,
        enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        enrollment_status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
        payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        payment_amount DECIMAL(10,2) DEFAULT 0.00,
        UNIQUE KEY unique_enrollment (student_id, course_id),
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $conn->exec($sql);
    echo "<p>✓ تم إنشاء/التحقق من جدول enrollments</p>";
    
    // إنشاء جدول join_requests إذا لم يكن موجود
    $sql = "CREATE TABLE IF NOT EXISTS join_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        course_id INT,
        message TEXT,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        processed_by INT NULL,
        processed_at TIMESTAMP NULL,
        rejection_reason TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
        FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $conn->exec($sql);
    echo "<p>✓ تم إنشاء/التحقق من جدول join_requests</p>";
    
    echo "<h3 style='color: green;'>تم إصلاح جميع مشاكل قاعدة البيانات بنجاح!</h3>";
    echo "<p><a href='../admin/dashboard.php'>العودة إلى لوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>خطأ: " . $e->getMessage() . "</h3>";
}
?>
