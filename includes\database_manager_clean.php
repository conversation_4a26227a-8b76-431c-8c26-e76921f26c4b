<?php
/**
 * مدير قاعدة البيانات المحسن - نسخة نظيفة ومصححة
 * Clean and Fixed Database Manager
 * ===============================================
 */

// تحديد المسار الصحيح لملف قاعدة البيانات
$configPath = __DIR__ . '/../config/database.php';
if (!file_exists($configPath)) {
    // محاولة مسار بديل
    $configPath = dirname(__DIR__) . '/config/database.php';
}
if (!file_exists($configPath)) {
    // مسار آخر للتجربة
    $configPath = $_SERVER['DOCUMENT_ROOT'] . '/Zoom/config/database.php';
}

if (file_exists($configPath)) {
    require_once $configPath;
} else {
    // إعدادات افتراضية إذا لم يوجد الملف
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'zoom_learning_system');
    define('DB_USER', 'root');
    define('DB_PASS', '');
    define('DB_CHARSET', 'utf8mb4');
}

class DatabaseManagerClean {
    private $conn;
    private $lastInsertId;
    private $affectedRows;
    private $inTransaction = false;

    public function __construct() {
        try {
            // إنشاء اتصال PDO
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];

            $this->conn = new PDO($dsn, DB_USER, DB_PASS, $options);

        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("فشل في الاتصال بقاعدة البيانات");
        }
    }
    
    /**
     * إدارة المعاملات
     */
    public function beginTransaction() {
        if (!$this->inTransaction) {
            $result = $this->conn->beginTransaction();
            $this->inTransaction = true;
            return $result;
        }
        return true;
    }
    
    public function commit() {
        if ($this->inTransaction) {
            $result = $this->conn->commit();
            $this->inTransaction = false;
            return $result;
        }
        return true;
    }
    
    public function rollback() {
        if ($this->inTransaction) {
            $result = $this->conn->rollback();
            $this->inTransaction = false;
            return $result;
        }
        return true;
    }
    
    /**
     * عمليات CREATE (إنشاء)
     */
    public function create($table, $data, $returnId = true) {
        try {
            $columns = array_keys($data);
            $placeholders = ':' . implode(', :', $columns);
            
            $sql = "INSERT INTO {$table} (" . implode(', ', $columns) . ") VALUES ({$placeholders})";
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute($data);
            
            if ($result) {
                $this->lastInsertId = $this->conn->lastInsertId();
                $this->affectedRows = $stmt->rowCount();
                return $returnId ? $this->lastInsertId : true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            error_log("Create Error in {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * عمليات READ (قراءة)
     */
    public function find($table, $id, $columns = '*') {
        try {
            $sql = "SELECT {$columns} FROM {$table} WHERE id = ? LIMIT 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Find Error in {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    public function getAll($table, $columns = '*', $orderBy = 'id DESC', $limit = null) {
        try {
            $sql = "SELECT {$columns} FROM {$table}";
            
            if ($orderBy) {
                $sql .= " ORDER BY {$orderBy}";
            }
            
            if ($limit) {
                $sql .= " LIMIT {$limit}";
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("GetAll Error in {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    public function getWhere($table, $conditions, $columns = '*', $orderBy = null, $limit = null) {
        try {
            $whereClause = $this->buildWhereClause($conditions);
            $sql = "SELECT {$columns} FROM {$table} WHERE {$whereClause['sql']}";
            
            if ($orderBy) {
                $sql .= " ORDER BY {$orderBy}";
            }
            
            if ($limit) {
                $sql .= " LIMIT {$limit}";
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($whereClause['params']);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("GetWhere Error in {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    public function count($table, $conditions = []) {
        try {
            $sql = "SELECT COUNT(*) as total FROM {$table}";
            $params = [];
            
            if (!empty($conditions)) {
                $whereClause = $this->buildWhereClause($conditions);
                $sql .= " WHERE {$whereClause['sql']}";
                $params = $whereClause['params'];
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return (int)$result['total'];
            
        } catch (PDOException $e) {
            error_log("Count Error in {$table}: " . $e->getMessage());
            return 0;
        }
    }
    
    public function search($table, $searchTerm, $searchColumns, $conditions = [], $columns = '*', $limit = 50) {
        try {
            $searchConditions = [];
            $params = [];
            
            // بناء شروط البحث
            foreach ($searchColumns as $column) {
                $searchConditions[] = "{$column} LIKE ?";
                $params[] = "%{$searchTerm}%";
            }
            
            $sql = "SELECT {$columns} FROM {$table} WHERE (" . implode(' OR ', $searchConditions) . ")";
            
            // إضافة شروط إضافية
            if (!empty($conditions)) {
                $whereClause = $this->buildWhereClause($conditions);
                $sql .= " AND {$whereClause['sql']}";
                $params = array_merge($params, $whereClause['params']);
            }
            
            $sql .= " LIMIT {$limit}";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Search Error in {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * عمليات UPDATE (تحديث)
     */
    public function update($table, $id, $data) {
        try {
            $setParts = [];
            foreach (array_keys($data) as $column) {
                $setParts[] = "{$column} = :{$column}";
            }
            
            $sql = "UPDATE {$table} SET " . implode(', ', $setParts) . " WHERE id = :id";
            $data['id'] = $id;
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute($data);
            
            if ($result) {
                $this->affectedRows = $stmt->rowCount();
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            error_log("Update Error in {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * عمليات DELETE (حذف)
     */
    public function delete($table, $id) {
        try {
            $sql = "DELETE FROM {$table} WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $this->affectedRows = $stmt->rowCount();
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            error_log("Delete Error in {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * دوال مساعدة
     */
    private function buildWhereClause($conditions) {
        $whereParts = [];
        $params = [];
        
        foreach ($conditions as $key => $value) {
            if (is_array($value)) {
                // للقيم المتعددة (IN clause)
                $placeholders = str_repeat('?,', count($value) - 1) . '?';
                $whereParts[] = "{$key} IN ({$placeholders})";
                $params = array_merge($params, $value);
            } elseif (strpos($key, ' ') !== false) {
                // للعمليات المخصصة مثل 'age >' أو 'name LIKE'
                $whereParts[] = $key;
                $params[] = $value;
            } else {
                // للمقارنة العادية
                $whereParts[] = "{$key} = ?";
                $params[] = $value;
            }
        }
        
        return [
            'sql' => implode(' AND ', $whereParts),
            'params' => $params
        ];
    }
    
    public function getLastInsertId() {
        return $this->lastInsertId;
    }
    
    public function getAffectedRows() {
        return $this->affectedRows;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Custom Query Error: " . $e->getMessage());
            return false;
        }
    }
    
    public function exists($table, $conditions) {
        return $this->count($table, $conditions) > 0;
    }
}

// إنشاء مثيل عام
$dbClean = new DatabaseManagerClean();

// دوال مساعدة سريعة
function dbCreateClean($table, $data) {
    global $dbClean;
    return $dbClean->create($table, $data);
}

function dbFindClean($table, $id) {
    global $dbClean;
    return $dbClean->find($table, $id);
}

function dbUpdateClean($table, $id, $data) {
    global $dbClean;
    return $dbClean->update($table, $id, $data);
}

function dbDeleteClean($table, $id) {
    global $dbClean;
    return $dbClean->delete($table, $id);
}

function dbGetAllClean($table, $orderBy = 'id DESC') {
    global $dbClean;
    return $dbClean->getAll($table, '*', $orderBy);
}

function dbGetWhereClean($table, $conditions) {
    global $dbClean;
    return $dbClean->getWhere($table, $conditions);
}

function dbCountClean($table, $conditions = []) {
    global $dbClean;
    return $dbClean->count($table, $conditions);
}

function dbSearchClean($table, $term, $columns) {
    global $dbClean;
    return $dbClean->search($table, $term, $columns);
}
?>
