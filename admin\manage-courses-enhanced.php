<?php
session_start();
require_once '../includes/database_manager_clean.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// معالجة العمليات
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_status':
            $courseId = (int)$_POST['course_id'];
            $newStatus = $_POST['status'];
            
            if ($dbClean->update('courses', $courseId, ['status' => $newStatus])) {
                $message = 'تم تحديث حالة الكورس بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في تحديث حالة الكورس';
                $messageType = 'danger';
            }
            break;
            
        case 'toggle_featured':
            $courseId = (int)$_POST['course_id'];
            $isFeatured = (int)$_POST['is_featured'];
            
            if ($dbClean->update('courses', $courseId, ['is_featured' => $isFeatured])) {
                $message = $isFeatured ? 'تم إضافة الكورس للمميزة' : 'تم إزالة الكورس من المميزة';
                $messageType = 'success';
            } else {
                $message = 'فشل في تحديث حالة الكورس المميز';
                $messageType = 'danger';
            }
            break;
            
        case 'delete_course':
            $courseId = (int)$_POST['course_id'];
            
            if ($dbClean->delete('courses', $courseId)) {
                $message = 'تم حذف الكورس بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في حذف الكورس';
                $messageType = 'danger';
            }
            break;
    }
}

// جلب البيانات من قاعدة البيانات
$page = (int)($_GET['page'] ?? 1);
$limit = 15;
$offset = ($page - 1) * $limit;

// فلاتر البحث
$search = $_GET['search'] ?? '';
$categoryFilter = $_GET['category'] ?? '';
$statusFilter = $_GET['status'] ?? '';
$instructorFilter = $_GET['instructor'] ?? '';

// بناء شروط البحث
$conditions = [];
if (!empty($search)) {
    $courses = $dbClean->search('courses', $search, ['title', 'short_description', 'description'], $conditions, '*', $limit);
    $totalCourses = count($courses);
} else {
    if (!empty($categoryFilter)) {
        $conditions['category_id'] = $categoryFilter;
    }
    if (!empty($statusFilter)) {
        $conditions['status'] = $statusFilter;
    }
    if (!empty($instructorFilter)) {
        $conditions['instructor_id'] = $instructorFilter;
    }
    
    $courses = $dbClean->getWhere('courses', $conditions, '*', 'created_at DESC', $limit);
    $totalCourses = $dbClean->count('courses', $conditions);
}

// جلب بيانات إضافية لكل كورس
$coursesWithDetails = [];
foreach ($courses as $course) {
    // جلب بيانات المدرب
    $instructor = $dbClean->find('users', $course['instructor_id']);
    $course['instructor_name'] = $instructor['name'] ?? 'غير محدد';
    
    // جلب بيانات الفئة
    $category = $dbClean->find('categories', $course['category_id']);
    $course['category_name'] = $category['name'] ?? 'غير محدد';
    
    // عدد الطلاب المسجلين
    $course['students_count'] = $dbClean->count('course_enrollments', ['course_id' => $course['id']]);
    
    // عدد الدروس
    $course['lessons_count'] = $dbClean->count('course_lessons', ['course_id' => $course['id']]);
    
    // إجمالي الإيرادات
    $revenueQuery = "SELECT SUM(amount) as total_revenue FROM payments WHERE course_id = ? AND status = 'completed'";
    $revenueResult = $dbClean->query($revenueQuery, [$course['id']]);
    $course['total_revenue'] = $revenueResult[0]['total_revenue'] ?? 0;
    
    $coursesWithDetails[] = $course;
}

// إحصائيات الكورسات
$courseStats = [
    'total' => $dbClean->count('courses'),
    'published' => $dbClean->count('courses', ['status' => 'published']),
    'draft' => $dbClean->count('courses', ['status' => 'draft']),
    'pending' => $dbClean->count('courses', ['status' => 'pending']),
    'archived' => $dbClean->count('courses', ['status' => 'archived']),
    'featured' => $dbClean->count('courses', ['is_featured' => 1]),
    'free' => $dbClean->count('courses', ['is_free' => 1]),
    'paid' => $dbClean->count('courses', ['is_free' => 0])
];

// جلب الفئات للفلترة
$categories = $dbClean->getAll('categories', 'id, name', 'name ASC');

// جلب المدربين للفلترة
$instructors = $dbClean->getAll('users', 'id, name', 'name ASC', null);
$instructors = array_filter($instructors, function($user) {
    return $user['role'] === 'instructor';
});

$totalPages = ceil($totalCourses / $limit);
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الكورسات - لوحة التحكم</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card.primary { border-left-color: #667eea; }
        .stats-card.success { border-left-color: #28a745; }
        .stats-card.warning { border-left-color: #ffc107; }
        .stats-card.danger { border-left-color: #dc3545; }
        .stats-card.info { border-left-color: #17a2b8; }
        .stats-card.secondary { border-left-color: #6c757d; }
        .stats-card.dark { border-left-color: #343a40; }
        .stats-card.light { border-left-color: #f8f9fa; }
        
        .data-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .course-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .course-card:hover {
            transform: translateY(-5px);
        }
        
        .course-thumbnail {
            width: 100px;
            height: 70px;
            object-fit: cover;
            border-radius: 10px;
        }
        
        .btn-action {
            padding: 0.25rem 0.5rem;
            margin: 0 0.125rem;
            border-radius: 5px;
            font-size: 0.875rem;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 0.75rem 1rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .pagination {
            justify-content: center;
            margin-top: 2rem;
        }
        
        .page-link {
            border-radius: 10px;
            margin: 0 0.25rem;
            border: 2px solid #e2e8f0;
            color: #667eea;
        }
        
        .page-link:hover, .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-2">
                        <i class="fas fa-book me-3"></i>
                        إدارة الكورسات
                    </h1>
                    <p class="lead mb-0">إدارة شاملة لجميع الكورسات مع البيانات الحية والإحصائيات المفصلة</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="dashboard.php" class="btn btn-light btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- رسائل النظام -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الكورسات -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card primary">
                    <div class="text-center">
                        <h3 class="text-primary mb-1"><?php echo number_format($courseStats['total']); ?></h3>
                        <p class="text-muted mb-0">إجمالي الكورسات</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card success">
                    <div class="text-center">
                        <h3 class="text-success mb-1"><?php echo number_format($courseStats['published']); ?></h3>
                        <p class="text-muted mb-0">منشورة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card warning">
                    <div class="text-center">
                        <h3 class="text-warning mb-1"><?php echo number_format($courseStats['draft']); ?></h3>
                        <p class="text-muted mb-0">مسودة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card info">
                    <div class="text-center">
                        <h3 class="text-info mb-1"><?php echo number_format($courseStats['featured']); ?></h3>
                        <p class="text-muted mb-0">مميزة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات إضافية -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card secondary">
                    <div class="text-center">
                        <h3 class="text-secondary mb-1"><?php echo number_format($courseStats['pending']); ?></h3>
                        <p class="text-muted mb-0">معلقة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card dark">
                    <div class="text-center">
                        <h3 class="text-dark mb-1"><?php echo number_format($courseStats['archived']); ?></h3>
                        <p class="text-muted mb-0">مؤرشفة</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card success">
                    <div class="text-center">
                        <h3 class="text-success mb-1"><?php echo number_format($courseStats['free']); ?></h3>
                        <p class="text-muted mb-0">مجانية</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card danger">
                    <div class="text-center">
                        <h3 class="text-danger mb-1"><?php echo number_format($courseStats['paid']); ?></h3>
                        <p class="text-muted mb-0">مدفوعة</p>
                    </div>
                </div>
            </div>
        </div>
