<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$course_id = (int)($_GET['id'] ?? 0);

if (!$course_id) {
    header('Location: manage-courses-new.php');
    exit;
}

// جلب تفاصيل الكورس
$course = fetchOne("
    SELECT
        c.*,
        u.name as instructor_name,
        u.email as instructor_email,
        u.phone as instructor_phone,
        cat.name as category_name,
        COUNT(DISTINCT ce.id) as student_count,
        COUNT(DISTINCT s.id) as session_count,
        COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN ce.payment_amount END), 0) as total_revenue,
        COALESCE(AVG(cr.rating), 0) as avg_rating,
        COUNT(DISTINCT cr.id) as review_count
    FROM courses c
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN categories cat ON c.category_id = cat.id
    LEFT JOIN course_enrollments ce ON c.id = ce.course_id
    LEFT JOIN sessions s ON c.id = s.course_id
    LEFT JOIN course_reviews cr ON c.id = cr.course_id
    WHERE c.id = ?
    GROUP BY c.id
", [$course_id]);

if (!$course) {
    header('Location: manage-courses-new.php');
    exit;
}

// جلب الطلاب المسجلين
$students = fetchAll("
    SELECT
        u.id, u.name, u.email, u.phone,
        ce.enrollment_type, ce.payment_status, ce.payment_amount,
        ce.enrolled_at, ce.status as enrollment_status,
        ce.progress_percentage
    FROM course_enrollments ce
    JOIN users u ON ce.student_id = u.id
    WHERE ce.course_id = ?
    ORDER BY ce.enrolled_at DESC
", [$course_id]);

// جلب الجلسات
$sessions = fetchAll("
    SELECT
        s.*,
        COUNT(DISTINCT sa.id) as attendance_count
    FROM sessions s
    LEFT JOIN session_attendance sa ON s.id = sa.session_id AND sa.status = 'present'
    WHERE s.course_id = ?
    GROUP BY s.id
    ORDER BY s.session_date DESC, s.start_time DESC
", [$course_id]);

// جلب التقييمات
$reviews = fetchAll("
    SELECT
        cr.*,
        u.name as student_name
    FROM course_reviews cr
    JOIN users u ON cr.student_id = u.id
    WHERE cr.course_id = ?
    ORDER BY cr.created_at DESC
    LIMIT 10
", [$course_id]);

$pageTitle = 'تفاصيل الكورس: ' . $course['title'];
include 'includes/header.php';
?>

<div class="row">
    <!-- معلومات الكورس الأساسية -->
    <div class="col-lg-8">
        <div class="card-admin mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الكورس
                    </h5>
                    <div class="d-flex gap-2">
                        <a href="edit-course.php?id=<?php echo $course['id']; ?>" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <a href="manage-sessions.php?course_id=<?php echo $course['id']; ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-video me-1"></i>إدارة الجلسات
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <?php if (isset($course['image']) && $course['image']): ?>
                            <img src="../<?php echo htmlspecialchars($course['image']); ?>"
                                 alt="Course Image" class="img-fluid rounded mb-3">
                        <?php else: ?>
                            <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center mb-3"
                                 style="height: 200px;">
                                <i class="fas fa-book fa-4x"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-8">
                        <h4><?php echo htmlspecialchars($course['title']); ?></h4>
                        <p class="text-muted"><?php echo nl2br(htmlspecialchars($course['description'] ?? '')); ?></p>

                        <div class="row mt-3">
                            <div class="col-sm-6">
                                <strong>المدرب:</strong> <?php echo htmlspecialchars($course['instructor_name']); ?><br>
                                <strong>البريد:</strong> <?php echo htmlspecialchars($course['instructor_email']); ?><br>
                                <strong>التصنيف:</strong> <?php echo htmlspecialchars($course['category_name'] ?? 'غير محدد'); ?>
                            </div>
                            <div class="col-sm-6">
                                <strong>النوع:</strong>
                                <span class="badge bg-<?php echo $course['course_type'] === 'paid' ? 'warning' : 'success'; ?>">
                                    <?php echo $course['course_type'] === 'paid' ? 'مدفوع' : 'مجاني'; ?>
                                </span><br>
                                <?php if ($course['course_type'] === 'paid'): ?>
                                <strong>السعر:</strong> <?php echo number_format($course['price'], 2); ?> ر.س<br>
                                <?php endif; ?>
                                <strong>الحالة:</strong>
                                <span class="badge bg-<?php echo $course['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                    <?php echo $course['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجلسات -->
        <div class="card-admin mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-video me-2"></i>
                        الجلسات (<?php echo count($sessions); ?>)
                    </h5>
                    <a href="manage-sessions.php?course_id=<?php echo $course['id']; ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>إضافة جلسة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($sessions)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-video fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد جلسات</h6>
                        <a href="manage-sessions.php?course_id=<?php echo $course['id']; ?>" class="btn btn-primary">
                            إضافة جلسة جديدة
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>عنوان الجلسة</th>
                                    <th>التاريخ والوقت</th>
                                    <th>النوع</th>
                                    <th>الحضور</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($sessions as $session): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($session['title']); ?></td>
                                    <td>
                                        <?php echo date('Y-m-d', strtotime($session['session_date'])); ?><br>
                                        <small class="text-muted">
                                            <?php echo date('H:i', strtotime($session['start_time'])); ?> -
                                            <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $session['session_type']; ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $session['attendance_count']; ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $session['status'] === 'completed' ? 'success' : 'warning'; ?>">
                                            <?php echo $session['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="session-details.php?id=<?php echo $session['id']; ?>"
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- الطلاب المسجلين -->
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    الطلاب المسجلين (<?php echo count($students); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($students)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا يوجد طلاب مسجلين</h6>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover" id="studentsTable">
                            <thead>
                                <tr>
                                    <th>الطالب</th>
                                    <th>نوع التسجيل</th>
                                    <th>حالة الدفع</th>
                                    <th>المبلغ</th>
                                    <th>التقدم</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($students as $student): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($student['name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $student['enrollment_type'] === 'paid' ? 'warning' : 'success'; ?>">
                                            <?php echo $student['enrollment_type']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $student['payment_status'] === 'completed' ? 'success' : 'warning'; ?>">
                                            <?php echo $student['payment_status']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($student['payment_amount'], 2); ?> ر.س</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar"
                                                 style="width: <?php echo $student['progress_percentage']; ?>%">
                                                <?php echo number_format($student['progress_percentage'], 1); ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($student['enrolled_at'])); ?></td>
                                    <td>
                                        <a href="student-details.php?id=<?php echo $student['id']; ?>"
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- إحصائيات سريعة -->
        <div class="card-admin mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary mb-1"><?php echo $course['student_count']; ?></h4>
                        <small class="text-muted">الطلاب</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-1"><?php echo $course['session_count']; ?></h4>
                        <small class="text-muted">الجلسات</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-warning mb-1"><?php echo number_format($course['total_revenue'], 2); ?></h4>
                        <small class="text-muted">الإيرادات (ر.س)</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-info mb-1"><?php echo number_format($course['avg_rating'], 1); ?></h4>
                        <small class="text-muted">التقييم</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- التقييمات الأخيرة -->
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    التقييمات الأخيرة
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($reviews)): ?>
                    <div class="text-center py-3">
                        <i class="fas fa-star fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد تقييمات</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($reviews as $review): ?>
                    <div class="mb-3 pb-3 border-bottom">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($review['student_name']); ?></h6>
                                <div class="text-warning mb-1">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star<?php echo $i <= $review['rating'] ? '' : '-o'; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                                <p class="text-muted small mb-0"><?php echo htmlspecialchars($review['comment']); ?></p>
                            </div>
                            <small class="text-muted"><?php echo date('Y-m-d', strtotime($review['created_at'])); ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // تهيئة DataTables للطلاب
    if (!$.fn.DataTable.isDataTable('#studentsTable')) {
        $('#studentsTable').DataTable({
            pageLength: 10,
            responsive: true,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
            }
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
