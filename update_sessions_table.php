<?php
// تحديث جدول الجلسات لإضافة حقول الفيديو
require_once 'config/database.php';

echo "<h2>تحديث جدول الجلسات لدعم الفيديوهات</h2>";

try {
    // التحقق من وجود الأعمدة الجديدة
    $stmt = $conn->query("DESCRIBE sessions");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // إضافة عمود video_file_path إذا لم يكن موجود
    if (!in_array('video_file_path', $columns)) {
        $conn->exec("ALTER TABLE sessions ADD COLUMN video_file_path VARCHAR(500) DEFAULT NULL");
        echo "<p style='color: green;'>✅ تم إضافة عمود video_file_path</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود video_file_path موجود بالفعل</p>";
    }
    
    // إضافة عمود video_title إذا لم يكن موجود
    if (!in_array('video_title', $columns)) {
        $conn->exec("ALTER TABLE sessions ADD COLUMN video_title VARCHAR(255) DEFAULT NULL");
        echo "<p style='color: green;'>✅ تم إضافة عمود video_title</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود video_title موجود بالفعل</p>";
    }
    
    // إضافة عمود video_description إذا لم يكن موجود
    if (!in_array('video_description', $columns)) {
        $conn->exec("ALTER TABLE sessions ADD COLUMN video_description TEXT DEFAULT NULL");
        echo "<p style='color: green;'>✅ تم إضافة عمود video_description</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود video_description موجود بالفعل</p>";
    }
    
    // إضافة عمود video_size إذا لم يكن موجود
    if (!in_array('video_size', $columns)) {
        $conn->exec("ALTER TABLE sessions ADD COLUMN video_size BIGINT DEFAULT NULL");
        echo "<p style='color: green;'>✅ تم إضافة عمود video_size</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود video_size موجود بالفعل</p>";
    }
    
    // إضافة عمود video_duration إذا لم يكن موجود
    if (!in_array('video_duration', $columns)) {
        $conn->exec("ALTER TABLE sessions ADD COLUMN video_duration INT DEFAULT NULL");
        echo "<p style='color: green;'>✅ تم إضافة عمود video_duration</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود video_duration موجود بالفعل</p>";
    }
    
    // إضافة عمود has_video إذا لم يكن موجود
    if (!in_array('has_video', $columns)) {
        $conn->exec("ALTER TABLE sessions ADD COLUMN has_video TINYINT(1) DEFAULT 0");
        echo "<p style='color: green;'>✅ تم إضافة عمود has_video</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود has_video موجود بالفعل</p>";
    }
    
    // إضافة عمود video_uploaded_at إذا لم يكن موجود
    if (!in_array('video_uploaded_at', $columns)) {
        $conn->exec("ALTER TABLE sessions ADD COLUMN video_uploaded_at TIMESTAMP NULL");
        echo "<p style='color: green;'>✅ تم إضافة عمود video_uploaded_at</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود video_uploaded_at موجود بالفعل</p>";
    }
    
    echo "<h3 style='color: green;'>✅ تم تحديث جدول الجلسات بنجاح!</h3>";
    echo "<p><a href='instructor/session-videos.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب لصفحة فيديوهات الجلسات</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
