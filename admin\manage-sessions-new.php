<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة الجلسات';
$breadcrumbs = [
    ['title' => 'إدارة الكورسات', 'url' => 'manage-courses-new.php'],
    ['title' => 'إدارة الجلسات']
];

// معالجة إضافة جلسة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_session'])) {
    $course_id = (int)$_POST['course_id'];
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $session_date = $_POST['session_date'];
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];
    $session_type = $_POST['session_type'];
    $meeting_url = trim($_POST['meeting_url']);
    $status = $_POST['status'];
    
    // معالجة رفع الملفات
    $materials = [];
    if (isset($_FILES['materials']) && !empty($_FILES['materials']['name'][0])) {
        $upload_dir = '../uploads/sessions/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        for ($i = 0; $i < count($_FILES['materials']['name']); $i++) {
            if ($_FILES['materials']['error'][$i] === UPLOAD_ERR_OK) {
                $file_name = time() . '_' . $_FILES['materials']['name'][$i];
                $file_path = $upload_dir . $file_name;
                
                if (move_uploaded_file($_FILES['materials']['tmp_name'][$i], $file_path)) {
                    $materials[] = 'uploads/sessions/' . $file_name;
                }
            }
        }
    }
    
    // إدراج الجلسة الجديدة
    $session_data = [
        'course_id' => $course_id,
        'title' => $title,
        'description' => $description,
        'session_date' => $session_date,
        'start_time' => $start_time,
        'end_time' => $end_time,
        'session_type' => $session_type,
        'meeting_url' => $meeting_url,
        'materials' => json_encode($materials),
        'status' => $status,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $session_id = insertRecord('sessions', $session_data);
    
    if ($session_id) {
        $success = 'تم إضافة الجلسة بنجاح';
        logUserActivity($_SESSION['user_id'], 'إضافة جلسة', "تم إضافة جلسة جديدة: $title");
    } else {
        $error = 'حدث خطأ أثناء إضافة الجلسة';
    }
}

// معاملات التصفية
$course_filter = $_GET['course_id'] ?? '';
$status_filter = $_GET['status'] ?? '';
$type_filter = $_GET['type'] ?? '';
$date_filter = $_GET['date'] ?? '';
$search = $_GET['search'] ?? '';

// بناء استعلام جلب الجلسات
$where_conditions = [];
$params = [];

if ($course_filter) {
    $where_conditions[] = "s.course_id = ?";
    $params[] = $course_filter;
}

if ($status_filter) {
    $where_conditions[] = "s.status = ?";
    $params[] = $status_filter;
}

if ($type_filter) {
    $where_conditions[] = "s.session_type = ?";
    $params[] = $type_filter;
}

if ($date_filter) {
    $where_conditions[] = "DATE(s.session_date) = ?";
    $params[] = $date_filter;
}

if ($search) {
    $where_conditions[] = "(s.title LIKE ? OR s.description LIKE ? OR c.title LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب الجلسات مع معلومات الكورس والمدرب
$sessions_sql = "
    SELECT 
        s.*,
        c.title as course_title,
        c.course_type,
        u.name as instructor_name,
        COUNT(DISTINCT sa.id) as attendance_count,
        COUNT(DISTINCT ce.id) as enrolled_count
    FROM sessions s
    LEFT JOIN courses c ON s.course_id = c.id
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN session_attendance sa ON s.id = sa.session_id AND sa.status = 'present'
    LEFT JOIN course_enrollments ce ON c.id = ce.course_id AND ce.status = 'active'
    $where_clause
    GROUP BY s.id
    ORDER BY s.session_date DESC, s.start_time DESC
";

$sessions = fetchAll($sessions_sql, $params);

// جلب قوائم للفلترة
$courses = fetchAll("
    SELECT c.id, c.title, u.name as instructor_name 
    FROM courses c 
    LEFT JOIN users u ON c.instructor_id = u.id 
    ORDER BY c.title
");

// إحصائيات عامة
$total_sessions = countRecords('sessions');
$upcoming_sessions = countRecords('sessions', "session_date >= CURDATE() AND status = 'scheduled'");
$completed_sessions = countRecords('sessions', "status = 'completed'");
$live_sessions = countRecords('sessions', "status = 'live'");

include 'includes/header.php';
?>

<!-- الإحصائيات العامة -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-primary mb-1"><?php echo number_format($total_sessions); ?></h3>
                    <p class="text-muted mb-0">إجمالي الجلسات</p>
                    <small class="text-muted">
                        <i class="fas fa-video me-1"></i>
                        جميع الجلسات في النظام
                    </small>
                </div>
                <div class="text-primary">
                    <i class="fas fa-play-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-success mb-1"><?php echo number_format($upcoming_sessions); ?></h3>
                    <p class="text-muted mb-0">الجلسات القادمة</p>
                    <small class="text-muted">
                        <i class="fas fa-calendar-plus me-1"></i>
                        مجدولة للمستقبل
                    </small>
                </div>
                <div class="text-success">
                    <i class="fas fa-calendar-check fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-warning mb-1"><?php echo number_format($live_sessions); ?></h3>
                    <p class="text-muted mb-0">الجلسات المباشرة</p>
                    <small class="text-muted">
                        <i class="fas fa-broadcast-tower me-1"></i>
                        جارية الآن
                    </small>
                </div>
                <div class="text-warning">
                    <i class="fas fa-satellite-dish fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-info mb-1"><?php echo number_format($completed_sessions); ?></h3>
                    <p class="text-muted mb-0">الجلسات المكتملة</p>
                    <small class="text-muted">
                        <i class="fas fa-check-circle me-1"></i>
                        تم إنجازها
                    </small>
                </div>
                <div class="text-info">
                    <i class="fas fa-clipboard-check fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أدوات التحكم والفلترة -->
<div class="card-admin mb-4" data-aos="fade-up">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>
                أدوات التحكم والفلترة
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addSessionModal">
                    <i class="fas fa-plus me-1"></i>
                    إضافة جلسة جديدة
                </button>
                <button class="btn btn-info btn-sm" onclick="exportSessions()">
                    <i class="fas fa-download me-1"></i>
                    تصدير البيانات
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" placeholder="ابحث في الجلسات..." 
                       value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">الكورس</label>
                <select name="course_id" class="form-select">
                    <option value="">جميع الكورسات</option>
                    <?php foreach ($courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>" 
                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="scheduled" <?php echo $status_filter === 'scheduled' ? 'selected' : ''; ?>>مجدولة</option>
                    <option value="live" <?php echo $status_filter === 'live' ? 'selected' : ''; ?>>مباشرة</option>
                    <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                    <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغية</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">النوع</label>
                <select name="type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="live" <?php echo $type_filter === 'live' ? 'selected' : ''; ?>>مباشرة</option>
                    <option value="recorded" <?php echo $type_filter === 'recorded' ? 'selected' : ''; ?>>مسجلة</option>
                    <option value="hybrid" <?php echo $type_filter === 'hybrid' ? 'selected' : ''; ?>>مختلطة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">التاريخ</label>
                <input type="date" name="date" class="form-control" value="<?php echo htmlspecialchars($date_filter); ?>">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
        
        <?php if ($course_filter || $status_filter || $type_filter || $date_filter || $search): ?>
        <div class="mt-3">
            <a href="manage-sessions-new.php" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-times me-1"></i>
                إزالة الفلاتر
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- نموذج إضافة جلسة جديدة -->
<div class="modal fade" id="addSessionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة جلسة جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">عنوان الجلسة <span class="text-danger">*</span></label>
                                <input type="text" name="title" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الحالة <span class="text-danger">*</span></label>
                                <select name="status" class="form-select" required>
                                    <option value="scheduled">مجدولة</option>
                                    <option value="live">مباشرة</option>
                                    <option value="completed">مكتملة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">وصف الجلسة</label>
                        <textarea name="description" class="form-control" rows="3" placeholder="اكتب وصفاً للجلسة..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الكورس <span class="text-danger">*</span></label>
                                <select name="course_id" class="form-select" required>
                                    <option value="">اختر الكورس</option>
                                    <?php foreach ($courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>"
                                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($course['title']); ?>
                                        (<?php echo htmlspecialchars($course['instructor_name']); ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع الجلسة <span class="text-danger">*</span></label>
                                <select name="session_type" class="form-select" required>
                                    <option value="live">مباشرة</option>
                                    <option value="recorded">مسجلة</option>
                                    <option value="hybrid">مختلطة</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الجلسة <span class="text-danger">*</span></label>
                                <input type="date" name="session_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">وقت البداية <span class="text-danger">*</span></label>
                                <input type="time" name="start_time" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">وقت النهاية <span class="text-danger">*</span></label>
                                <input type="time" name="end_time" class="form-control" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">رابط الاجتماع</label>
                        <input type="url" name="meeting_url" class="form-control" placeholder="https://zoom.us/j/...">
                        <small class="text-muted">رابط Zoom أو أي منصة اجتماعات أخرى</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">المواد التعليمية</label>
                        <input type="file" name="materials[]" class="form-control" multiple accept=".pdf,.doc,.docx,.ppt,.pptx,.mp4,.mp3">
                        <small class="text-muted">يمكنك رفع عدة ملفات (PDF, DOC, PPT, MP4, MP3)</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="add_session" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        حفظ الجلسة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 16px;
    font-weight: 600;
}

.table th {
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 6px !important;
    margin: 0 1px;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card.success {
    border-left: 4px solid #28a745;
}

.stats-card.warning {
    border-left: 4px solid #ffc107;
}

.stats-card.info {
    border-left: 4px solid #17a2b8;
}
</style>

<script>
$(document).ready(function() {
    // تهيئة DataTables مع فحص عدم إعادة التهيئة
    if (!$.fn.DataTable.isDataTable('#sessionsTable')) {
        const table = $('#sessionsTable').DataTable({
            order: [[3, 'desc']], // ترتيب حسب التاريخ
            pageLength: 25,
            responsive: true,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
            },
            columnDefs: [
                { orderable: false, targets: [7] } // عمود الإجراءات غير قابل للترتيب
            ],
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip'
        });
    }

    // تعيين التاريخ الافتراضي لليوم الحالي
    const today = new Date().toISOString().split('T')[0];
    $('input[name="session_date"]').val(today);

    // تهيئة التلميحات
    $('[data-bs-toggle="tooltip"]').tooltip();
});

// إعادة تعيين الفلاتر
function resetFilters() {
    window.location.href = 'manage-sessions-new.php';
}

// تصدير البيانات
function exportSessions() {
    Swal.fire({
        title: 'تصدير بيانات الجلسات',
        html: `
            <div class="d-grid gap-2">
                <button class="btn btn-success" onclick="exportData('excel', 'export-sessions.php')">
                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                </button>
                <button class="btn btn-danger" onclick="exportData('pdf', 'export-sessions.php')">
                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                </button>
                <button class="btn btn-info" onclick="exportData('csv', 'export-sessions.php')">
                    <i class="fas fa-file-csv me-2"></i>تصدير CSV
                </button>
            </div>
        `,
        showConfirmButton: false,
        showCloseButton: true,
        width: 300
    });
}

// حذف جلسة
function deleteSession(sessionId, sessionTitle) {
    Swal.fire({
        title: 'حذف الجلسة',
        html: `
            <p>هل أنت متأكد من حذف الجلسة <strong>${sessionTitle}</strong>؟</p>
            <div class="alert alert-warning text-start">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> سيتم حذف جميع البيانات المرتبطة بهذه الجلسة:
                <ul class="mt-2 mb-0">
                    <li>سجلات الحضور</li>
                    <li>المواد التعليمية</li>
                    <li>التسجيلات</li>
                </ul>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545',
        input: 'checkbox',
        inputPlaceholder: 'أؤكد أنني أريد حذف هذه الجلسة نهائياً'
    }).then((result) => {
        if (result.isConfirmed && result.value) {
            // هنا يمكن إضافة كود الحذف عبر AJAX
            Swal.fire('تم الحذف!', 'تم حذف الجلسة بنجاح', 'success').then(() => {
                location.reload();
            });
        } else if (result.isConfirmed && !result.value) {
            Swal.fire({
                title: 'مطلوب تأكيد',
                text: 'يجب تأكيد الحذف بوضع علامة في المربع',
                icon: 'info'
            });
        }
    });
}

// دالة مساعدة لتصدير البيانات
function exportData(format, url) {
    window.open(`${url}?format=${format}`, '_blank');
    Swal.close();
}
</script>

<?php
// عرض رسائل النجاح أو الخطأ
if (isset($success)): ?>
<script>
$(document).ready(function() {
    Swal.fire({
        title: 'تم بنجاح!',
        text: '<?php echo $success; ?>',
        icon: 'success',
        confirmButtonText: 'موافق'
    });
});
</script>
<?php endif; ?>

<?php if (isset($error)): ?>
<script>
$(document).ready(function() {
    Swal.fire({
        title: 'خطأ!',
        text: '<?php echo $error; ?>',
        icon: 'error',
        confirmButtonText: 'موافق'
    });
});
</script>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>

<!-- جدول الجلسات -->
<div class="card-admin" data-aos="fade-up">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة الجلسات (<?php echo number_format(count($sessions)); ?>)
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm" onclick="resetFilters()">
                    <i class="fas fa-redo me-1"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($sessions)): ?>
        <div class="text-center py-5">
            <i class="fas fa-video fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد جلسات</h5>
            <p class="text-muted">لم يتم العثور على جلسات تطابق معايير البحث</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSessionModal">
                <i class="fas fa-plus me-2"></i>
                إضافة جلسة جديدة
            </button>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="sessionsTable">
                <thead class="table-light">
                    <tr>
                        <th>الجلسة</th>
                        <th>الكورس</th>
                        <th>المدرب</th>
                        <th>التاريخ والوقت</th>
                        <th>النوع</th>
                        <th>الحضور</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($sessions as $session): ?>
                    <tr data-session-id="<?php echo $session['id']; ?>">
                        <td>
                            <div>
                                <h6 class="mb-0"><?php echo htmlspecialchars($session['title']); ?></h6>
                                <small class="text-muted">
                                    <?php echo mb_substr(strip_tags($session['description'] ?? ''), 0, 50); ?>...
                                </small>
                                <?php if ($session['materials']): ?>
                                <br><span class="badge bg-light text-dark">
                                    <i class="fas fa-paperclip me-1"></i>
                                    <?php echo count(json_decode($session['materials'], true)); ?> ملف
                                </span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div>
                                <h6 class="mb-0"><?php echo htmlspecialchars($session['course_title']); ?></h6>
                                <span class="badge bg-<?php echo $session['course_type'] === 'paid' ? 'warning' : 'success'; ?>">
                                    <?php echo $session['course_type'] === 'paid' ? 'مدفوع' : 'مجاني'; ?>
                                </span>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm rounded-circle bg-info text-white me-2 d-flex align-items-center justify-content-center">
                                    <?php echo strtoupper(substr($session['instructor_name'] ?? 'N', 0, 1)); ?>
                                </div>
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($session['instructor_name'] ?? 'غير محدد'); ?></h6>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo date('Y-m-d', strtotime($session['session_date'])); ?></strong>
                                <br>
                                <small class="text-muted">
                                    <?php echo date('H:i', strtotime($session['start_time'])); ?> - 
                                    <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                </small>
                            </div>
                        </td>
                        <td>
                            <?php 
                            $type_badges = [
                                'live' => 'bg-danger',
                                'recorded' => 'bg-info',
                                'hybrid' => 'bg-warning'
                            ];
                            $type_names = [
                                'live' => 'مباشرة',
                                'recorded' => 'مسجلة',
                                'hybrid' => 'مختلطة'
                            ];
                            $type = $session['session_type'] ?? 'live';
                            ?>
                            <span class="badge <?php echo $type_badges[$type] ?? 'bg-secondary'; ?>">
                                <?php echo $type_names[$type] ?? $type; ?>
                            </span>
                        </td>
                        <td>
                            <div class="text-center">
                                <strong class="text-primary"><?php echo $session['attendance_count']; ?></strong>
                                <small class="text-muted">/ <?php echo $session['enrolled_count']; ?></small>
                                <br>
                                <small class="text-muted">
                                    <?php 
                                    $attendance_rate = $session['enrolled_count'] > 0 ? 
                                        round(($session['attendance_count'] / $session['enrolled_count']) * 100) : 0;
                                    echo $attendance_rate . '%';
                                    ?>
                                </small>
                            </div>
                        </td>
                        <td>
                            <?php 
                            $status_badges = [
                                'scheduled' => 'bg-primary',
                                'live' => 'bg-danger',
                                'completed' => 'bg-success',
                                'cancelled' => 'bg-secondary'
                            ];
                            $status_names = [
                                'scheduled' => 'مجدولة',
                                'live' => 'مباشرة',
                                'completed' => 'مكتملة',
                                'cancelled' => 'ملغية'
                            ];
                            $status = $session['status'] ?? 'scheduled';
                            ?>
                            <span class="badge <?php echo $status_badges[$status] ?? 'bg-secondary'; ?>">
                                <?php echo $status_names[$status] ?? $status; ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="session-details.php?id=<?php echo $session['id']; ?>"
                                   class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="edit-session.php?id=<?php echo $session['id']; ?>"
                                   class="btn btn-sm btn-outline-warning" data-bs-toggle="tooltip" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php if ($session['meeting_url']): ?>
                                <a href="<?php echo htmlspecialchars($session['meeting_url']); ?>" target="_blank"
                                   class="btn btn-sm btn-outline-success" data-bs-toggle="tooltip" title="رابط الاجتماع">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <?php endif; ?>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="deleteSession(<?php echo $session['id']; ?>, '<?php echo htmlspecialchars($session['title']); ?>')"
                                        data-bs-toggle="tooltip" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>
