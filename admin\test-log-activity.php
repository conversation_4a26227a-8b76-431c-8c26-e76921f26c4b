<?php
require_once 'includes/simple_db.php';
session_start();

echo "<h2>اختبار دالة logUserActivity</h2>";

try {
    // محاولة تسجيل نشاط تجريبي
    $result = logUserActivity(1, 'test_action', 'اختبار تسجيل النشاط');
    
    if ($result) {
        echo "<p style='color: green;'>✓ تم تسجيل النشاط بنجاح</p>";
    } else {
        echo "<p style='color: red;'>✗ فشل في تسجيل النشاط</p>";
    }
    
    // عرض الأنشطة المسجلة
    $activities = fetchAll("SELECT * FROM user_activities ORDER BY created_at DESC LIMIT 5");
    
    echo "<h3>آخر 5 أنشطة:</h3>";
    if (empty($activities)) {
        echo "<p>لا توجد أنشطة مسجلة</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>User ID</th><th>Action</th><th>Details</th><th>IP</th><th>Created At</th></tr>";
        
        foreach ($activities as $activity) {
            echo "<tr>";
            echo "<td>" . $activity['id'] . "</td>";
            echo "<td>" . $activity['user_id'] . "</td>";
            echo "<td>" . $activity['action'] . "</td>";
            echo "<td>" . $activity['details'] . "</td>";
            echo "<td>" . $activity['ip_address'] . "</td>";
            echo "<td>" . $activity['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<p><a href='add-instructor.php'>اختبار إضافة مدرب</a></p>";
    echo "<p><a href='add-course.php'>اختبار إضافة كورس</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
