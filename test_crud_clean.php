<?php
/**
 * اختبار عمليات CRUD النظيف والمصحح
 * Clean and Fixed CRUD Operations Test
 * ===================================
 */

require_once 'includes/database_manager_clean.php';

echo "<h1>🧪 اختبار عمليات CRUD النظيف والمصحح</h1>";
echo "<div style='font-family: Arial; padding: 20px; background: #f8f9fa; border-radius: 10px; margin: 20px;'>";

$testResults = [];
$testData = []; // لحفظ معرفات البيانات التجريبية

// ===================================================================
// 1. اختبار عمليات CREATE (إنشاء)
// ===================================================================
echo "<h2>1️⃣ اختبار عمليات CREATE (إنشاء)</h2>";

// إنشاء فئة جديدة
$categoryData = [
    'name' => 'فئة اختبار نظيفة',
    'slug' => 'test-category-clean-' . time(),
    'description' => 'هذه فئة للاختبار النظيف',
    'icon' => 'fas fa-test',
    'color' => '#28a745',
    'is_active' => 1,
    'sort_order' => 999
];

$categoryId = $dbClean->create('categories', $categoryData);
if ($categoryId) {
    echo "✅ <span style='color: green;'>تم إنشاء فئة جديدة بالمعرف: {$categoryId}</span><br>";
    $testResults['create_category'] = true;
    $testData['category_id'] = $categoryId;
} else {
    echo "❌ <span style='color: red;'>فشل في إنشاء الفئة</span><br>";
    $testResults['create_category'] = false;
}

// إنشاء مستخدم جديد
$userData = [
    'username' => 'testuser_clean_' . time(),
    'email' => 'test_clean_' . time() . '@example.com',
    'password' => password_hash('123456', PASSWORD_DEFAULT),
    'name' => 'مستخدم اختبار نظيف',
    'role' => 'instructor',
    'status' => 'active',
    'email_verified' => 1
];

$userId = $dbClean->create('users', $userData);
if ($userId) {
    echo "✅ <span style='color: green;'>تم إنشاء مستخدم جديد بالمعرف: {$userId}</span><br>";
    $testResults['create_user'] = true;
    $testData['user_id'] = $userId;
} else {
    echo "❌ <span style='color: red;'>فشل في إنشاء المستخدم</span><br>";
    $testResults['create_user'] = false;
}

// إنشاء كورس جديد
if (isset($testData['category_id']) && isset($testData['user_id'])) {
    $courseData = [
        'title' => 'كورس اختبار نظيف',
        'slug' => 'test-course-clean-' . time(),
        'short_description' => 'وصف قصير للكورس النظيف',
        'description' => 'وصف مفصل للكورس التجريبي النظيف',
        'category_id' => $testData['category_id'],
        'instructor_id' => $testData['user_id'],
        'level' => 'beginner',
        'price' => 199.99,
        'is_free' => 0,
        'status' => 'published'
    ];
    
    $courseId = $dbClean->create('courses', $courseData);
    if ($courseId) {
        echo "✅ <span style='color: green;'>تم إنشاء كورس جديد بالمعرف: {$courseId}</span><br>";
        $testResults['create_course'] = true;
        $testData['course_id'] = $courseId;
    } else {
        echo "❌ <span style='color: red;'>فشل في إنشاء الكورس</span><br>";
        $testResults['create_course'] = false;
    }
}

// إنشاء وجبة جديدة
$mealCategories = $dbClean->getAll('meal_categories', '*', 'id ASC', 1);
if (!empty($mealCategories) && isset($testData['user_id'])) {
    $mealData = [
        'name' => 'وجبة اختبار نظيفة',
        'description' => 'وجبة صحية للاختبار النظيف',
        'category_id' => $mealCategories[0]['id'],
        'calories' => 380,
        'protein' => 28,
        'carbs' => 45,
        'fat' => 15,
        'prep_time' => 20,
        'cook_time' => 25,
        'total_time' => 45,
        'servings' => 3,
        'difficulty' => 'medium',
        'meal_type' => 'dinner',
        'diet_type' => 'high_protein',
        'created_by' => $testData['user_id'],
        'status' => 'published'
    ];
    
    $mealId = $dbClean->create('meals', $mealData);
    if ($mealId) {
        echo "✅ <span style='color: green;'>تم إنشاء وجبة جديدة بالمعرف: {$mealId}</span><br>";
        $testResults['create_meal'] = true;
        $testData['meal_id'] = $mealId;
    } else {
        echo "❌ <span style='color: red;'>فشل في إنشاء الوجبة</span><br>";
        $testResults['create_meal'] = false;
    }
}

// ===================================================================
// 2. اختبار عمليات READ (قراءة)
// ===================================================================
echo "<h2>2️⃣ اختبار عمليات READ (قراءة)</h2>";

// قراءة سجل واحد
if (isset($testData['category_id'])) {
    $category = $dbClean->find('categories', $testData['category_id']);
    if ($category && $category['name'] === 'فئة اختبار نظيفة') {
        echo "✅ <span style='color: green;'>تم قراءة الفئة بنجاح: {$category['name']}</span><br>";
        $testResults['read_single'] = true;
    } else {
        echo "❌ <span style='color: red;'>فشل في قراءة الفئة</span><br>";
        $testResults['read_single'] = false;
    }
}

// قراءة متعددة بشروط
$activeCategories = $dbClean->getWhere('categories', ['is_active' => 1]);
if (!empty($activeCategories)) {
    echo "✅ <span style='color: green;'>تم قراءة " . count($activeCategories) . " فئة نشطة</span><br>";
    $testResults['read_multiple'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في قراءة الفئات النشطة</span><br>";
    $testResults['read_multiple'] = false;
}

// البحث في النصوص
$searchResults = $dbClean->search('categories', 'نظيفة', ['name', 'description']);
if (!empty($searchResults)) {
    echo "✅ <span style='color: green;'>تم العثور على " . count($searchResults) . " نتيجة بحث</span><br>";
    $testResults['search'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في البحث</span><br>";
    $testResults['search'] = false;
}

// عد السجلات
$totalUsers = $dbClean->count('users');
if ($totalUsers > 0) {
    echo "✅ <span style='color: green;'>إجمالي المستخدمين: {$totalUsers}</span><br>";
    $testResults['count'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في عد المستخدمين</span><br>";
    $testResults['count'] = false;
}

// اختبار قراءة الوجبات
$allMeals = $dbClean->getAll('meals');
if (!empty($allMeals)) {
    echo "✅ <span style='color: green;'>تم قراءة " . count($allMeals) . " وجبة</span><br>";
    $testResults['read_meals'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في قراءة الوجبات</span><br>";
    $testResults['read_meals'] = false;
}

// ===================================================================
// 3. اختبار عمليات UPDATE (تحديث)
// ===================================================================
echo "<h2>3️⃣ اختبار عمليات UPDATE (تحديث)</h2>";

// تحديث الفئة
if (isset($testData['category_id'])) {
    $updateData = [
        'name' => 'فئة اختبار نظيفة محدثة',
        'description' => 'تم تحديث الوصف بنجاح',
        'color' => '#17a2b8'
    ];
    
    $updateResult = $dbClean->update('categories', $testData['category_id'], $updateData);
    if ($updateResult) {
        echo "✅ <span style='color: green;'>تم تحديث الفئة بنجاح</span><br>";
        $testResults['update_category'] = true;
        
        // التحقق من التحديث
        $updatedCategory = $dbClean->find('categories', $testData['category_id']);
        if ($updatedCategory['name'] === 'فئة اختبار نظيفة محدثة') {
            echo "✅ <span style='color: green;'>تم التحقق من التحديث بنجاح</span><br>";
        }
    } else {
        echo "❌ <span style='color: red;'>فشل في تحديث الفئة</span><br>";
        $testResults['update_category'] = false;
    }
}

// تحديث الوجبة
if (isset($testData['meal_id'])) {
    $mealUpdateData = [
        'name' => 'وجبة اختبار نظيفة محدثة',
        'calories' => 400,
        'protein' => 30
    ];
    
    $mealUpdateResult = $dbClean->update('meals', $testData['meal_id'], $mealUpdateData);
    if ($mealUpdateResult) {
        echo "✅ <span style='color: green;'>تم تحديث الوجبة بنجاح</span><br>";
        $testResults['update_meal'] = true;
    } else {
        echo "❌ <span style='color: red;'>فشل في تحديث الوجبة</span><br>";
        $testResults['update_meal'] = false;
    }
}

// ===================================================================
// 4. اختبار العمليات المتقدمة
// ===================================================================
echo "<h2>4️⃣ اختبار العمليات المتقدمة</h2>";

// اختبار البحث في الوجبات
$mealSearchResults = $dbClean->search('meals', 'نظيفة', ['name', 'description']);
if (!empty($mealSearchResults)) {
    echo "✅ <span style='color: green;'>البحث في الوجبات يعمل - " . count($mealSearchResults) . " نتيجة</span><br>";
    $testResults['meal_search'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في البحث في الوجبات</span><br>";
    $testResults['meal_search'] = false;
}

// اختبار الفلترة بالشروط
$healthyMeals = $dbClean->getWhere('meals', ['calories <' => 500], '*', 'calories ASC');
if (!empty($healthyMeals)) {
    echo "✅ <span style='color: green;'>تم العثور على " . count($healthyMeals) . " وجبة صحية (أقل من 500 سعرة)</span><br>";
    $testResults['meal_filter'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في فلترة الوجبات</span><br>";
    $testResults['meal_filter'] = false;
}

// اختبار الإحصائيات
$instructorCount = $dbClean->count('users', ['role' => 'instructor']);
$studentCount = $dbClean->count('users', ['role' => 'student']);
$adminCount = $dbClean->count('users', ['role' => 'admin']);

echo "✅ <span style='color: green;'>إحصائيات المستخدمين:</span><br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;📊 المدراء: {$adminCount}<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;👨‍🏫 المدربين: {$instructorCount}<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;👨‍🎓 الطلاب: {$studentCount}<br>";
$testResults['statistics'] = true;

// اختبار استعلام مخصص
$customQuery = "SELECT c.name as category_name, COUNT(m.id) as meals_count 
                FROM meal_categories c 
                LEFT JOIN meals m ON c.id = m.category_id 
                GROUP BY c.id, c.name 
                ORDER BY meals_count DESC";

$customResults = $dbClean->query($customQuery);
if (!empty($customResults)) {
    echo "✅ <span style='color: green;'>الاستعلام المخصص يعمل - " . count($customResults) . " نتيجة</span><br>";
    $testResults['custom_query'] = true;
} else {
    echo "❌ <span style='color: red;'>فشل في الاستعلام المخصص</span><br>";
    $testResults['custom_query'] = false;
}

// ===================================================================
// 5. اختبار نظام الوجبات بالتفصيل
// ===================================================================
echo "<h2>5️⃣ اختبار نظام الوجبات بالتفصيل</h2>";

// عرض فئات الوجبات
$mealCategories = $dbClean->getAll('meal_categories');
echo "✅ <span style='color: green;'>فئات الوجبات المتاحة:</span><br>";
foreach ($mealCategories as $cat) {
    $mealCount = $dbClean->count('meals', ['category_id' => $cat['id']]);
    echo "&nbsp;&nbsp;&nbsp;&nbsp;🍽️ {$cat['name']}: {$mealCount} وجبة<br>";
}

// عرض الوجبات حسب النوع
$mealTypes = ['breakfast', 'lunch', 'dinner', 'snack', 'dessert'];
$typeLabels = [
    'breakfast' => 'إفطار',
    'lunch' => 'غداء',
    'dinner' => 'عشاء',
    'snack' => 'وجبة خفيفة',
    'dessert' => 'حلوى'
];

echo "✅ <span style='color: green;'>الوجبات حسب النوع:</span><br>";
foreach ($mealTypes as $type) {
    $count = $dbClean->count('meals', ['meal_type' => $type]);
    echo "&nbsp;&nbsp;&nbsp;&nbsp;🥘 {$typeLabels[$type]}: {$count} وجبة<br>";
}

$testResults['meal_system'] = true;

// ===================================================================
// 6. اختبار عمليات DELETE (حذف)
// ===================================================================
echo "<h2>6️⃣ اختبار عمليات DELETE (حذف)</h2>";

// حذف البيانات التجريبية
$deletedCount = 0;

if (isset($testData['meal_id'])) {
    if ($dbClean->delete('meals', $testData['meal_id'])) {
        echo "✅ تم حذف الوجبة التجريبية<br>";
        $deletedCount++;
    }
}

if (isset($testData['course_id'])) {
    if ($dbClean->delete('courses', $testData['course_id'])) {
        echo "✅ تم حذف الكورس التجريبي<br>";
        $deletedCount++;
    }
}

if (isset($testData['category_id'])) {
    if ($dbClean->delete('categories', $testData['category_id'])) {
        echo "✅ تم حذف الفئة التجريبية<br>";
        $deletedCount++;
    }
}

if (isset($testData['user_id'])) {
    if ($dbClean->delete('users', $testData['user_id'])) {
        echo "✅ تم حذف المستخدم التجريبي<br>";
        $deletedCount++;
    }
}

if ($deletedCount > 0) {
    echo "✅ <span style='color: green;'>تم تنظيف {$deletedCount} سجل تجريبي</span><br>";
    $testResults['cleanup'] = true;
} else {
    echo "⚠️ <span style='color: orange;'>لم يتم حذف أي سجلات</span><br>";
    $testResults['cleanup'] = false;
}

// ===================================================================
// 7. ملخص النتائج النهائي
// ===================================================================
echo "<h2>📊 ملخص نتائج الاختبار النظيف</h2>";

$totalTests = count($testResults);
$passedTests = count(array_filter($testResults));
$failedTests = $totalTests - $passedTests;

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 10px 0;'>";
echo "<h3>📈 الإحصائيات النهائية:</h3>";
echo "<strong>إجمالي الاختبارات:</strong> {$totalTests}<br>";
echo "<strong style='color: green;'>الاختبارات الناجحة:</strong> {$passedTests}<br>";
echo "<strong style='color: red;'>الاختبارات الفاشلة:</strong> {$failedTests}<br>";
echo "<strong>معدل النجاح:</strong> " . round(($passedTests / $totalTests) * 100, 2) . "%<br>";
echo "</div>";

echo "<h3>📋 تفاصيل النتائج:</h3>";
echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1);'>";
echo "<tr style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;'><th style='padding: 15px; font-weight: 600;'>الاختبار</th><th style='padding: 15px; font-weight: 600;'>النتيجة</th></tr>";

foreach ($testResults as $test => $result) {
    $status = $result ? '✅ نجح' : '❌ فشل';
    $color = $result ? 'green' : 'red';
    $bgColor = $result ? '#f8fff8' : '#fff8f8';
    echo "<tr style='background: {$bgColor};'><td style='padding: 12px; border: 1px solid #ddd;'>{$test}</td><td style='padding: 12px; border: 1px solid #ddd; color: {$color}; font-weight: 600;'>{$status}</td></tr>";
}

echo "</table>";

if ($passedTests === $totalTests) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 15px; margin: 20px 0; border-left: 5px solid #28a745;'>";
    echo "<h3>🎉 نجاح باهر!</h3>";
    echo "<p><strong>جميع اختبارات CRUD النظيفة نجحت بنسبة 100%!</strong></p>";
    echo "<p>النظام جاهز للاستخدام الكامل مع جميع الميزات المتقدمة.</p>";
    
    echo "<h4>✨ المميزات المؤكدة:</h4>";
    echo "<ul>";
    echo "<li>🗄️ جميع عمليات CRUD تعمل بشكل مثالي</li>";
    echo "<li>🍽️ نظام الوجبات يعمل بكامل وظائفه</li>";
    echo "<li>🔍 البحث والفلترة يعملان بدقة عالية</li>";
    echo "<li>📊 الإحصائيات والتقارير دقيقة ومحدثة</li>";
    echo "<li>🔧 لا توجد مشاكل في إدارة المعاملات</li>";
    echo "<li>🧹 تنظيف البيانات يعمل بكفاءة</li>";
    echo "<li>⚡ الأداء محسن وسريع</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 15px; margin: 20px 0; border-left: 5px solid #dc3545;'>";
    echo "<h3>⚠️ تحذير!</h3>";
    echo "<p>بعض الاختبارات فشلت. يرجى مراجعة الأخطاء وإصلاحها.</p>";
    echo "</div>";
}

echo "<div style='background: #cff4fc; padding: 20px; border-radius: 15px; margin: 20px 0; border-left: 5px solid #0dcaf0;'>";
echo "<h3>🔗 استكشاف النظام المكتمل:</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 15px;'>";
echo "<a href='admin/database_viewer.php' style='display: block; padding: 12px 20px; background: #0d6efd; color: white; text-decoration: none; border-radius: 8px; text-align: center; font-weight: 600; transition: all 0.3s ease;'>📊 عارض قاعدة البيانات</a>";
echo "<a href='visitor_homepage.php' style='display: block; padding: 12px 20px; background: #6f42c1; color: white; text-decoration: none; border-radius: 8px; text-align: center; font-weight: 600; transition: all 0.3s ease;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='meals.php' style='display: block; padding: 12px 20px; background: #198754; color: white; text-decoration: none; border-radius: 8px; text-align: center; font-weight: 600; transition: all 0.3s ease;'>🍽️ صفحة الوجبات</a>";
echo "<a href='courses.php' style='display: block; padding: 12px 20px; background: #fd7e14; color: white; text-decoration: none; border-radius: 8px; text-align: center; font-weight: 600; transition: all 0.3s ease;'>📚 صفحة الكورسات</a>";
echo "</div>";
echo "</div>";

echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

h1 {
    color: white;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 30px;
    font-size: 2.5rem;
}

h2 {
    color: #495057;
    border-bottom: 3px solid #dee2e6;
    padding-bottom: 8px;
    margin-top: 40px;
    font-size: 1.8rem;
}

h3 {
    color: #495057;
    margin-top: 25px;
    font-size: 1.4rem;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.9;
    transform: translateY(-3px);
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

ul {
    padding-left: 25px;
}

li {
    margin-bottom: 8px;
    line-height: 1.6;
}
</style>
