<?php
session_start();

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

echo "<h1>إضافة بيانات تجريبية</h1>";

try {
    require_once '../includes/database_manager_clean.php';
    
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</div><br>";
    
    // إضافة مدفوعات تجريبية
    echo "<h2>إضافة مدفوعات تجريبية...</h2>";
    
    // جلب المستخدمين والكورسات الموجودين
    $users = $dbClean->getAll('users', 'id', '', 10);
    $courses = $dbClean->getAll('courses', 'id, price', '', 10);
    
    if (!empty($users) && !empty($courses)) {
        $payments = [];
        
        for ($i = 0; $i < 20; $i++) {
            $user = $users[array_rand($users)];
            $course = $courses[array_rand($courses)];
            
            $amount = $course['price'] ?: rand(100, 1000);
            $commission = $amount * 0.3; // 30% عمولة
            
            $statuses = ['completed', 'completed', 'completed', 'pending', 'failed'];
            $status = $statuses[array_rand($statuses)];
            
            $methods = ['credit_card', 'paypal', 'bank_transfer'];
            $method = $methods[array_rand($methods)];
            
            // تاريخ عشوائي في آخر 6 أشهر
            $randomDate = date('Y-m-d H:i:s', strtotime('-' . rand(1, 180) . ' days'));
            
            $payment = [
                'user_id' => $user['id'],
                'course_id' => $course['id'],
                'amount' => $amount,
                'commission_amount' => $commission,
                'payment_method' => $method,
                'payment_date' => $randomDate,
                'status' => $status,
                'transaction_id' => 'TXN_' . strtoupper(uniqid()),
                'notes' => 'دفعة تجريبية للاختبار'
            ];
            
            try {
                $paymentId = $dbClean->insert('payments', $payment);
                echo "<div style='color: blue;'>➕ تم إضافة دفعة #{$paymentId} - {$amount} ريال ({$status})</div>";
            } catch (Exception $e) {
                echo "<div style='color: orange;'>⚠️ خطأ في إضافة دفعة: " . $e->getMessage() . "</div>";
            }
        }
    } else {
        echo "<div style='color: red;'>❌ لا توجد مستخدمين أو كورسات لإضافة مدفوعات</div>";
    }
    
    // إضافة تسجيلات تجريبية
    echo "<h2>إضافة تسجيلات تجريبية...</h2>";
    
    if (!empty($users) && !empty($courses)) {
        for ($i = 0; $i < 30; $i++) {
            $user = $users[array_rand($users)];
            $course = $courses[array_rand($courses)];
            
            $statuses = ['active', 'active', 'active', 'completed', 'pending'];
            $status = $statuses[array_rand($statuses)];
            
            $randomDate = date('Y-m-d H:i:s', strtotime('-' . rand(1, 90) . ' days'));
            
            $enrollment = [
                'student_id' => $user['id'],
                'course_id' => $course['id'],
                'status' => $status,
                'enrolled_at' => $randomDate,
                'progress' => rand(0, 100)
            ];
            
            try {
                $enrollmentId = $dbClean->insert('course_enrollments', $enrollment);
                echo "<div style='color: blue;'>➕ تم إضافة تسجيل #{$enrollmentId} - المستخدم {$user['id']} في الكورس {$course['id']}</div>";
            } catch (Exception $e) {
                echo "<div style='color: orange;'>⚠️ خطأ في إضافة تسجيل: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // إضافة جلسات تجريبية
    echo "<h2>إضافة جلسات تجريبية...</h2>";
    
    if (!empty($courses)) {
        for ($i = 0; $i < 15; $i++) {
            $course = $courses[array_rand($courses)];
            
            $statuses = ['scheduled', 'completed', 'completed', 'cancelled'];
            $status = $statuses[array_rand($statuses)];
            
            $futureDate = date('Y-m-d H:i:s', strtotime('+' . rand(1, 30) . ' days'));
            $pastDate = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));
            
            $sessionDate = ($status === 'scheduled') ? $futureDate : $pastDate;
            
            $session = [
                'course_id' => $course['id'],
                'title' => 'جلسة تجريبية ' . ($i + 1),
                'description' => 'وصف الجلسة التجريبية رقم ' . ($i + 1),
                'session_date' => $sessionDate,
                'duration' => rand(60, 180), // من 60 إلى 180 دقيقة
                'status' => $status,
                'max_participants' => rand(10, 50),
                'current_participants' => rand(5, 30)
            ];
            
            try {
                $sessionId = $dbClean->insert('sessions', $session);
                echo "<div style='color: blue;'>➕ تم إضافة جلسة #{$sessionId} - {$session['title']} ({$status})</div>";
            } catch (Exception $e) {
                echo "<div style='color: orange;'>⚠️ خطأ في إضافة جلسة: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // إضافة أنشطة تجريبية
    echo "<h2>إضافة أنشطة تجريبية...</h2>";
    
    if (!empty($users)) {
        $activities = [
            'تسجيل دخول المستخدم',
            'تسجيل في كورس جديد',
            'إكمال درس',
            'رفع واجب',
            'مشاهدة فيديو',
            'تحديث الملف الشخصي',
            'إضافة تقييم',
            'مشاركة في منتدى'
        ];
        
        for ($i = 0; $i < 50; $i++) {
            $user = $users[array_rand($users)];
            $activity = $activities[array_rand($activities)];
            
            $randomDate = date('Y-m-d H:i:s', strtotime('-' . rand(1, 7) . ' days'));
            
            $activityLog = [
                'user_id' => $user['id'],
                'activity_type' => 'user_action',
                'description' => $activity,
                'ip_address' => '192.168.1.' . rand(1, 255),
                'user_agent' => 'Mozilla/5.0 (Test Browser)',
                'created_at' => $randomDate
            ];
            
            try {
                $activityId = $dbClean->insert('activity_logs', $activityLog);
                echo "<div style='color: blue;'>➕ تم إضافة نشاط #{$activityId} - {$activity}</div>";
            } catch (Exception $e) {
                echo "<div style='color: orange;'>⚠️ خطأ في إضافة نشاط: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // تحديث أسعار الكورسات
    echo "<h2>تحديث أسعار الكورسات...</h2>";
    
    foreach ($courses as $course) {
        $price = rand(100, 2000);
        $isFree = ($price < 300) ? 1 : 0;
        
        try {
            $dbClean->update('courses', $course['id'], [
                'price' => $price,
                'is_free' => $isFree
            ]);
            echo "<div style='color: blue;'>➕ تم تحديث سعر الكورس #{$course['id']} - {$price} ريال</div>";
        } catch (Exception $e) {
            echo "<div style='color: orange;'>⚠️ خطأ في تحديث سعر الكورس: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<div style='color: green; font-weight: bold; margin-top: 20px;'>🎉 تم إضافة البيانات التجريبية بنجاح!</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div style='color: red;'>📍 التفاصيل: " . $e->getTraceAsString() . "</div>";
}

echo "<br><a href='analytics.php'>عرض التحليلات</a> | <a href='dashboard.php'>لوحة التحكم</a>";
?>
