<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول كمدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$instructor_id = $_SESSION['user_id'];
$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
$error = '';
$success = '';

// التحقق من ملكية الكورس
if ($course_id > 0) {
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $instructor_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: dashboard.php');
        exit;
    }
} else {
    header('Location: dashboard.php');
    exit;
}

// معالجة الإجراءات (قبول/رفض)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $request_id = (int)$_POST['request_id'];
        $action = $_POST['action'];
        $notes = trim($_POST['notes'] ?? '');
        
        if (!in_array($action, ['approve', 'reject'])) {
            throw new Exception('إجراء غير صحيح');
        }
        
        // التحقق من ملكية طلب الانضمام
        $stmt = $conn->prepare("
            SELECT jr.*, c.title as course_title
            FROM join_requests jr
            JOIN courses c ON jr.course_id = c.id
            WHERE jr.id = ? AND c.instructor_id = ? AND jr.status = 'pending'
        ");
        $stmt->execute([$request_id, $instructor_id]);
        $request = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$request) {
            throw new Exception('طلب الانضمام غير موجود أو تم معالجته مسبقاً');
        }

        $new_status = $action === 'approve' ? 'approved' : 'rejected';

        // تحديث حالة الطلب
        $stmt = $conn->prepare("
            UPDATE join_requests
            SET status = ?, processed_at = NOW(), processed_by = ?
            WHERE id = ?
        ");
        $stmt->execute([$new_status, $instructor_id, $request_id]);
        
        // إذا تم القبول، إضافة الطالب للكورس
        if ($action === 'approve') {
            // التحقق من عدم وجود تسجيل سابق
            $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE course_id = ? AND student_id = ?");
            $stmt->execute([$request['course_id'], $request['student_id']]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO course_enrollments (course_id, student_id, enrolled_at, status)
                    VALUES (?, ?, NOW(), 'active')
                ");
                $stmt->execute([$request['course_id'], $request['student_id']]);
            }
        }
        
        $success = $action === 'approve' ? 'تم قبول الطالب وإضافته للكورس' : 'تم رفض طلب الانضمام';
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب طلبات الانضمام
try {
    // التحقق من وجود جدول join_requests
    $stmt = $conn->query("SHOW TABLES LIKE 'join_requests'");
    if ($stmt->rowCount() == 0) {
        throw new Exception('جدول طلبات الانضمام غير موجود');
    }

    $stmt = $conn->prepare("
        SELECT jr.*,
               COALESCE(u.full_name, u.username) as student_name,
               u.email as student_email,
               u.phone as student_phone,
               COALESCE(pu.full_name, pu.username) as processed_by_name
        FROM join_requests jr
        JOIN users u ON jr.student_id = u.id
        LEFT JOIN users pu ON jr.processed_by = pu.id
        WHERE jr.course_id = ?
        ORDER BY
            CASE jr.status
                WHEN 'pending' THEN 1
                WHEN 'approved' THEN 2
                WHEN 'rejected' THEN 3
            END,
            jr.created_at DESC
    ");
    $stmt->execute([$course_id]);
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات
    $pending_count = count(array_filter($requests, function($r) { return $r['status'] === 'pending'; }));
    $approved_count = count(array_filter($requests, function($r) { return $r['status'] === 'approved'; }));
    $rejected_count = count(array_filter($requests, function($r) { return $r['status'] === 'rejected'; }));
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب طلبات الانضمام';
    $requests = [];
    $pending_count = $approved_count = $rejected_count = 0;
}

$pageTitle = 'طلبات الانضمام - ' . $course['title'];
include 'includes/header.php';

?>

<div class="container-fluid py-4">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            طلبات الانضمام للكورس
                        </h5>
                        <small><?php echo htmlspecialchars($course['title']); ?></small>
                    </div>
                    <div>
                        <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> العودة للكورس
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3><?php echo $pending_count; ?></h3>
                    <p class="mb-0">طلبات معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo $approved_count; ?></h3>
                    <p class="mb-0">طلبات مقبولة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3><?php echo $rejected_count; ?></h3>
                    <p class="mb-0">طلبات مرفوضة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3><?php echo count($requests); ?></h3>
                    <p class="mb-0">إجمالي الطلبات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول طلبات الانضمام -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">جميع طلبات الانضمام</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($requests)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد طلبات انضمام</h5>
                            <p class="text-muted">سيتم عرض طلبات الانضمام هنا عندما يرسل الطلاب طلباتهم</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الطالب</th>
                                        <th>معلومات الاتصال</th>
                                        <th>الرسالة</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($requests as $request): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($request['student_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">ID: <?php echo $request['student_id']; ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <i class="fas fa-envelope text-primary me-1"></i>
                                                    <?php echo htmlspecialchars($request['student_email']); ?>
                                                    <?php if ($request['student_phone']): ?>
                                                        <br>
                                                        <i class="fas fa-phone text-success me-1"></i>
                                                        <?php echo htmlspecialchars($request['student_phone']); ?>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($request['message']): ?>
                                                    <small><?php echo htmlspecialchars($request['message']); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">لا توجد رسالة</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = '';
                                                switch($request['status']) {
                                                    case 'pending':
                                                        $status_class = 'warning';
                                                        $status_text = 'معلق';
                                                        break;
                                                    case 'approved':
                                                        $status_class = 'success';
                                                        $status_text = 'مقبول';
                                                        break;
                                                    case 'rejected':
                                                        $status_class = 'danger';
                                                        $status_text = 'مرفوض';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge bg-<?php echo $status_class; ?>">
                                                    <?php echo $status_text; ?>
                                                </span>
                                                <?php if ($request['processed_at']): ?>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo date('Y-m-d', strtotime($request['processed_at'])); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($request['status'] === 'pending'): ?>
                                                    <div class="btn-group-vertical btn-group-sm">
                                                        <button class="btn btn-success btn-sm" 
                                                                onclick="processRequest(<?php echo $request['id']; ?>, 'approve', '<?php echo htmlspecialchars($request['student_name']); ?>')">
                                                            <i class="fas fa-check"></i> قبول
                                                        </button>
                                                        <button class="btn btn-danger btn-sm" 
                                                                onclick="processRequest(<?php echo $request['id']; ?>, 'reject', '<?php echo htmlspecialchars($request['student_name']); ?>')">
                                                            <i class="fas fa-times"></i> رفض
                                                        </button>
                                                    </div>
                                                <?php else: ?>
                                                    <small class="text-muted">
                                                        تم المعالجة
                                                        <?php if ($request['processed_by_name']): ?>
                                                            <br>بواسطة: <?php echo htmlspecialchars($request['processed_by_name']); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج معالجة الطلب -->
<div class="modal fade" id="processModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="processModalTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="request_id" id="processRequestId">
                    <input type="hidden" name="action" id="processAction">
                    
                    <div class="alert" id="processAlert"></div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea class="form-control" name="notes" id="notes" rows="3" 
                                  placeholder="أضف أي ملاحظات للطالب..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn" id="processSubmitBtn"></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function processRequest(requestId, action, studentName) {
    document.getElementById('processRequestId').value = requestId;
    document.getElementById('processAction').value = action;
    
    const modal = document.getElementById('processModal');
    const title = document.getElementById('processModalTitle');
    const alert = document.getElementById('processAlert');
    const submitBtn = document.getElementById('processSubmitBtn');
    
    if (action === 'approve') {
        title.textContent = 'قبول طلب الانضمام';
        alert.className = 'alert alert-success';
        alert.innerHTML = `<i class="fas fa-check-circle me-2"></i>سيتم قبول طلب الطالب <strong>${studentName}</strong> وإضافته للكورس`;
        submitBtn.className = 'btn btn-success';
        submitBtn.innerHTML = '<i class="fas fa-check me-1"></i>قبول الطلب';
    } else {
        title.textContent = 'رفض طلب الانضمام';
        alert.className = 'alert alert-danger';
        alert.innerHTML = `<i class="fas fa-times-circle me-2"></i>سيتم رفض طلب الطالب <strong>${studentName}</strong>`;
        submitBtn.className = 'btn btn-danger';
        submitBtn.innerHTML = '<i class="fas fa-times me-1"></i>رفض الطلب';
    }
    
    new bootstrap.Modal(modal).show();
}
</script>

<?php require_once '../includes/footer.php'; ?>
