<?php
require_once 'includes/simple_db.php';

try {
    echo "<h2>فحص هيكل جدول courses</h2>";
    
    // التحقق من وجود الجدول
    $check_table = $conn->query("SHOW TABLES LIKE 'courses'");
    if ($check_table->rowCount() == 0) {
        echo "<p style='color: red;'>جدول courses غير موجود! سيتم إنشاؤه الآن...</p>";
        
        // إنشاء جدول courses
        $sql = "CREATE TABLE courses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            instructor_id INT,
            category_id INT DEFAULT NULL,
            price DECIMAL(10,2) DEFAULT 0.00,
            duration_hours INT DEFAULT 0,
            max_students INT DEFAULT 50,
            start_date DATE,
            end_date DATE,
            status ENUM('draft', 'active', 'completed', 'cancelled') DEFAULT 'draft',
            image VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✓ تم إنشاء جدول courses بنجاح</p>";
    }
    
    // عرض هيكل الجدول
    $stmt = $conn->query("DESCRIBE courses");
    $columns = $stmt->fetchAll();
    
    echo "<h3>الأعمدة الموجودة في جدول courses:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // التحقق من وجود جدول categories
    $check_categories = $conn->query("SHOW TABLES LIKE 'categories'");
    if ($check_categories->rowCount() == 0) {
        echo "<p style='color: red;'>جدول categories غير موجود! سيتم إنشاؤه الآن...</p>";
        
        // إنشاء جدول categories
        $sql = "CREATE TABLE categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✓ تم إنشاء جدول categories بنجاح</p>";
        
        // إضافة بعض التصنيفات الأساسية
        $categories = [
            'البرمجة وتطوير المواقع',
            'التصميم الجرافيكي',
            'إدارة الأعمال',
            'التسويق الرقمي',
            'الذكاء الاصطناعي',
            'الأمن السيبراني',
            'تطوير التطبيقات',
            'علوم البيانات'
        ];
        
        foreach ($categories as $category) {
            $stmt = $conn->prepare("INSERT INTO categories (name) VALUES (?)");
            $stmt->execute([$category]);
        }
        echo "<p style='color: green;'>✓ تم إضافة التصنيفات الأساسية</p>";
    }
    
    // عرض المدربين المتاحين
    $instructors = $conn->query("SELECT id, name FROM users WHERE role = 'instructor'")->fetchAll();
    echo "<h3>المدربين المتاحين:</h3>";
    if (empty($instructors)) {
        echo "<p style='color: orange;'>لا يوجد مدربين في النظام</p>";
    } else {
        echo "<ul>";
        foreach ($instructors as $instructor) {
            echo "<li>ID: {$instructor['id']} - {$instructor['name']}</li>";
        }
        echo "</ul>";
    }
    
    // عرض التصنيفات المتاحة
    $categories = $conn->query("SELECT id, name FROM categories")->fetchAll();
    echo "<h3>التصنيفات المتاحة:</h3>";
    if (empty($categories)) {
        echo "<p style='color: orange;'>لا توجد تصنيفات في النظام</p>";
    } else {
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li>ID: {$category['id']} - {$category['name']}</li>";
        }
        echo "</ul>";
    }
    
    echo "<h3 style='color: green;'>تم الانتهاء من فحص وإصلاح جداول الكورسات</h3>";
    echo "<p><a href='add-course.php'>اختبار إضافة كورس</a></p>";
    echo "<p><a href='add-instructor.php'>اختبار إضافة مدرب</a></p>";
    echo "<p><a href='dashboard.php'>العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>خطأ: " . $e->getMessage() . "</h3>";
}
?>
