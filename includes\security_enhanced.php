<?php
/**
 * نظام الأمان المحسن والشامل
 * Enhanced and Comprehensive Security System
 * =========================================
 */

/**
 * فئة الأمان الرئيسية
 */
class SecurityManager {
    private static $instance = null;
    private $config;
    private $rateLimiter;
    private $sessionManager;
    
    public function __construct() {
        $this->config = [
            'max_login_attempts' => 5,
            'lockout_duration' => 900, // 15 دقيقة
            'session_timeout' => 3600, // ساعة واحدة
            'csrf_token_lifetime' => 1800, // 30 دقيقة
            'password_min_length' => 8,
            'rate_limit_requests' => 100,
            'rate_limit_window' => 3600, // ساعة واحدة
            'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'mp4', 'avi'],
            'max_file_size' => 50 * 1024 * 1024, // 50MB
            'suspicious_patterns' => [
                '/\b(union|select|insert|update|delete|drop|create|alter)\b/i',
                '/<script[^>]*>.*?<\/script>/i',
                '/javascript:/i',
                '/on\w+\s*=/i'
            ]
        ];
        
        $this->initializeSecurity();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تهيئة الأمان
     */
    private function initializeSecurity() {
        $this->applySecurityHeaders();
        $this->initializeSession();
        $this->checkRateLimit();
        $this->validateRequest();
        $this->setupErrorHandling();
    }
    
    /**
     * تطبيق رؤوس الأمان المحسنة
     */
    public function applySecurityHeaders() {
        // منع XSS
        header('X-XSS-Protection: 1; mode=block');
        
        // منع MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // منع clickjacking
        header('X-Frame-Options: SAMEORIGIN');
        
        // HSTS للمواقع HTTPS
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Permissions Policy
        header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
        
        // CSP محسن
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://www.google.com https://www.gstatic.com; " .
               "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " .
               "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; " .
               "img-src 'self' data: https: blob:; " .
               "media-src 'self' blob:; " .
               "connect-src 'self' https://api.stripe.com https://api.paypal.com; " .
               "frame-src 'self' https://www.youtube.com https://player.vimeo.com https://js.stripe.com; " .
               "object-src 'none'; " .
               "base-uri 'self'; " .
               "form-action 'self'";
        
        header("Content-Security-Policy: $csp");
        
        // إزالة معلومات الخادم
        header_remove('X-Powered-By');
        header_remove('Server');
    }
    
    /**
     * تهيئة الجلسة الآمنة
     */
    private function initializeSession() {
        if (session_status() === PHP_SESSION_NONE) {
            // إعدادات الجلسة الآمنة
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            ini_set('session.cookie_samesite', 'Strict');
            ini_set('session.use_strict_mode', 1);
            ini_set('session.gc_maxlifetime', $this->config['session_timeout']);
            
            session_start();
            
            // تجديد معرف الجلسة دورياً
            if (!isset($_SESSION['last_regeneration'])) {
                $_SESSION['last_regeneration'] = time();
            } elseif (time() - $_SESSION['last_regeneration'] > 300) {
                session_regenerate_id(true);
                $_SESSION['last_regeneration'] = time();
            }
            
            // التحقق من انتهاء صلاحية الجلسة
            if (isset($_SESSION['last_activity']) && 
                (time() - $_SESSION['last_activity'] > $this->config['session_timeout'])) {
                session_unset();
                session_destroy();
                session_start();
            }
            
            $_SESSION['last_activity'] = time();
        }
    }
    
    /**
     * فحص معدل الطلبات
     */
    private function checkRateLimit() {
        $ip = $this->getClientIP();
        $key = "rate_limit_" . md5($ip);
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = [
                'count' => 1,
                'start_time' => time()
            ];
        } else {
            $elapsed = time() - $_SESSION[$key]['start_time'];
            
            if ($elapsed > $this->config['rate_limit_window']) {
                // إعادة تعيين العداد
                $_SESSION[$key] = [
                    'count' => 1,
                    'start_time' => time()
                ];
            } else {
                $_SESSION[$key]['count']++;
                
                if ($_SESSION[$key]['count'] > $this->config['rate_limit_requests']) {
                    $this->logSecurityEvent('rate_limit_exceeded', [
                        'ip' => $ip,
                        'requests' => $_SESSION[$key]['count'],
                        'window' => $this->config['rate_limit_window']
                    ]);
                    
                    http_response_code(429);
                    die('Too Many Requests');
                }
            }
        }
    }
    
    /**
     * التحقق من صحة الطلب
     */
    private function validateRequest() {
        // فحص User Agent
        if (empty($_SERVER['HTTP_USER_AGENT']) || 
            strlen($_SERVER['HTTP_USER_AGENT']) < 10) {
            $this->logSecurityEvent('suspicious_user_agent', [
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'empty',
                'ip' => $this->getClientIP()
            ]);
        }
        
        // فحص الطلبات المشبوهة
        $this->scanForMaliciousInput();
        
        // التحقق من CSRF للطلبات POST
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->validateCSRFToken();
        }
    }
    
    /**
     * فحص المدخلات الضارة
     */
    private function scanForMaliciousInput() {
        $inputs = array_merge($_GET, $_POST, $_COOKIE);
        
        foreach ($inputs as $key => $value) {
            if (is_string($value)) {
                foreach ($this->config['suspicious_patterns'] as $pattern) {
                    if (preg_match($pattern, $value)) {
                        $this->logSecurityEvent('malicious_input_detected', [
                            'key' => $key,
                            'value' => substr($value, 0, 100),
                            'pattern' => $pattern,
                            'ip' => $this->getClientIP()
                        ]);
                        
                        // يمكن إما حظر الطلب أو تنظيف المدخل
                        // http_response_code(400);
                        // die('Malicious input detected');
                    }
                }
            }
        }
    }
    
    /**
     * إنشاء رمز CSRF
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token']) || 
            !isset($_SESSION['csrf_token_time']) ||
            (time() - $_SESSION['csrf_token_time']) > $this->config['csrf_token_lifetime']) {
            
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            $_SESSION['csrf_token_time'] = time();
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * التحقق من رمز CSRF
     */
    public function validateCSRFToken() {
        $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        
        if (empty($token) || 
            !isset($_SESSION['csrf_token']) ||
            !hash_equals($_SESSION['csrf_token'], $token)) {
            
            $this->logSecurityEvent('csrf_token_invalid', [
                'provided_token' => substr($token, 0, 10) . '...',
                'ip' => $this->getClientIP()
            ]);
            
            http_response_code(403);
            die('CSRF token validation failed');
        }
    }
    
    /**
     * تشفير كلمة المرور
     */
    public function hashPassword($password) {
        if (strlen($password) < $this->config['password_min_length']) {
            throw new InvalidArgumentException('Password too short');
        }
        
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 3
        ]);
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * تنظيف المدخلات
     */
    public function sanitizeInput($input, $type = 'string') {
        switch ($type) {
            case 'string':
                return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
            
            case 'email':
                return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
            
            case 'url':
                return filter_var(trim($input), FILTER_SANITIZE_URL);
            
            case 'int':
                return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
            
            case 'float':
                return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            
            case 'html':
                // تنظيف HTML مع السماح ببعض العلامات الآمنة
                $allowed_tags = '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6>';
                return strip_tags($input, $allowed_tags);
            
            default:
                return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        }
    }
    
    /**
     * التحقق من صحة المدخلات
     */
    public function validateInput($input, $type, $options = []) {
        switch ($type) {
            case 'email':
                return filter_var($input, FILTER_VALIDATE_EMAIL) !== false;
            
            case 'url':
                return filter_var($input, FILTER_VALIDATE_URL) !== false;
            
            case 'int':
                $min = $options['min'] ?? null;
                $max = $options['max'] ?? null;
                $flags = 0;
                $filter_options = [];
                
                if ($min !== null) {
                    $filter_options['min_range'] = $min;
                }
                if ($max !== null) {
                    $filter_options['max_range'] = $max;
                }
                
                return filter_var($input, FILTER_VALIDATE_INT, [
                    'options' => $filter_options
                ]) !== false;
            
            case 'float':
                return filter_var($input, FILTER_VALIDATE_FLOAT) !== false;
            
            case 'phone':
                return preg_match('/^[+]?[\d\s\-\(\)]{10,}$/', $input);
            
            case 'password':
                return strlen($input) >= $this->config['password_min_length'] &&
                       preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $input);
            
            default:
                return !empty(trim($input));
        }
    }
    
    /**
     * التحقق من رفع الملفات
     */
    public function validateFileUpload($file) {
        if (!isset($file['error']) || is_array($file['error'])) {
            throw new RuntimeException('Invalid parameters.');
        }
        
        switch ($file['error']) {
            case UPLOAD_ERR_OK:
                break;
            case UPLOAD_ERR_NO_FILE:
                throw new RuntimeException('No file sent.');
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                throw new RuntimeException('Exceeded filesize limit.');
            default:
                throw new RuntimeException('Unknown errors.');
        }
        
        if ($file['size'] > $this->config['max_file_size']) {
            throw new RuntimeException('Exceeded filesize limit.');
        }
        
        $finfo = new finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->file($file['tmp_name']);
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($extension, $this->config['allowed_file_types'])) {
            throw new RuntimeException('Invalid file format.');
        }
        
        // فحص إضافي للصور
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            $imageInfo = getimagesize($file['tmp_name']);
            if ($imageInfo === false) {
                throw new RuntimeException('Invalid image file.');
            }
        }
        
        return true;
    }
    
    /**
     * الحصول على عنوان IP الحقيقي للعميل
     */
    public function getClientIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 
                   'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 
                   'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, 
                    FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * تسجيل أحداث الأمان
     */
    public function logSecurityEvent($event, $data = []) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'user_id' => $_SESSION['user_id'] ?? null,
            'data' => $data
        ];
        
        $logEntry = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        
        // كتابة في ملف السجل
        $logFile = __DIR__ . '/../logs/security_' . date('Y-m-d') . '.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // إرسال تنبيه للمدير في الحالات الخطيرة
        $criticalEvents = ['malicious_input_detected', 'rate_limit_exceeded', 'csrf_token_invalid'];
        if (in_array($event, $criticalEvents)) {
            $this->sendSecurityAlert($event, $logData);
        }
    }
    
    /**
     * إرسال تنبيه أمني
     */
    private function sendSecurityAlert($event, $data) {
        // يمكن تنفيذ إرسال بريد إلكتروني أو إشعار هنا
        error_log("SECURITY ALERT: $event - " . json_encode($data));
    }
    
    /**
     * إعداد معالجة الأخطاء
     */
    private function setupErrorHandling() {
        // إخفاء أخطاء PHP في الإنتاج
        if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
            ini_set('display_errors', 0);
            ini_set('log_errors', 1);
            ini_set('error_log', __DIR__ . '/../logs/php_errors.log');
        }
        
        // معالج أخطاء مخصص
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
    }
    
    /**
     * معالج الأخطاء
     */
    public function handleError($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $this->logSecurityEvent('php_error', [
            'severity' => $severity,
            'message' => $message,
            'file' => $file,
            'line' => $line
        ]);
        
        if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
            // عرض صفحة خطأ عامة
            http_response_code(500);
            include __DIR__ . '/../templates/error_500.php';
            exit;
        }
        
        return false;
    }
    
    /**
     * معالج الاستثناءات
     */
    public function handleException($exception) {
        $this->logSecurityEvent('php_exception', [
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]);
        
        if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
            http_response_code(500);
            include __DIR__ . '/../templates/error_500.php';
            exit;
        }
        
        throw $exception;
    }
}

/**
 * دوال مساعدة سريعة
 */

// تطبيق رؤوس الأمان
function applySecurityHeaders() {
    SecurityManager::getInstance()->applySecurityHeaders();
}

// إنشاء رمز CSRF
function generateCSRFToken() {
    return SecurityManager::getInstance()->generateCSRFToken();
}

// التحقق من رمز CSRF
function validateCSRFToken() {
    return SecurityManager::getInstance()->validateCSRFToken();
}

// تنظيف المدخلات
function sanitizeInput($input, $type = 'string') {
    return SecurityManager::getInstance()->sanitizeInput($input, $type);
}

// التحقق من صحة المدخلات
function validateInput($input, $type, $options = []) {
    return SecurityManager::getInstance()->validateInput($input, $type, $options);
}

// تشفير كلمة المرور
function hashPassword($password) {
    return SecurityManager::getInstance()->hashPassword($password);
}

// التحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return SecurityManager::getInstance()->verifyPassword($password, $hash);
}

// التحقق من رفع الملفات
function validateFileUpload($file) {
    return SecurityManager::getInstance()->validateFileUpload($file);
}

// الحصول على عنوان IP
function getClientIP() {
    return SecurityManager::getInstance()->getClientIP();
}

// تسجيل حدث أمني
function logSecurityEvent($event, $data = []) {
    return SecurityManager::getInstance()->logSecurityEvent($event, $data);
}

// تهيئة الأمان تلقائياً
SecurityManager::getInstance();
?>
