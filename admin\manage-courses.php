<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة الكورسات';
$pageSubtitle = 'إدارة وتتبع جميع الكورسات والمحتوى التعليمي';

// معالجة إضافة كورس جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_course'])) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $instructor_id = (int)$_POST['instructor_id'];

    if (empty($title)) {
        $error = 'يرجى إدخال عنوان الكورس';
    } else {
        try {
            $stmt = $conn->prepare("INSERT INTO courses (title, description, instructor_id) VALUES (?, ?, ?)");
            $stmt->execute([$title, $description, $instructor_id]);
            $success = 'تم إضافة الكورس بنجاح';
            
            // تسجيل النشاط
            logUserActivity($_SESSION['user_id'], 'إضافة كورس', "تم إضافة كورس جديد: $title");
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة الكورس';
        }
    }
}

// جلب قائمة المدربين
$instructors = [];
try {
    $stmt = $conn->query("SELECT id, name, email FROM users WHERE role = 'instructor'");
    $instructors = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب قائمة المدربين';
}

// جلب إحصائيات الكورسات
$stats = [
    'total_courses' => 0,
    'active_courses' => 0,
    'total_enrollments' => 0,
    'total_revenue' => 0,
    'free_courses' => 0,
    'paid_courses' => 0
];

try {
    $stats['total_courses'] = $conn->query("SELECT COUNT(*) FROM courses")->fetchColumn();
    $stats['active_courses'] = $conn->query("SELECT COUNT(*) FROM courses WHERE status = 'active'")->fetchColumn();
    $stats['total_enrollments'] = $conn->query("SELECT COUNT(*) FROM course_enrollments")->fetchColumn();
    $stats['total_revenue'] = $conn->query("SELECT COALESCE(SUM(payment_amount), 0) FROM course_enrollments WHERE payment_status = 'completed'")->fetchColumn();
    $stats['free_courses'] = $conn->query("SELECT COUNT(*) FROM courses WHERE course_type = 'free'")->fetchColumn();
    $stats['paid_courses'] = $conn->query("SELECT COUNT(*) FROM courses WHERE course_type = 'paid'")->fetchColumn();
} catch (Exception $e) {
    error_log("Stats error: " . $e->getMessage());
}

// جلب قائمة الكورسات مع التفاصيل
try {
    $stmt = $conn->prepare("
        SELECT
            c.*,
            u.name as instructor_name,
            u.email as instructor_email,
            COALESCE((SELECT COUNT(*) FROM course_enrollments WHERE course_id = c.id), 0) as student_count,
            COALESCE((SELECT COUNT(*) FROM sessions WHERE course_id = c.id), 0) as session_count,
            COALESCE((SELECT SUM(payment_amount) FROM course_enrollments WHERE course_id = c.id AND payment_status = 'completed'), 0) as total_revenue,
            COALESCE((SELECT AVG(rating) FROM course_reviews WHERE course_id = c.id), 0) as avg_rating,
            COALESCE((SELECT COUNT(*) FROM course_reviews WHERE course_id = c.id), 0) as review_count
        FROM courses c
        LEFT JOIN users u ON c.instructor_id = u.id
        ORDER BY c.created_at DESC
    ");
    $stmt->execute();
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    $error = 'حدث خطأ أثناء جلب قائمة الكورسات';
    $courses = [];
}

require_once 'includes/admin-header.php';
?>

<!-- إحصائيات الكورسات -->
<div class="row mb-4 fade-in-up">
    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">إجمالي الكورسات</h6>
                        <h3 class="mb-0 text-primary"><?php echo number_format($stats['total_courses']); ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-primary bg-soft text-primary">
                            <i class="fas fa-book fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">الكورسات النشطة</h6>
                        <h3 class="mb-0 text-success"><?php echo number_format($stats['active_courses']); ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-success bg-soft text-success">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">إجمالي التسجيلات</h6>
                        <h3 class="mb-0 text-info"><?php echo number_format($stats['total_enrollments']); ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-info bg-soft text-info">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">إجمالي الإيرادات</h6>
                        <h3 class="mb-0 text-warning"><?php echo number_format($stats['total_revenue'], 2); ?> ريال</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-warning bg-soft text-warning">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">كورسات مجانية</h6>
                        <h3 class="mb-0 text-secondary"><?php echo number_format($stats['free_courses']); ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-secondary bg-soft text-secondary">
                            <i class="fas fa-gift fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">كورسات مدفوعة</h6>
                        <h3 class="mb-0 text-danger"><?php echo number_format($stats['paid_courses']); ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-danger bg-soft text-danger">
                            <i class="fas fa-credit-card fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الكورسات -->
<div class="admin-card fade-in-up">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h5 class="mb-0">قائمة الكورسات</h5>
            <small class="text-muted">إدارة وتتبع جميع الكورسات في المنصة</small>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" onclick="exportCourses()">
                <i class="fas fa-download me-2"></i>تصدير
            </button>
            <a href="add-course.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة كورس جديد
            </a>
        </div>
    </div>

    <div class="card-body">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- فلاتر البحث -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث في الكورسات...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="typeFilter">
                    <option value="">جميع الأنواع</option>
                    <option value="free">مجاني</option>
                    <option value="paid">مدفوع</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="instructorFilter">
                    <option value="">جميع المدربين</option>
                    <?php foreach ($instructors as $instructor): ?>
                        <option value="<?php echo htmlspecialchars($instructor['name']); ?>">
                            <?php echo htmlspecialchars($instructor['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                </button>
            </div>
        </div>

        <?php if (empty($courses)): ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-book fa-4x text-muted"></i>
                </div>
                <h5 class="text-muted">لا توجد كورسات متاحة حالياً</h5>
                <p class="text-muted">ابدأ بإضافة كورسات جديدة للمنصة</p>
                <a href="add-course.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة كورس جديد
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="coursesTable">
                    <thead class="table-light">
                        <tr>
                            <th>الكورس</th>
                            <th>المدرب</th>
                            <th>النوع والسعر</th>
                            <th>الطلاب</th>
                            <th>الجلسات</th>
                            <th>الإيرادات</th>
                            <th>التقييم</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($courses as $course): ?>
                            <tr data-course-id="<?php echo $course['id']; ?>">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="course-thumbnail me-3">
                                            <?php if (isset($course['image']) && $course['image']): ?>
                                                <img src="<?php echo htmlspecialchars($course['image']); ?>"
                                                     alt="Course Image" class="rounded" width="50" height="50">
                                            <?php else: ?>
                                                <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-book"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($course['title']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo mb_substr(strip_tags($course['description'] ?? ''), 0, 50); ?>...
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm rounded-circle bg-info text-white me-2 d-flex align-items-center justify-content-center">
                                            <?php echo strtoupper(substr($course['instructor_name'] ?? 'N', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($course['instructor_name'] ?? 'غير محدد'); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($course['instructor_email'] ?? ''); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if (($course['course_type'] ?? 'free') === 'free'): ?>
                                        <span class="badge bg-success">مجاني</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">مدفوع</span>
                                        <br><strong><?php echo number_format($course['price'] ?? 0, 2); ?> ريال</strong>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary rounded-pill"><?php echo $course['student_count']; ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-info rounded-pill"><?php echo $course['session_count']; ?></span>
                                </td>
                                <td>
                                    <strong class="text-success"><?php echo number_format($course['total_revenue'], 2); ?> ريال</strong>
                                </td>
                                <td>
                                    <?php if ($course['avg_rating'] > 0): ?>
                                        <div class="d-flex align-items-center">
                                            <span class="me-1"><?php echo number_format($course['avg_rating'], 1); ?></span>
                                            <div class="text-warning">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star<?php echo $i <= $course['avg_rating'] ? '' : '-o'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        <small class="text-muted">(<?php echo $course['review_count']; ?> تقييم)</small>
                                    <?php else: ?>
                                        <span class="text-muted">لا يوجد تقييم</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (($course['status'] ?? 'inactive') === 'active'): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo date('Y-m-d', strtotime($course['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="course-details.php?id=<?php echo $course['id']; ?>"
                                           class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit-course.php?id=<?php echo $course['id']; ?>"
                                           class="btn btn-sm btn-outline-warning" data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="manage-sessions.php?course_id=<?php echo $course['id']; ?>"
                                           class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="إدارة الجلسات">
                                            <i class="fas fa-video"></i>
                                        </a>
                                        <a href="course-students.php?course_id=<?php echo $course['id']; ?>"
                                           class="btn btn-sm btn-outline-success" data-bs-toggle="tooltip" title="إدارة الطلاب">
                                            <i class="fas fa-users"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger"
                                                onclick="deleteCourse(<?php echo $course['id']; ?>, '<?php echo htmlspecialchars($course['title']); ?>')"
                                                data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.course-thumbnail img {
    object-fit: cover;
}

.bg-soft {
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
}

.bg-success.bg-soft {
    background-color: rgba(var(--bs-success-rgb), 0.1) !important;
}

.bg-info.bg-soft {
    background-color: rgba(var(--bs-info-rgb), 0.1) !important;
}

.bg-warning.bg-soft {
    background-color: rgba(var(--bs-warning-rgb), 0.1) !important;
}

.bg-secondary.bg-soft {
    background-color: rgba(var(--bs-secondary-rgb), 0.1) !important;
}

.bg-danger.bg-soft {
    background-color: rgba(var(--bs-danger-rgb), 0.1) !important;
}

.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 16px;
    font-weight: 600;
}

.table th {
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 6px !important;
    margin: 0 1px;
}
</style>

<script>
$(document).ready(function() {
    // تهيئة DataTables مع فحص عدم إعادة التهيئة
    if (!$.fn.DataTable.isDataTable('#coursesTable')) {
        const table = $('#coursesTable').DataTable({
        order: [[8, 'desc']],
        pageLength: 25,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        columnDefs: [
            { orderable: false, targets: [9] }
        ],
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip'
        });
    }

    // البحث المخصص
    $('#searchInput').on('keyup', function() {
        table.search(this.value).draw();
    });

    // فلتر الحالة
    $('#statusFilter').on('change', function() {
        const status = this.value;
        if (status) {
            table.column(7).search(status).draw();
        } else {
            table.column(7).search('').draw();
        }
    });

    // فلتر النوع
    $('#typeFilter').on('change', function() {
        const type = this.value;
        if (type) {
            table.column(2).search(type).draw();
        } else {
            table.column(2).search('').draw();
        }
    });

    // فلتر المدرب
    $('#instructorFilter').on('change', function() {
        const instructor = this.value;
        if (instructor) {
            table.column(1).search(instructor).draw();
        } else {
            table.column(1).search('').draw();
        }
    });
});

// إعادة تعيين الفلاتر
function resetFilters() {
    $('#searchInput').val('');
    $('#statusFilter').val('');
    $('#typeFilter').val('');
    $('#instructorFilter').val('');
    $('#coursesTable').DataTable().search('').columns().search('').draw();
}

// تصدير البيانات
function exportCourses() {
    Swal.fire({
        title: 'تصدير بيانات الكورسات',
        html: `
            <div class="d-grid gap-2">
                <button class="btn btn-success" onclick="exportData('excel', 'export-courses.php')">
                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                </button>
                <button class="btn btn-danger" onclick="exportData('pdf', 'export-courses.php')">
                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                </button>
                <button class="btn btn-info" onclick="exportData('csv', 'export-courses.php')">
                    <i class="fas fa-file-csv me-2"></i>تصدير CSV
                </button>
            </div>
        `,
        showConfirmButton: false,
        showCloseButton: true,
        width: 300
    });
}

// حذف كورس
function deleteCourse(courseId, courseTitle) {
    Swal.fire({
        title: 'حذف الكورس',
        html: `
            <p>هل أنت متأكد من حذف الكورس <strong>${courseTitle}</strong>؟</p>
            <div class="alert alert-warning text-start">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> سيتم حذف جميع البيانات المرتبطة بهذا الكورس:
                <ul class="mt-2 mb-0">
                    <li>جميع الجلسات</li>
                    <li>جميع التسجيلات</li>
                    <li>جميع التقييمات</li>
                    <li>جميع المواد التعليمية</li>
                </ul>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545',
        input: 'checkbox',
        inputPlaceholder: 'أؤكد أنني أريد حذف هذا الكورس نهائياً'
    }).then((result) => {
        if (result.isConfirmed && result.value) {
            showLoading('جاري حذف الكورس...');

            $.post('ajax/delete-course.php', {
                course_id: courseId
            })
            .done(function(response) {
                hideLoading();
                const data = JSON.parse(response);
                if (data.success) {
                    let detailsHtml = '';
                    if (data.details) {
                        detailsHtml = `
                            <div class="alert alert-info text-start mt-3">
                                <h6>تفاصيل الحذف:</h6>
                                <ul class="mb-0">
                                    <li>الجلسات المحذوفة: ${data.details.sessions_deleted}</li>
                                    <li>التسجيلات المحذوفة: ${data.details.enrollments_deleted}</li>
                                    <li>التقييمات المحذوفة: ${data.details.reviews_deleted}</li>
                                    <li>سجلات الحضور المحذوفة: ${data.details.attendance_deleted}</li>
                                </ul>
                            </div>
                        `;
                    }

                    Swal.fire({
                        title: 'تم الحذف بنجاح!',
                        html: `
                            <p>تم حذف الكورس <strong>${courseTitle}</strong> وجميع بياناته المرتبطة</p>
                            ${detailsHtml}
                        `,
                        icon: 'success',
                        confirmButtonText: 'موافق'
                    }).then(() => {
                        // إزالة الصف من الجدول فوراً باستخدام DataTables
                        const table = $('#coursesTable').DataTable();
                        const row = $(`tr[data-course-id="${courseId}"]`);

                        row.fadeOut(500, function() {
                            table.row(row).remove().draw();
                            updateCoursesCount();
                        });
                    });
                } else {
                    Swal.fire({
                        title: 'خطأ!',
                        text: data.message || 'حدث خطأ أثناء حذف الكورس',
                        icon: 'error'
                    });
                }
            })
            .fail(function() {
                hideLoading();
                Swal.fire({
                    title: 'خطأ!',
                    text: 'حدث خطأ في الاتصال',
                    icon: 'error'
                });
            });
        } else if (result.isConfirmed && !result.value) {
            Swal.fire({
                title: 'مطلوب تأكيد',
                text: 'يجب تأكيد الحذف بوضع علامة في المربع',
                icon: 'info'
            });
        }
    });
}

// تحديث عداد الكورسات
function updateCoursesCount() {
    const totalRows = $('#coursesTable tbody tr').length;
    $('.courses-count').text(totalRows);

    // إذا لم تعد هناك كورسات، أظهر رسالة
    if (totalRows === 0) {
        $('#coursesTable tbody').html(`
            <tr>
                <td colspan="8" class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-book fa-4x text-muted"></i>
                    </div>
                    <h5 class="text-muted">لا توجد كورسات</h5>
                    <p class="text-muted">ابدأ بإضافة كورس جديد</p>
                    <a href="add-course.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة كورس جديد
                    </a>
                </td>
            </tr>
        `);
    }
}

// تغيير حالة الكورس
function toggleCourseStatus(courseId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const statusText = newStatus === 'active' ? 'تفعيل' : 'إلغاء تفعيل';

    Swal.fire({
        title: `${statusText} الكورس`,
        text: `هل أنت متأكد من ${statusText} هذا الكورس؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: `نعم، ${statusText}`,
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            showLoading(`جاري ${statusText} الكورس...`);

            $.post('ajax/toggle-course-status.php', {
                course_id: courseId,
                status: newStatus
            })
            .done(function(response) {
                hideLoading();
                const data = JSON.parse(response);
                if (data.success) {
                    showToast(`تم ${statusText} الكورس بنجاح`, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    Swal.fire({
                        title: 'خطأ!',
                        text: data.message || `حدث خطأ أثناء ${statusText} الكورس`,
                        icon: 'error'
                    });
                }
            })
            .fail(function() {
                hideLoading();
                Swal.fire({
                    title: 'خطأ!',
                    text: 'حدث خطأ في الاتصال',
                    icon: 'error'
                });
            });
        }
    });
}

// عرض إحصائيات الكورس
function showCourseStats(courseId) {
    showLoading('جاري جلب الإحصائيات...');

    $.get('ajax/get-course-stats.php', {
        course_id: courseId
    })
    .done(function(response) {
        hideLoading();
        const data = JSON.parse(response);
        if (data.success) {
            Swal.fire({
                title: 'إحصائيات الكورس',
                html: `
                    <div class="row text-start">
                        <div class="col-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h3 class="text-primary">${data.stats.students}</h3>
                                    <p class="mb-0">الطلاب</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h3 class="text-success">${data.stats.sessions}</h3>
                                    <p class="mb-0">الجلسات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h3 class="text-warning">${data.stats.revenue} ريال</h3>
                                    <p class="mb-0">الإيرادات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h3 class="text-info">${data.stats.rating}</h3>
                                    <p class="mb-0">التقييم</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                showConfirmButton: false,
                showCloseButton: true,
                width: 500
            });
        }
    })
    .fail(function() {
        hideLoading();
        Swal.fire({
            title: 'خطأ!',
            text: 'حدث خطأ في جلب الإحصائيات',
            icon: 'error'
        });
    });
}
</script>

<?php require_once 'includes/admin-footer.php'; ?>
