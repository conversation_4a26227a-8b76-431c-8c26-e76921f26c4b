<?php
require_once 'includes/session_config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 1;

// جلب معلومات الكورس
try {
    $stmt = $conn->prepare("
        SELECT c.*, u.full_name as instructor_name 
        FROM courses c 
        INNER JOIN users u ON c.instructor_id = u.id 
        WHERE c.id = ?
    ");
    $stmt->execute([$course_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $course = null;
}

if (!$course) {
    echo "الكورس غير موجود";
    exit;
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طلب الانضمام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>
                            <i class="fas fa-graduation-cap text-primary me-2"></i>
                            اختبار طلب الانضمام للكورس
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5>معلومات الكورس:</h5>
                            <p><strong>العنوان:</strong> <?php echo htmlspecialchars($course['title']); ?></p>
                            <p><strong>المدرب:</strong> <?php echo htmlspecialchars($course['instructor_name']); ?></p>
                            <p><strong>الوصف:</strong> <?php echo htmlspecialchars($course['description']); ?></p>
                        </div>

                        <div class="alert alert-success">
                            <h5>معلومات المستخدم الحالي:</h5>
                            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($_SESSION['full_name'] ?? $_SESSION['username']); ?></p>
                            <p><strong>الدور:</strong> <?php echo htmlspecialchars($_SESSION['role']); ?></p>
                            <p><strong>ID:</strong> <?php echo $_SESSION['user_id']; ?></p>
                        </div>

                        <form id="joinRequestForm">
                            <input type="hidden" name="course_id" value="<?php echo $course_id; ?>">
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">رسالة للمدرب</label>
                                <textarea class="form-control" id="message" name="message" rows="4" 
                                          placeholder="اكتب سبب رغبتك في الانضمام للكورس...">أرغب في الانضمام لهذا الكورس لتطوير مهاراتي في البرمجة.</textarea>
                            </div>
                            
                            <button type="button" class="btn btn-success" id="submitJoinRequest">
                                <i class="fas fa-paper-plane me-1"></i>إرسال طلب الانضمام
                            </button>
                        </form>

                        <div id="result" class="mt-3"></div>
                    </div>
                </div>

                <!-- عرض طلبات الانضمام الموجودة -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>طلبات الانضمام الموجودة</h5>
                    </div>
                    <div class="card-body">
                        <div id="existingRequests">جاري التحميل...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadExistingRequests();
            
            document.getElementById('submitJoinRequest').addEventListener('click', function() {
                const form = document.getElementById('joinRequestForm');
                const formData = new FormData(form);
                const submitBtn = this;
                const originalText = submitBtn.innerHTML;
                
                // تعطيل الزر وإظهار التحميل
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإرسال...';
                
                fetch('request-join.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    const resultDiv = document.getElementById('result');
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                ${data.message}
                            </div>
                        `;
                        form.reset();
                        loadExistingRequests(); // إعادة تحميل الطلبات
                    } else {
                        resultDiv.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                ${data.message}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('result').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.
                        </div>
                    `;
                })
                .finally(() => {
                    // إعادة تفعيل الزر
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                });
            });
        });

        function loadExistingRequests() {
            fetch(`check-join-requests.php?course_id=<?php echo $course_id; ?>&user_id=<?php echo $_SESSION['user_id']; ?>`)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('existingRequests').innerHTML = data;
                })
                .catch(error => {
                    document.getElementById('existingRequests').innerHTML = 'حدث خطأ في تحميل الطلبات';
                });
        }
    </script>
</body>
</html>
