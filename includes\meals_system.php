<?php
/**
 * نظام الوجبات المحسن
 * Enhanced Meals System
 * ====================
 */

require_once 'config/database.php';
require_once 'includes/security_enhanced.php';

class MealsSystem {
    private $conn;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->createMealsTables();
    }
    
    /**
     * إنشاء جداول الوجبات
     */
    private function createMealsTables() {
        try {
            // جدول فئات الوجبات
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS meal_categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT NULL,
                    icon VARCHAR(50) NULL,
                    color VARCHAR(7) DEFAULT '#667eea',
                    is_active BOOLEAN DEFAULT TRUE,
                    sort_order INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    INDEX idx_active (is_active),
                    INDEX idx_sort (sort_order)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // جدول الوجبات
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS meals (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(200) NOT NULL,
                    description TEXT NULL,
                    category_id INT NOT NULL,
                    image VARCHAR(255) NULL,
                    
                    -- معلومات غذائية
                    calories DECIMAL(8,2) DEFAULT 0,
                    protein DECIMAL(8,2) DEFAULT 0,
                    carbs DECIMAL(8,2) DEFAULT 0,
                    fat DECIMAL(8,2) DEFAULT 0,
                    fiber DECIMAL(8,2) DEFAULT 0,
                    sugar DECIMAL(8,2) DEFAULT 0,
                    sodium DECIMAL(8,2) DEFAULT 0,
                    
                    -- معلومات الوجبة
                    prep_time INT DEFAULT 0, -- بالدقائق
                    cook_time INT DEFAULT 0, -- بالدقائق
                    total_time INT DEFAULT 0, -- بالدقائق
                    servings INT DEFAULT 1,
                    difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',
                    
                    -- تصنيفات إضافية
                    meal_type ENUM('breakfast', 'lunch', 'dinner', 'snack', 'dessert') NOT NULL,
                    diet_type SET('vegetarian', 'vegan', 'gluten_free', 'dairy_free', 'keto', 'low_carb', 'high_protein') NULL,
                    
                    -- معلومات التكلفة
                    cost_per_serving DECIMAL(8,2) DEFAULT 0,
                    currency VARCHAR(3) DEFAULT 'USD',
                    
                    -- تقييم ومراجعات
                    rating DECIMAL(3,2) DEFAULT 0,
                    total_reviews INT DEFAULT 0,
                    
                    -- حالة الوجبة
                    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                    is_featured BOOLEAN DEFAULT FALSE,
                    
                    -- معلومات المؤلف
                    created_by INT NOT NULL,
                    
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (category_id) REFERENCES meal_categories(id) ON DELETE RESTRICT,
                    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
                    
                    INDEX idx_category (category_id),
                    INDEX idx_meal_type (meal_type),
                    INDEX idx_status (status),
                    INDEX idx_featured (is_featured),
                    INDEX idx_rating (rating),
                    INDEX idx_created_by (created_by),
                    FULLTEXT idx_search (name, description)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // جدول مكونات الوجبات
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS meal_ingredients (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    meal_id INT NOT NULL,
                    name VARCHAR(200) NOT NULL,
                    amount DECIMAL(8,2) NOT NULL,
                    unit VARCHAR(50) NOT NULL,
                    notes TEXT NULL,
                    is_optional BOOLEAN DEFAULT FALSE,
                    sort_order INT DEFAULT 0,
                    
                    FOREIGN KEY (meal_id) REFERENCES meals(id) ON DELETE CASCADE,
                    INDEX idx_meal (meal_id),
                    INDEX idx_sort (sort_order)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // جدول خطوات التحضير
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS meal_instructions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    meal_id INT NOT NULL,
                    step_number INT NOT NULL,
                    instruction TEXT NOT NULL,
                    image VARCHAR(255) NULL,
                    time_minutes INT DEFAULT 0,
                    temperature VARCHAR(50) NULL,
                    notes TEXT NULL,
                    
                    FOREIGN KEY (meal_id) REFERENCES meals(id) ON DELETE CASCADE,
                    INDEX idx_meal (meal_id),
                    INDEX idx_step (step_number)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // جدول خطط الوجبات
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS meal_plans (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(200) NOT NULL,
                    description TEXT NULL,
                    duration_days INT NOT NULL,
                    target_calories DECIMAL(8,2) DEFAULT 0,
                    target_protein DECIMAL(8,2) DEFAULT 0,
                    target_carbs DECIMAL(8,2) DEFAULT 0,
                    target_fat DECIMAL(8,2) DEFAULT 0,
                    
                    -- معلومات الخطة
                    plan_type ENUM('weight_loss', 'weight_gain', 'maintenance', 'muscle_building', 'general') DEFAULT 'general',
                    difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
                    
                    -- تكلفة الخطة
                    estimated_cost DECIMAL(10,2) DEFAULT 0,
                    currency VARCHAR(3) DEFAULT 'USD',
                    
                    -- تقييم
                    rating DECIMAL(3,2) DEFAULT 0,
                    total_reviews INT DEFAULT 0,
                    
                    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                    is_featured BOOLEAN DEFAULT FALSE,
                    
                    created_by INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_plan_type (plan_type),
                    INDEX idx_status (status),
                    INDEX idx_featured (is_featured)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // جدول وجبات الخطة
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS meal_plan_meals (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    plan_id INT NOT NULL,
                    meal_id INT NOT NULL,
                    day_number INT NOT NULL,
                    meal_time ENUM('breakfast', 'lunch', 'dinner', 'snack1', 'snack2', 'snack3') NOT NULL,
                    servings DECIMAL(4,2) DEFAULT 1,
                    notes TEXT NULL,
                    
                    FOREIGN KEY (plan_id) REFERENCES meal_plans(id) ON DELETE CASCADE,
                    FOREIGN KEY (meal_id) REFERENCES meals(id) ON DELETE CASCADE,
                    INDEX idx_plan (plan_id),
                    INDEX idx_day (day_number),
                    INDEX idx_meal_time (meal_time)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // جدول مراجعات الوجبات
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS meal_reviews (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    meal_id INT NOT NULL,
                    user_id INT NOT NULL,
                    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                    review_text TEXT NULL,
                    images JSON NULL,
                    
                    -- معلومات إضافية
                    difficulty_rating INT NULL CHECK (difficulty_rating >= 1 AND difficulty_rating <= 5),
                    taste_rating INT NULL CHECK (taste_rating >= 1 AND taste_rating <= 5),
                    would_make_again BOOLEAN DEFAULT TRUE,
                    
                    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (meal_id) REFERENCES meals(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_review (meal_id, user_id),
                    INDEX idx_meal (meal_id),
                    INDEX idx_user (user_id),
                    INDEX idx_rating (rating),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // جدول قوائم التسوق
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS shopping_lists (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    name VARCHAR(200) NOT NULL,
                    description TEXT NULL,
                    total_estimated_cost DECIMAL(10,2) DEFAULT 0,
                    currency VARCHAR(3) DEFAULT 'USD',
                    
                    status ENUM('active', 'completed', 'archived') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_user (user_id),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // جدول عناصر قائمة التسوق
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS shopping_list_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    list_id INT NOT NULL,
                    ingredient_name VARCHAR(200) NOT NULL,
                    amount DECIMAL(8,2) NOT NULL,
                    unit VARCHAR(50) NOT NULL,
                    estimated_cost DECIMAL(8,2) DEFAULT 0,
                    actual_cost DECIMAL(8,2) NULL,
                    is_purchased BOOLEAN DEFAULT FALSE,
                    notes TEXT NULL,
                    
                    FOREIGN KEY (list_id) REFERENCES shopping_lists(id) ON DELETE CASCADE,
                    INDEX idx_list (list_id),
                    INDEX idx_purchased (is_purchased)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // إدراج فئات الوجبات الأساسية
            $this->insertDefaultMealCategories();
            
        } catch (PDOException $e) {
            error_log("Error creating meals tables: " . $e->getMessage());
        }
    }
    
    /**
     * إدراج فئات الوجبات الافتراضية
     */
    private function insertDefaultMealCategories() {
        try {
            $categories = [
                ['name' => 'الإفطار', 'description' => 'وجبات الإفطار الصحية والمغذية', 'icon' => 'fas fa-coffee', 'color' => '#f093fb'],
                ['name' => 'الغداء', 'description' => 'وجبات الغداء المتوازنة', 'icon' => 'fas fa-utensils', 'color' => '#4facfe'],
                ['name' => 'العشاء', 'description' => 'وجبات العشاء الخفيفة والصحية', 'icon' => 'fas fa-moon', 'color' => '#43e97b'],
                ['name' => 'الوجبات الخفيفة', 'description' => 'وجبات خفيفة صحية بين الوجبات', 'icon' => 'fas fa-cookie-bite', 'color' => '#667eea'],
                ['name' => 'الحلويات', 'description' => 'حلويات صحية ولذيذة', 'icon' => 'fas fa-birthday-cake', 'color' => '#764ba2'],
                ['name' => 'المشروبات', 'description' => 'مشروبات صحية ومنعشة', 'icon' => 'fas fa-glass-whiskey', 'color' => '#38f9d7']
            ];
            
            foreach ($categories as $index => $category) {
                $stmt = $this->conn->prepare("
                    INSERT IGNORE INTO meal_categories (name, description, icon, color, sort_order)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $category['name'],
                    $category['description'],
                    $category['icon'],
                    $category['color'],
                    $index + 1
                ]);
            }
        } catch (PDOException $e) {
            error_log("Error inserting default meal categories: " . $e->getMessage());
        }
    }
    
    /**
     * إنشاء وجبة جديدة
     */
    public function createMeal($data) {
        try {
            $this->conn->beginTransaction();
            
            // التحقق من صحة البيانات
            $this->validateMealData($data);
            
            // إدراج الوجبة
            $stmt = $this->conn->prepare("
                INSERT INTO meals (
                    name, description, category_id, image, calories, protein, carbs, fat,
                    prep_time, cook_time, total_time, servings, difficulty, meal_type,
                    diet_type, cost_per_serving, currency, created_by, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['name'],
                $data['description'] ?? null,
                $data['category_id'],
                $data['image'] ?? null,
                $data['calories'] ?? 0,
                $data['protein'] ?? 0,
                $data['carbs'] ?? 0,
                $data['fat'] ?? 0,
                $data['prep_time'] ?? 0,
                $data['cook_time'] ?? 0,
                $data['total_time'] ?? ($data['prep_time'] ?? 0) + ($data['cook_time'] ?? 0),
                $data['servings'] ?? 1,
                $data['difficulty'] ?? 'medium',
                $data['meal_type'],
                $data['diet_type'] ?? null,
                $data['cost_per_serving'] ?? 0,
                $data['currency'] ?? 'USD',
                $data['created_by'],
                $data['status'] ?? 'draft'
            ]);
            
            $mealId = $this->conn->lastInsertId();
            
            // إضافة المكونات
            if (!empty($data['ingredients'])) {
                $this->addMealIngredients($mealId, $data['ingredients']);
            }
            
            // إضافة التعليمات
            if (!empty($data['instructions'])) {
                $this->addMealInstructions($mealId, $data['instructions']);
            }
            
            $this->conn->commit();
            
            return [
                'success' => true,
                'meal_id' => $mealId,
                'message' => 'تم إنشاء الوجبة بنجاح'
            ];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إضافة مكونات الوجبة
     */
    private function addMealIngredients($mealId, $ingredients) {
        foreach ($ingredients as $index => $ingredient) {
            $stmt = $this->conn->prepare("
                INSERT INTO meal_ingredients (meal_id, name, amount, unit, notes, is_optional, sort_order)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $mealId,
                $ingredient['name'],
                $ingredient['amount'],
                $ingredient['unit'],
                $ingredient['notes'] ?? null,
                $ingredient['is_optional'] ?? false,
                $index + 1
            ]);
        }
    }
    
    /**
     * إضافة تعليمات الوجبة
     */
    private function addMealInstructions($mealId, $instructions) {
        foreach ($instructions as $index => $instruction) {
            $stmt = $this->conn->prepare("
                INSERT INTO meal_instructions (meal_id, step_number, instruction, image, time_minutes, temperature, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $mealId,
                $index + 1,
                $instruction['instruction'],
                $instruction['image'] ?? null,
                $instruction['time_minutes'] ?? 0,
                $instruction['temperature'] ?? null,
                $instruction['notes'] ?? null
            ]);
        }
    }
    
    /**
     * الحصول على الوجبات
     */
    public function getMeals($filters = []) {
        try {
            $sql = "
                SELECT m.*, mc.name as category_name, mc.color as category_color,
                       u.name as creator_name,
                       (SELECT COUNT(*) FROM meal_reviews WHERE meal_id = m.id AND status = 'approved') as review_count
                FROM meals m
                LEFT JOIN meal_categories mc ON m.category_id = mc.id
                LEFT JOIN users u ON m.created_by = u.id
                WHERE m.status = 'published'
            ";
            
            $params = [];
            
            // تطبيق الفلاتر
            if (!empty($filters['category_id'])) {
                $sql .= " AND m.category_id = ?";
                $params[] = $filters['category_id'];
            }
            
            if (!empty($filters['meal_type'])) {
                $sql .= " AND m.meal_type = ?";
                $params[] = $filters['meal_type'];
            }
            
            if (!empty($filters['diet_type'])) {
                $sql .= " AND FIND_IN_SET(?, m.diet_type)";
                $params[] = $filters['diet_type'];
            }
            
            if (!empty($filters['difficulty'])) {
                $sql .= " AND m.difficulty = ?";
                $params[] = $filters['difficulty'];
            }
            
            if (!empty($filters['max_time'])) {
                $sql .= " AND m.total_time <= ?";
                $params[] = $filters['max_time'];
            }
            
            if (!empty($filters['search'])) {
                $sql .= " AND (m.name LIKE ? OR m.description LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            // ترتيب النتائج
            $orderBy = $filters['sort_by'] ?? 'rating';
            switch ($orderBy) {
                case 'name':
                    $sql .= " ORDER BY m.name ASC";
                    break;
                case 'time':
                    $sql .= " ORDER BY m.total_time ASC";
                    break;
                case 'calories':
                    $sql .= " ORDER BY m.calories ASC";
                    break;
                case 'newest':
                    $sql .= " ORDER BY m.created_at DESC";
                    break;
                default:
                    $sql .= " ORDER BY m.rating DESC, m.total_reviews DESC";
            }
            
            // تحديد عدد النتائج
            $limit = $filters['limit'] ?? 12;
            $offset = ($filters['page'] ?? 1 - 1) * $limit;
            $sql .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Error getting meals: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على تفاصيل وجبة
     */
    public function getMealDetails($mealId) {
        try {
            // الحصول على معلومات الوجبة
            $stmt = $this->conn->prepare("
                SELECT m.*, mc.name as category_name, mc.color as category_color,
                       u.name as creator_name, u.id as creator_id
                FROM meals m
                LEFT JOIN meal_categories mc ON m.category_id = mc.id
                LEFT JOIN users u ON m.created_by = u.id
                WHERE m.id = ? AND m.status = 'published'
            ");
            $stmt->execute([$mealId]);
            $meal = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$meal) {
                return null;
            }
            
            // الحصول على المكونات
            $stmt = $this->conn->prepare("
                SELECT * FROM meal_ingredients 
                WHERE meal_id = ? 
                ORDER BY sort_order ASC
            ");
            $stmt->execute([$mealId]);
            $meal['ingredients'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // الحصول على التعليمات
            $stmt = $this->conn->prepare("
                SELECT * FROM meal_instructions 
                WHERE meal_id = ? 
                ORDER BY step_number ASC
            ");
            $stmt->execute([$mealId]);
            $meal['instructions'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // الحصول على المراجعات
            $stmt = $this->conn->prepare("
                SELECT mr.*, u.name as user_name
                FROM meal_reviews mr
                LEFT JOIN users u ON mr.user_id = u.id
                WHERE mr.meal_id = ? AND mr.status = 'approved'
                ORDER BY mr.created_at DESC
                LIMIT 10
            ");
            $stmt->execute([$mealId]);
            $meal['reviews'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $meal;
            
        } catch (PDOException $e) {
            error_log("Error getting meal details: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * التحقق من صحة بيانات الوجبة
     */
    private function validateMealData($data) {
        if (empty($data['name'])) {
            throw new Exception('اسم الوجبة مطلوب');
        }
        
        if (empty($data['category_id'])) {
            throw new Exception('فئة الوجبة مطلوبة');
        }
        
        if (empty($data['meal_type'])) {
            throw new Exception('نوع الوجبة مطلوب');
        }
        
        if (empty($data['created_by'])) {
            throw new Exception('معرف المنشئ مطلوب');
        }
    }
    
    /**
     * الحصول على فئات الوجبات
     */
    public function getMealCategories() {
        try {
            $stmt = $this->conn->query("
                SELECT mc.*, COUNT(m.id) as meals_count
                FROM meal_categories mc
                LEFT JOIN meals m ON mc.id = m.category_id AND m.status = 'published'
                WHERE mc.is_active = 1
                GROUP BY mc.id
                ORDER BY mc.sort_order ASC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            return [];
        }
    }
}

// دوال مساعدة سريعة
function createMeal($data) {
    global $conn;
    $mealsSystem = new MealsSystem($conn);
    return $mealsSystem->createMeal($data);
}

function getMeals($filters = []) {
    global $conn;
    $mealsSystem = new MealsSystem($conn);
    return $mealsSystem->getMeals($filters);
}

function getMealDetails($mealId) {
    global $conn;
    $mealsSystem = new MealsSystem($conn);
    return $mealsSystem->getMealDetails($mealId);
}

function getMealCategories() {
    global $conn;
    $mealsSystem = new MealsSystem($conn);
    return $mealsSystem->getMealCategories();
}
?>
