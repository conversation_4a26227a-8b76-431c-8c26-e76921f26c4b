<?php
session_start();
require_once 'includes/simple_db.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$success_message = '';
$error_message = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $request_id = (int)($_POST['request_id'] ?? 0);
    
    if ($action === 'approve' && $request_id > 0) {
        try {
            // جلب تفاصيل الطلب
            $request = fetchOne("SELECT * FROM join_requests WHERE id = ?", [$request_id]);

            if ($request && $request['status'] === 'pending') {
                // تحديث حالة الطلب
                updateRecord('join_requests', $request_id, [
                    'status' => 'approved',
                    'processed_by' => $_SESSION['user_id'],
                    'processed_at' => date('Y-m-d H:i:s')
                ]);

                // إنشاء حساب المستخدم إذا لم يكن موجوداً
                $existing_user = fetchOne("SELECT * FROM users WHERE email = ?", [$request['email']]);

                if (!$existing_user) {
                    // إنشاء كلمة مرور عشوائية
                    $temp_password = bin2hex(random_bytes(8));
                    $hashed_password = password_hash($temp_password, PASSWORD_DEFAULT);

                    // إنشاء اسم مستخدم فريد
                    $username = explode('@', $request['email'])[0];
                    $counter = 1;
                    $original_username = $username;
                    while (true) {
                        $check_user = fetchOne("SELECT * FROM users WHERE username = ?", [$username]);
                        if (!$check_user) {
                            break;
                        }
                        $username = $original_username . $counter;
                        $counter++;
                    }

                    $user_id = insertRecord('users', [
                        'name' => $request['name'],
                        'email' => $request['email'],
                        'username' => $username,
                        'password' => $hashed_password,
                        'phone' => $request['phone'],
                        'role' => 'student',
                        'status' => 'active'
                    ]);

                    // إرسال بريد إلكتروني بكلمة المرور (يمكن تطويره لاحقاً)
                    // sendWelcomeEmail($request['email'], $temp_password);
                } else {
                    $user_id = $existing_user['id'];

                    // تحديث حالة المستخدم
                    updateRecord('users', $user_id, ['status' => 'active']);
                }

                // تسجيل النشاط
                logUserActivity($_SESSION['user_id'], 'admin_action', "تم قبول طلب انضمام {$request['name']}");

                $success_message = 'تم قبول الطلب بنجاح وإنشاء الحساب';
            } else {
                $error_message = 'الطلب غير موجود أو تم معالجته مسبقاً';
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء معالجة الطلب: ' . $e->getMessage();
        }
    } elseif ($action === 'reject' && $request_id > 0) {
        try {
            $rejection_reason = $_POST['rejection_reason'] ?? 'لم يتم تحديد السبب';

            // التحقق من وجود الطلب
            $request = fetchOne("SELECT * FROM join_requests WHERE id = ?", [$request_id]);

            if ($request && $request['status'] === 'pending') {
                updateRecord('join_requests', $request_id, [
                    'status' => 'rejected',
                    'processed_by' => $_SESSION['user_id'],
                    'processed_at' => date('Y-m-d H:i:s'),
                    'rejection_reason' => $rejection_reason
                ]);

                // تسجيل النشاط
                logUserActivity($_SESSION['user_id'], 'admin_action', "تم رفض طلب انضمام رقم $request_id");

                $success_message = 'تم رفض الطلب بنجاح';
            } else {
                $error_message = 'الطلب غير موجود أو تم معالجته مسبقاً';
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء رفض الطلب: ' . $e->getMessage();
        }
    }
}

// جلب طلبات الانضمام
try {
    $filter = $_GET['filter'] ?? 'all';
    $search = $_GET['search'] ?? '';

    // بناء شروط البحث
    $conditions = [];
    if ($filter !== 'all') {
        $conditions['status'] = $filter;
    }

    // جلب طلبات الانضمام مع معلومات المعالج
    if (!empty($search)) {
        $search_term = "%$search%";
        $join_requests = fetchAll("
            SELECT * FROM join_requests
            WHERE (name LIKE ? OR email LIKE ? OR phone LIKE ?)
            " . ($filter !== 'all' ? " AND status = '$filter'" : "") . "
            ORDER BY created_at DESC
        ", [$search_term, $search_term, $search_term]);
    } else {
        if ($filter !== 'all') {
            $join_requests = fetchAll("SELECT * FROM join_requests WHERE status = ? ORDER BY created_at DESC", [$filter]);
        } else {
            $join_requests = fetchAll("SELECT * FROM join_requests ORDER BY created_at DESC");
        }
    }

    // إضافة معلومات المعالج لكل طلب
    foreach ($join_requests as &$request) {
        if ($request['processed_by']) {
            $processor = fetchOne("SELECT name FROM users WHERE id = ?", [$request['processed_by']]);
            $request['processed_by_name'] = $processor['name'] ?? 'غير معروف';
        } else {
            $request['processed_by_name'] = null;
        }
    }

    // إحصائيات
    $stats = [
        'total' => fetchOne("SELECT COUNT(*) as count FROM join_requests")['count'],
        'pending' => fetchOne("SELECT COUNT(*) as count FROM join_requests WHERE status = 'pending'")['count'],
        'approved' => fetchOne("SELECT COUNT(*) as count FROM join_requests WHERE status = 'approved'")['count'],
        'rejected' => fetchOne("SELECT COUNT(*) as count FROM join_requests WHERE status = 'rejected'")['count']
    ];

} catch (Exception $e) {
    $error_message = 'حدث خطأ أثناء جلب طلبات الانضمام: ' . $e->getMessage();
    $join_requests = [];
    $stats = ['total' => 0, 'pending' => 0, 'approved' => 0, 'rejected' => 0];
}

$pageTitle = 'طلبات الانضمام';
$pageSubtitle = 'إدارة ومراجعة طلبات الانضمام للمنصة';
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary text-white mb-3">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['total']); ?></h3>
                    <p class="text-muted mb-0">إجمالي الطلبات</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-warning text-white mb-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['pending']); ?></h3>
                    <p class="text-muted mb-0">قيد المراجعة</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success text-white mb-3">
                        <i class="fas fa-check"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['approved']); ?></h3>
                    <p class="text-muted mb-0">مقبولة</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-danger text-white mb-3">
                        <i class="fas fa-times"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($stats['rejected']); ?></h3>
                    <p class="text-muted mb-0">مرفوضة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات التصفية والبحث -->
    <div class="admin-card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">تصفية حسب الحالة</label>
                    <select name="filter" class="form-select">
                        <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>جميع الطلبات</option>
                        <option value="pending" <?php echo $filter === 'pending' ? 'selected' : ''; ?>>قيد المراجعة</option>
                        <option value="approved" <?php echo $filter === 'approved' ? 'selected' : ''; ?>>مقبولة</option>
                        <option value="rejected" <?php echo $filter === 'rejected' ? 'selected' : ''; ?>>مرفوضة</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="ابحث بالاسم أو البريد الإلكتروني أو رقم الهاتف..."
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block w-100">
                        <i class="fas fa-search me-1"></i>بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة طلبات الانضمام -->
    <div class="admin-card">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-list me-2"></i>طلبات الانضمام (<?php echo count($join_requests); ?>)</h6>
        </div>
        <div class="card-body">
            <?php if (empty($join_requests)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد طلبات انضمام</h5>
                    <p class="text-muted">لم يتم العثور على أي طلبات انضمام تطابق معايير البحث</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="requestsTable">
                        <thead>
                            <tr>
                                <th>المتقدم</th>
                                <th>الدور المطلوب</th>
                                <th>تاريخ التقديم</th>
                                <th>الحالة</th>
                                <th>معالج بواسطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($join_requests as $request): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($request['name'] ?? 'غير محدد'); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($request['email'] ?? 'غير محدد'); ?>
                                            </small>
                                            <?php if (!empty($request['phone'])): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($request['phone']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php
                                            $role = $request['requested_role'] ?? 'student';
                                            echo $role === 'instructor' ? 'مدرب' : 'طالب';
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo !empty($request['created_at']) ? date('Y-m-d H:i', strtotime($request['created_at'])) : 'غير محدد'; ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $request['status'] === 'pending' ? 'warning' : 
                                                ($request['status'] === 'approved' ? 'success' : 'danger'); 
                                        ?>">
                                            <?php
                                            switch($request['status']) {
                                                case 'pending': echo 'قيد المراجعة'; break;
                                                case 'approved': echo 'مقبول'; break;
                                                case 'rejected': echo 'مرفوض'; break;
                                                default: echo $request['status'];
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($request['processed_by_name'])): ?>
                                            <small><?php echo htmlspecialchars($request['processed_by_name']); ?></small>
                                            <br>
                                            <small class="text-muted"><?php echo !empty($request['processed_at']) ? date('Y-m-d H:i', strtotime($request['processed_at'])) : 'غير محدد'; ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($request['status'] === 'pending'): ?>
                                            <button class="btn btn-success btn-sm me-1"
                                                    onclick="approveRequest(<?php echo intval($request['id'] ?? 0); ?>, '<?php echo htmlspecialchars($request['name'] ?? 'غير محدد'); ?>')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-danger btn-sm"
                                                    onclick="rejectRequest(<?php echo intval($request['id'] ?? 0); ?>, '<?php echo htmlspecialchars($request['name'] ?? 'غير محدد'); ?>')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        <?php else: ?>
                                            <button class="btn btn-info btn-sm"
                                                    onclick="viewRequestDetails(<?php echo intval($request['id'] ?? 0); ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal رفض الطلب -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض طلب الانضمام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="rejectForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reject">
                    <input type="hidden" name="request_id" id="rejectRequestId">
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من رفض طلب انضمام <strong id="rejectRequestName"></strong>؟
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">سبب الرفض</label>
                        <textarea class="form-control" name="rejection_reason" rows="3" 
                                  placeholder="اكتب سبب رفض الطلب..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">رفض الطلب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 24px;
}
</style>

<script>
// تهيئة DataTable
$(document).ready(function() {
    $('#requestsTable').DataTable({
        order: [[2, 'desc']],
        pageLength: 25,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        }
    });
});

// موافقة على الطلب
function approveRequest(requestId, requestName) {
    Swal.fire({
        title: 'موافقة على الطلب',
        html: `هل أنت متأكد من الموافقة على طلب انضمام <strong>${requestName}</strong>؟<br><br>سيتم إنشاء حساب جديد للمستخدم.`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم، وافق على الطلب',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#28a745'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="approve">
                <input type="hidden" name="request_id" value="${requestId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// رفض الطلب
function rejectRequest(requestId, requestName) {
    document.getElementById('rejectRequestId').value = requestId;
    document.getElementById('rejectRequestName').textContent = requestName;
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

// عرض تفاصيل الطلب
function viewRequestDetails(requestId) {
    // يمكن تطوير هذه الوظيفة لعرض تفاصيل أكثر
    showToast('سيتم تطوير هذه الميزة قريباً', 'info');
}
</script>

<?php include 'includes/footer.php'; ?>
