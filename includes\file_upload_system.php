<?php
/**
 * نظام رفع الملفات المحسن لمنع التكرار
 * Enhanced File Upload System with Duplicate Prevention
 * ===================================================
 */

require_once 'config/config.php';
require_once 'includes/security_enhanced.php';

class FileUploadSystem {
    private $conn;
    private $allowedTypes;
    private $maxFileSize;
    private $uploadPaths;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->initializeSettings();
        $this->createUploadDirectories();
    }
    
    /**
     * تهيئة الإعدادات
     */
    private function initializeSettings() {
        $this->allowedTypes = [
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
            'document' => ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt'],
            'audio' => ['mp3', 'wav', 'ogg', 'aac']
        ];
        
        $this->maxFileSize = getConfig('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
        
        $this->uploadPaths = [
            'profile' => 'uploads/profiles/',
            'course_thumbnail' => 'uploads/courses/thumbnails/',
            'lesson_video' => 'uploads/courses/videos/',
            'assignment' => 'uploads/assignments/',
            'document' => 'uploads/documents/',
            'temp' => 'uploads/temp/'
        ];
    }
    
    /**
     * إنشاء مجلدات الرفع
     */
    private function createUploadDirectories() {
        foreach ($this->uploadPaths as $path) {
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
                
                // إنشاء ملف .htaccess للحماية
                $htaccess = $path . '.htaccess';
                if (!file_exists($htaccess)) {
                    $content = "Options -Indexes\n";
                    if (strpos($path, 'videos') !== false) {
                        $content .= "AddType video/mp4 .mp4\n";
                        $content .= "AddType video/webm .webm\n";
                    }
                    file_put_contents($htaccess, $content);
                }
            }
        }
    }
    
    /**
     * رفع ملف مع منع التكرار
     */
    public function uploadFile($file, $uploadType, $relatedId = null, $userId = null) {
        try {
            // التحقق من صحة الملف
            $this->validateFile($file);
            
            // حساب hash الملف لمنع التكرار
            $fileHash = hash_file('sha256', $file['tmp_name']);
            
            // البحث عن ملف مطابق
            $existingFile = $this->findExistingFile($fileHash);
            if ($existingFile) {
                return $this->handleDuplicateFile($existingFile, $uploadType, $relatedId, $userId);
            }
            
            // معلومات الملف
            $fileInfo = $this->analyzeFile($file);
            $fileName = $this->generateUniqueFileName($file['name'], $fileInfo['extension']);
            $uploadPath = $this->getUploadPath($uploadType);
            $fullPath = $uploadPath . $fileName;
            
            // رفع الملف
            if (!move_uploaded_file($file['tmp_name'], $fullPath)) {
                throw new Exception('فشل في رفع الملف');
            }
            
            // معالجة الملف حسب النوع
            $processedInfo = $this->processFile($fullPath, $fileInfo);
            
            // حفظ معلومات الملف في قاعدة البيانات
            $fileId = $this->saveFileInfo([
                'original_name' => $file['name'],
                'file_name' => $fileName,
                'file_path' => $fullPath,
                'file_size' => $file['size'],
                'file_type' => $fileInfo['type'],
                'mime_type' => $file['type'],
                'file_hash' => $fileHash,
                'uploaded_by' => $userId,
                'upload_type' => $uploadType,
                'related_id' => $relatedId,
                'processing_status' => 'completed'
            ] + $processedInfo);
            
            return [
                'success' => true,
                'file_id' => $fileId,
                'file_name' => $fileName,
                'file_path' => $fullPath,
                'file_url' => $this->getFileUrl($fullPath),
                'file_info' => $fileInfo + $processedInfo
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * رفع فيديو مع معالجة متقدمة
     */
    public function uploadVideo($file, $courseId, $lessonId = null, $userId = null) {
        try {
            // التحقق من نوع الفيديو
            if (!$this->isVideoFile($file)) {
                throw new Exception('الملف المرفوع ليس فيديو صالح');
            }
            
            // رفع الفيديو
            $result = $this->uploadFile($file, 'lesson_video', $lessonId, $userId);
            
            if (!$result['success']) {
                return $result;
            }
            
            // معالجة الفيديو في الخلفية
            $this->processVideoInBackground($result['file_id'], $result['file_path']);
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * التحقق من صحة الملف
     */
    private function validateFile($file) {
        // التحقق من وجود أخطاء
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception($this->getUploadErrorMessage($file['error']));
        }
        
        // التحقق من حجم الملف
        if ($file['size'] > $this->maxFileSize) {
            throw new Exception('حجم الملف كبير جداً. الحد الأقصى: ' . $this->formatFileSize($this->maxFileSize));
        }
        
        // التحقق من نوع الملف
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!$this->isAllowedExtension($extension)) {
            throw new Exception('نوع الملف غير مسموح: ' . $extension);
        }
        
        // فحص أمني للملف
        $this->securityScanFile($file['tmp_name'], $extension);
    }
    
    /**
     * فحص أمني للملف
     */
    private function securityScanFile($filePath, $extension) {
        // فحص الصور
        if (in_array($extension, $this->allowedTypes['image'])) {
            $imageInfo = getimagesize($filePath);
            if ($imageInfo === false) {
                throw new Exception('الملف ليس صورة صالحة');
            }
        }
        
        // فحص محتوى الملف للبحث عن كود ضار
        $content = file_get_contents($filePath, false, null, 0, 1024);
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                throw new Exception('تم اكتشاف محتوى مشبوه في الملف');
            }
        }
    }
    
    /**
     * تحليل معلومات الملف
     */
    private function analyzeFile($file) {
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $type = $this->getFileType($extension);
        
        $info = [
            'extension' => $extension,
            'type' => $type,
            'size_formatted' => $this->formatFileSize($file['size'])
        ];
        
        // معلومات إضافية للصور
        if ($type === 'image') {
            $imageInfo = getimagesize($file['tmp_name']);
            if ($imageInfo) {
                $info['width'] = $imageInfo[0];
                $info['height'] = $imageInfo[1];
                $info['resolution'] = $imageInfo[0] . 'x' . $imageInfo[1];
            }
        }
        
        return $info;
    }
    
    /**
     * معالجة الملف حسب النوع
     */
    private function processFile($filePath, $fileInfo) {
        $processed = [];
        
        switch ($fileInfo['type']) {
            case 'image':
                $processed = $this->processImage($filePath, $fileInfo);
                break;
                
            case 'video':
                $processed = $this->processVideo($filePath);
                break;
                
            case 'document':
                $processed = $this->processDocument($filePath);
                break;
        }
        
        return $processed;
    }
    
    /**
     * معالجة الصور
     */
    private function processImage($filePath, $fileInfo) {
        $processed = [
            'image_width' => $fileInfo['width'] ?? null,
            'image_height' => $fileInfo['height'] ?? null
        ];
        
        // إنشاء صور مصغرة
        $thumbnails = $this->createThumbnails($filePath, $fileInfo);
        if ($thumbnails) {
            $processed['thumbnails'] = $thumbnails;
        }
        
        return $processed;
    }
    
    /**
     * معالجة الفيديو
     */
    private function processVideo($filePath) {
        $processed = [];
        
        // استخراج معلومات الفيديو باستخدام FFmpeg إذا كان متاحاً
        if ($this->isFFmpegAvailable()) {
            $videoInfo = $this->getVideoInfoWithFFmpeg($filePath);
            $processed = array_merge($processed, $videoInfo);
            
            // إنشاء صورة مصغرة للفيديو
            $thumbnail = $this->createVideoThumbnail($filePath);
            if ($thumbnail) {
                $processed['thumbnail_path'] = $thumbnail;
            }
        }
        
        return $processed;
    }
    
    /**
     * إنشاء صور مصغرة
     */
    private function createThumbnails($imagePath, $fileInfo) {
        $thumbnails = [];
        $sizes = [
            'small' => [150, 150],
            'medium' => [300, 300],
            'large' => [600, 600]
        ];
        
        foreach ($sizes as $sizeName => $dimensions) {
            $thumbnail = $this->resizeImage($imagePath, $dimensions[0], $dimensions[1], $sizeName);
            if ($thumbnail) {
                $thumbnails[$sizeName] = $thumbnail;
            }
        }
        
        return $thumbnails;
    }
    
    /**
     * تغيير حجم الصورة
     */
    private function resizeImage($sourcePath, $maxWidth, $maxHeight, $suffix) {
        try {
            $imageInfo = getimagesize($sourcePath);
            if (!$imageInfo) return null;
            
            $sourceWidth = $imageInfo[0];
            $sourceHeight = $imageInfo[1];
            $mimeType = $imageInfo['mime'];
            
            // حساب الأبعاد الجديدة
            $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
            $newWidth = round($sourceWidth * $ratio);
            $newHeight = round($sourceHeight * $ratio);
            
            // إنشاء الصورة المصدر
            switch ($mimeType) {
                case 'image/jpeg':
                    $sourceImage = imagecreatefromjpeg($sourcePath);
                    break;
                case 'image/png':
                    $sourceImage = imagecreatefrompng($sourcePath);
                    break;
                case 'image/gif':
                    $sourceImage = imagecreatefromgif($sourcePath);
                    break;
                default:
                    return null;
            }
            
            if (!$sourceImage) return null;
            
            // إنشاء الصورة الجديدة
            $newImage = imagecreatetruecolor($newWidth, $newHeight);
            
            // الحفاظ على الشفافية للـ PNG
            if ($mimeType === 'image/png') {
                imagealphablending($newImage, false);
                imagesavealpha($newImage, true);
                $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
                imagefill($newImage, 0, 0, $transparent);
            }
            
            // تغيير الحجم
            imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);
            
            // حفظ الصورة الجديدة
            $pathInfo = pathinfo($sourcePath);
            $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $suffix . '.' . $pathInfo['extension'];
            
            switch ($mimeType) {
                case 'image/jpeg':
                    imagejpeg($newImage, $thumbnailPath, 85);
                    break;
                case 'image/png':
                    imagepng($newImage, $thumbnailPath, 8);
                    break;
                case 'image/gif':
                    imagegif($newImage, $thumbnailPath);
                    break;
            }
            
            // تنظيف الذاكرة
            imagedestroy($sourceImage);
            imagedestroy($newImage);
            
            return $thumbnailPath;
            
        } catch (Exception $e) {
            error_log("Thumbnail creation error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * البحث عن ملف موجود بنفس الـ hash
     */
    private function findExistingFile($fileHash) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM file_uploads 
                WHERE file_hash = ? AND processing_status = 'completed'
                LIMIT 1
            ");
            $stmt->execute([$fileHash]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            return null;
        }
    }
    
    /**
     * التعامل مع الملف المكرر
     */
    private function handleDuplicateFile($existingFile, $uploadType, $relatedId, $userId) {
        // إنشاء رابط جديد للملف الموجود
        $linkId = $this->createFileLink($existingFile['id'], $uploadType, $relatedId, $userId);
        
        return [
            'success' => true,
            'file_id' => $existingFile['id'],
            'link_id' => $linkId,
            'file_name' => $existingFile['file_name'],
            'file_path' => $existingFile['file_path'],
            'file_url' => $this->getFileUrl($existingFile['file_path']),
            'is_duplicate' => true,
            'message' => 'تم استخدام ملف موجود مسبقاً'
        ];
    }
    
    /**
     * إنشاء رابط للملف الموجود
     */
    private function createFileLink($fileId, $uploadType, $relatedId, $userId) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO file_links (file_id, upload_type, related_id, linked_by, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$fileId, $uploadType, $relatedId, $userId]);
            return $this->conn->lastInsertId();
        } catch (PDOException $e) {
            return null;
        }
    }
    
    /**
     * حفظ معلومات الملف
     */
    private function saveFileInfo($fileData) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO file_uploads (
                    original_name, file_name, file_path, file_size, file_type, 
                    mime_type, file_hash, uploaded_by, upload_type, related_id,
                    video_duration, video_resolution, video_bitrate, thumbnail_path,
                    image_width, image_height, processing_status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $fileData['original_name'],
                $fileData['file_name'],
                $fileData['file_path'],
                $fileData['file_size'],
                $fileData['file_type'],
                $fileData['mime_type'],
                $fileData['file_hash'],
                $fileData['uploaded_by'],
                $fileData['upload_type'],
                $fileData['related_id'],
                $fileData['video_duration'] ?? null,
                $fileData['video_resolution'] ?? null,
                $fileData['video_bitrate'] ?? null,
                $fileData['thumbnail_path'] ?? null,
                $fileData['image_width'] ?? null,
                $fileData['image_height'] ?? null,
                $fileData['processing_status']
            ]);
            
            return $this->conn->lastInsertId();
        } catch (PDOException $e) {
            throw new Exception('فشل في حفظ معلومات الملف: ' . $e->getMessage());
        }
    }
    
    /**
     * دوال مساعدة
     */
    private function generateUniqueFileName($originalName, $extension) {
        $baseName = pathinfo($originalName, PATHINFO_FILENAME);
        $baseName = preg_replace('/[^a-zA-Z0-9\-_]/', '', $baseName);
        $timestamp = time();
        $random = bin2hex(random_bytes(8));
        return $baseName . '_' . $timestamp . '_' . $random . '.' . $extension;
    }
    
    private function getUploadPath($uploadType) {
        return $this->uploadPaths[$uploadType] ?? $this->uploadPaths['document'];
    }
    
    private function getFileType($extension) {
        foreach ($this->allowedTypes as $type => $extensions) {
            if (in_array($extension, $extensions)) {
                return $type;
            }
        }
        return 'document';
    }
    
    private function isAllowedExtension($extension) {
        foreach ($this->allowedTypes as $extensions) {
            if (in_array($extension, $extensions)) {
                return true;
            }
        }
        return false;
    }
    
    private function isVideoFile($file) {
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        return in_array($extension, $this->allowedTypes['video']);
    }
    
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    private function getFileUrl($filePath) {
        return str_replace($_SERVER['DOCUMENT_ROOT'], '', $filePath);
    }
    
    private function getUploadErrorMessage($errorCode) {
        $messages = [
            UPLOAD_ERR_INI_SIZE => 'الملف كبير جداً (تجاوز حد الخادم)',
            UPLOAD_ERR_FORM_SIZE => 'الملف كبير جداً (تجاوز حد النموذج)',
            UPLOAD_ERR_PARTIAL => 'تم رفع جزء من الملف فقط',
            UPLOAD_ERR_NO_FILE => 'لم يتم رفع أي ملف',
            UPLOAD_ERR_NO_TMP_DIR => 'مجلد مؤقت مفقود',
            UPLOAD_ERR_CANT_WRITE => 'فشل في كتابة الملف',
            UPLOAD_ERR_EXTENSION => 'امتداد PHP أوقف رفع الملف'
        ];
        
        return $messages[$errorCode] ?? 'خطأ غير معروف في رفع الملف';
    }
    
    /**
     * معالجة الفيديو في الخلفية
     */
    private function processVideoInBackground($fileId, $filePath) {
        // يمكن تنفيذ هذا باستخدام queue system أو cron job
        // هنا سنقوم بمعالجة بسيطة
        
        if ($this->isFFmpegAvailable()) {
            // تحديث حالة المعالجة
            $this->updateProcessingStatus($fileId, 'processing');
            
            try {
                // استخراج معلومات الفيديو
                $videoInfo = $this->getVideoInfoWithFFmpeg($filePath);
                
                // إنشاء thumbnail
                $thumbnail = $this->createVideoThumbnail($filePath);
                
                // تحديث قاعدة البيانات
                $this->updateVideoInfo($fileId, $videoInfo, $thumbnail);
                
                // تحديث حالة المعالجة
                $this->updateProcessingStatus($fileId, 'completed');
                
            } catch (Exception $e) {
                $this->updateProcessingStatus($fileId, 'failed', $e->getMessage());
            }
        }
    }
    
    private function isFFmpegAvailable() {
        $output = shell_exec('ffmpeg -version 2>&1');
        return strpos($output, 'ffmpeg version') !== false;
    }
    
    private function getVideoInfoWithFFmpeg($filePath) {
        $command = "ffprobe -v quiet -print_format json -show_format -show_streams " . escapeshellarg($filePath);
        $output = shell_exec($command);
        $data = json_decode($output, true);
        
        $info = [];
        
        if (isset($data['format']['duration'])) {
            $info['video_duration'] = (int)$data['format']['duration'];
        }
        
        if (isset($data['streams'])) {
            foreach ($data['streams'] as $stream) {
                if ($stream['codec_type'] === 'video') {
                    $info['video_resolution'] = $stream['width'] . 'x' . $stream['height'];
                    $info['video_bitrate'] = isset($stream['bit_rate']) ? (int)$stream['bit_rate'] : null;
                    break;
                }
            }
        }
        
        return $info;
    }
    
    private function createVideoThumbnail($filePath) {
        $pathInfo = pathinfo($filePath);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.jpg';
        
        $command = "ffmpeg -i " . escapeshellarg($filePath) . " -ss 00:00:01 -vframes 1 -y " . escapeshellarg($thumbnailPath) . " 2>&1";
        $output = shell_exec($command);
        
        return file_exists($thumbnailPath) ? $thumbnailPath : null;
    }
    
    private function updateProcessingStatus($fileId, $status, $error = null) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE file_uploads 
                SET processing_status = ?, processing_error = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$status, $error, $fileId]);
        } catch (PDOException $e) {
            error_log("Failed to update processing status: " . $e->getMessage());
        }
    }
    
    private function updateVideoInfo($fileId, $videoInfo, $thumbnail) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE file_uploads 
                SET video_duration = ?, video_resolution = ?, video_bitrate = ?, thumbnail_path = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([
                $videoInfo['video_duration'] ?? null,
                $videoInfo['video_resolution'] ?? null,
                $videoInfo['video_bitrate'] ?? null,
                $thumbnail,
                $fileId
            ]);
        } catch (PDOException $e) {
            error_log("Failed to update video info: " . $e->getMessage());
        }
    }
    
    /**
     * حذف ملف
     */
    public function deleteFile($fileId, $userId = null) {
        try {
            // الحصول على معلومات الملف
            $stmt = $this->conn->prepare("SELECT * FROM file_uploads WHERE id = ?");
            $stmt->execute([$fileId]);
            $file = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$file) {
                throw new Exception('الملف غير موجود');
            }
            
            // التحقق من الصلاحيات
            if ($userId && $file['uploaded_by'] != $userId) {
                throw new Exception('ليس لديك صلاحية لحذف هذا الملف');
            }
            
            // التحقق من وجود روابط أخرى للملف
            $stmt = $this->conn->prepare("SELECT COUNT(*) FROM file_links WHERE file_id = ?");
            $stmt->execute([$fileId]);
            $linkCount = $stmt->fetchColumn();
            
            if ($linkCount > 0) {
                // لا نحذف الملف الفعلي، فقط نحذف الرابط
                return ['success' => true, 'message' => 'تم حذف الرابط، الملف محفوظ للاستخدامات الأخرى'];
            }
            
            // حذف الملف الفعلي
            if (file_exists($file['file_path'])) {
                unlink($file['file_path']);
            }
            
            // حذف الصور المصغرة إن وجدت
            if ($file['thumbnail_path'] && file_exists($file['thumbnail_path'])) {
                unlink($file['thumbnail_path']);
            }
            
            // حذف من قاعدة البيانات
            $stmt = $this->conn->prepare("DELETE FROM file_uploads WHERE id = ?");
            $stmt->execute([$fileId]);
            
            return ['success' => true, 'message' => 'تم حذف الملف بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}

// إنشاء جدول روابط الملفات إذا لم يكن موجوداً
function createFileLinksTable($conn) {
    try {
        $sql = "CREATE TABLE IF NOT EXISTS file_links (
            id INT AUTO_INCREMENT PRIMARY KEY,
            file_id INT NOT NULL,
            upload_type VARCHAR(50) NOT NULL,
            related_id INT NULL,
            linked_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (file_id) REFERENCES file_uploads(id) ON DELETE CASCADE,
            FOREIGN KEY (linked_by) REFERENCES users(id) ON DELETE CASCADE,
            
            INDEX idx_file (file_id),
            INDEX idx_related (related_id),
            INDEX idx_linked_by (linked_by)
        ) ENGINE=InnoDB";
        
        $conn->exec($sql);
    } catch (PDOException $e) {
        error_log("Error creating file_links table: " . $e->getMessage());
    }
}

// دوال مساعدة سريعة
function uploadFile($file, $uploadType, $relatedId = null, $userId = null) {
    global $conn;
    $uploadSystem = new FileUploadSystem($conn);
    return $uploadSystem->uploadFile($file, $uploadType, $relatedId, $userId);
}

function uploadVideo($file, $courseId, $lessonId = null, $userId = null) {
    global $conn;
    $uploadSystem = new FileUploadSystem($conn);
    return $uploadSystem->uploadVideo($file, $courseId, $lessonId, $userId);
}

function deleteFile($fileId, $userId = null) {
    global $conn;
    $uploadSystem = new FileUploadSystem($conn);
    return $uploadSystem->deleteFile($fileId, $userId);
}
?>
