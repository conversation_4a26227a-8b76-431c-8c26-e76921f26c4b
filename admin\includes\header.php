<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>لوحة التحكم الإدارية</title>
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --sidebar-width: 280px;
            --header-height: 70px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Sidebar Styles */
        .admin-sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.2);
        }

        .sidebar-logo {
            color: white;
            font-size: 24px;
            font-weight: 700;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .sidebar-logo i {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 28px;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-section {
            margin-bottom: 30px;
        }

        .menu-section-title {
            color: #94a3b8;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 20px 10px;
            margin-bottom: 10px;
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }

        .menu-item {
            display: block;
            color: #cbd5e1;
            text-decoration: none;
            padding: 12px 20px;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
            position: relative;
        }

        .menu-item:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: var(--primary-color);
        }

        .menu-item.active {
            background: linear-gradient(90deg, rgba(37,99,235,0.2), transparent);
            color: white;
            border-right-color: var(--primary-color);
        }

        .menu-item i {
            width: 20px;
            margin-left: 12px;
            text-align: center;
        }

        .menu-badge {
            background: var(--danger-color);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-right: auto;
        }

        /* Main Content */
        .admin-main {
            margin-right: var(--sidebar-width);
            min-height: 100vh;
            background: var(--light-color);
        }

        /* Top Header */
        .admin-header {
            background: white;
            height: var(--header-height);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .sidebar-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 20px;
            color: var(--dark-color);
            cursor: pointer;
        }

        .breadcrumb-nav {
            background: none;
            padding: 0;
            margin: 0;
        }

        .breadcrumb-nav .breadcrumb-item {
            color: var(--secondary-color);
        }

        .breadcrumb-nav .breadcrumb-item.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-notifications {
            position: relative;
            background: none;
            border: none;
            font-size: 18px;
            color: var(--secondary-color);
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .header-notifications:hover {
            background: var(--light-color);
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            left: 0;
            background: var(--danger-color);
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        .user-dropdown {
            position: relative;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .user-info:hover {
            background: var(--light-color);
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--primary-color), var(--info-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .user-details h6 {
            margin: 0;
            font-size: 14px;
            color: var(--dark-color);
        }

        .user-details small {
            color: var(--secondary-color);
        }

        /* Content Area */
        .admin-content {
            padding: 30px;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 25px 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--dark-color);
            margin: 0;
        }

        .page-subtitle {
            color: var(--secondary-color);
            margin: 5px 0 0;
        }

        /* Cards */
        .admin-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: none;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .admin-card .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            border: none;
            padding: 20px 25px;
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(100%);
            }

            .admin-sidebar.show {
                transform: translateX(0);
            }

            .admin-main {
                margin-right: 0;
            }

            .sidebar-toggle {
                display: block;
            }

            .admin-content {
                padding: 20px 15px;
            }
        }

        /* Custom Scrollbar */
        .admin-sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .admin-sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .admin-sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.5s ease;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-header">
            <a href="dashboard.php" class="sidebar-logo">
                <i class="fas fa-graduation-cap"></i>
                <span>منصة التعلم</span>
            </a>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-section">
                <div class="menu-section-title">الرئيسية</div>
                <a href="dashboard.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
                <a href="analytics.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'analytics.php' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-line"></i>
                    التحليلات والإحصائيات
                </a>
            </div>

            <div class="menu-section">
                <div class="menu-section-title">إدارة المستخدمين</div>
                <a href="manage-users.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'manage-users.php' ? 'active' : ''; ?>">
                    <i class="fas fa-users"></i>
                    جميع المستخدمين
                </a>
                <a href="manage-instructors.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'manage-instructors.php' ? 'active' : ''; ?>">
                    <i class="fas fa-chalkboard-teacher"></i>
                    المدربين
                </a>
                <a href="manage-students.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'manage-students.php' ? 'active' : ''; ?>">
                    <i class="fas fa-user-graduate"></i>
                    الطلاب
                </a>
                <a href="join-requests.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'join-requests.php' ? 'active' : ''; ?>">
                    <i class="fas fa-user-plus"></i>
                    طلبات الانضمام
                    <?php
                    try {
                        $pending_requests = $conn->query("SELECT COUNT(*) FROM join_requests WHERE status = 'pending'")->fetchColumn();
                        if ($pending_requests > 0) {
                            echo "<span class='menu-badge'>$pending_requests</span>";
                        }
                    } catch (Exception $e) {}
                    ?>
                </a>
            </div>

            <div class="menu-section">
                <div class="menu-section-title">إدارة المحتوى</div>
                <a href="manage-courses.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'manage-courses.php' ? 'active' : ''; ?>">
                    <i class="fas fa-book"></i>
                    الكورسات
                </a>
                <a href="manage-categories.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'manage-categories.php' ? 'active' : ''; ?>">
                    <i class="fas fa-tags"></i>
                    التصنيفات
                </a>
                <a href="manage-sessions.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'manage-sessions.php' ? 'active' : ''; ?>">
                    <i class="fas fa-video"></i>
                    الجلسات
                </a>
            </div>

            <div class="menu-section">
                <div class="menu-section-title">المالية</div>
                <a href="financial-reports.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'financial-reports.php' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-pie"></i>
                    التقارير المالية
                </a>
                <a href="payments.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'payments.php' ? 'active' : ''; ?>">
                    <i class="fas fa-credit-card"></i>
                    المدفوعات
                </a>
                <a href="commissions.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'commissions.php' ? 'active' : ''; ?>">
                    <i class="fas fa-percentage"></i>
                    العمولات
                </a>
            </div>

            <div class="menu-section">
                <div class="menu-section-title">النظام</div>
                <a href="activity-logs.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'activity-logs.php' ? 'active' : ''; ?>">
                    <i class="fas fa-history"></i>
                    سجل الأنشطة
                </a>
                <a href="system-settings.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'system-settings.php' ? 'active' : ''; ?>">
                    <i class="fas fa-cog"></i>
                    إعدادات النظام
                </a>
                <a href="backup.php" class="menu-item <?php echo basename($_SERVER['PHP_SELF']) == 'backup.php' ? 'active' : ''; ?>">
                    <i class="fas fa-database"></i>
                    النسخ الاحتياطي
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="admin-main">
        <!-- Top Header -->
        <div class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <nav class="breadcrumb-nav">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">الرئيسية</a></li>
                        <?php if (isset($pageTitle) && $pageTitle != 'لوحة التحكم'): ?>
                            <li class="breadcrumb-item active"><?php echo $pageTitle; ?></li>
                        <?php endif; ?>
                    </ol>
                </nav>
            </div>

            <div class="header-right">
                <button class="header-notifications" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>

                <div class="user-dropdown">
                    <div class="user-info" onclick="toggleUserMenu()">
                        <div class="user-avatar">
                            <?php echo strtoupper(substr($_SESSION['user_name'] ?? 'A', 0, 1)); ?>
                        </div>
                        <div class="user-details">
                            <h6><?php echo $_SESSION['user_name'] ?? 'المدير'; ?></h6>
                            <small>مدير النظام</small>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Content -->
        <div class="admin-content">
            <?php if (isset($pageTitle)): ?>
                <div class="page-header fade-in-up">
                    <h1 class="page-title"><?php echo $pageTitle; ?></h1>
                    <?php if (isset($pageSubtitle)): ?>
                        <p class="page-subtitle"><?php echo $pageSubtitle; ?></p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- DataTables Fix Scripts -->
    <script src="assets/js/datatables-fix.js"></script>
    <script src="assets/js/datatables-final-fix.js"></script>

    <script>
        // Sidebar Toggle
        function toggleSidebar() {
            document.getElementById('adminSidebar').classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('adminSidebar');
            const toggle = document.querySelector('.sidebar-toggle');
            
            if (window.innerWidth <= 768 && !sidebar.contains(e.target) && !toggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        });

        // Notifications
        function showNotifications() {
            Swal.fire({
                title: 'الإشعارات',
                html: `
                    <div class="text-start">
                        <div class="alert alert-info">
                            <i class="fas fa-user-plus"></i>
                            طلب انضمام جديد من أحمد محمد
                        </div>
                        <div class="alert alert-success">
                            <i class="fas fa-check"></i>
                            تم إنشاء كورس جديد بنجاح
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            تحديث النظام متاح
                        </div>
                    </div>
                `,
                showConfirmButton: false,
                showCloseButton: true,
                width: 500
            });
        }

        // User Menu
        function toggleUserMenu() {
            Swal.fire({
                title: 'قائمة المستخدم',
                html: `
                    <div class="d-grid gap-2">
                        <a href="./profile.php" class="btn btn-outline-primary">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a>
                        <a href="./settings.php" class="btn btn-outline-secondary">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a>
                        <hr>
                        <a href="../logout.php" class="btn btn-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </div>
                `,
                showConfirmButton: false,
                showCloseButton: true,
                width: 300
            });
        }

        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 500);
                }
            });
        }, 5000);
    </script>
