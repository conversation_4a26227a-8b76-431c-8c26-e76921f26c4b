<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$success = '';
$error = '';

// التحقق من معرف الجلسة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: sessions.php');
    exit;
}

$session_id = (int)$_GET['id'];

// معالجة حذف الجلسة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_session') {
    try {
        // التحقق من أن الجلسة تخص المدرب الحالي
        $stmt = $conn->prepare("
            SELECT s.id 
            FROM sessions s 
            JOIN courses c ON s.course_id = c.id 
            WHERE s.id = ? AND c.instructor_id = ?
        ");
        $stmt->execute([$session_id, $_SESSION['user_id']]);
        
        if ($stmt->rowCount() > 0) {
            // حذف الجلسة
            $stmt = $conn->prepare("DELETE FROM sessions WHERE id = ?");
            $stmt->execute([$session_id]);
            
            $_SESSION['success_message'] = 'تم حذف الجلسة بنجاح';
            header('Location: sessions.php');
            exit;
        } else {
            $error = 'لا يمكنك حذف هذه الجلسة';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء حذف الجلسة';
        error_log($e->getMessage());
    }
}

// جلب تفاصيل الجلسة
try {
    $stmt = $conn->prepare("
        SELECT s.*, c.title as course_title, c.id as course_id,
               u.name as instructor_name
        FROM sessions s
        JOIN courses c ON s.course_id = c.id
        JOIN users u ON c.instructor_id = u.id
        WHERE s.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$session_id, $_SESSION['user_id']]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        header('Location: sessions.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب تفاصيل الجلسة';
    error_log($e->getMessage());
}

// جلب قائمة الحضور
try {
    $stmt = $conn->prepare("
        SELECT sa.*, u.name as student_name, u.email as student_email
        FROM session_attendees sa
        JOIN users u ON sa.user_id = u.id
        WHERE sa.session_id = ?
        ORDER BY sa.join_time DESC
    ");
    $stmt->execute([$session_id]);
    $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $attendees = [];
    error_log($e->getMessage());
}

$pageTitle = 'تفاصيل الجلسة';
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $success; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- تفاصيل الجلسة -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-video me-2"></i>
                        <?php echo htmlspecialchars($session['title']); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات الجلسة</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>الكورس:</strong></td>
                                    <td><?php echo htmlspecialchars($session['course_title']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ البدء:</strong></td>
                                    <td><?php echo date('Y-m-d', strtotime($session['start_time'])); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>وقت البدء:</strong></td>
                                    <td><?php echo date('H:i', strtotime($session['start_time'])); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>المدة:</strong></td>
                                    <td><?php echo $session['duration']; ?> دقيقة</td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        <?php
                                        $now = new DateTime();
                                        $start = new DateTime($session['start_time']);
                                        $end = clone $start;
                                        $end->add(new DateInterval('PT' . $session['duration'] . 'M'));
                                        
                                        if ($now < $start) {
                                            echo '<span class="badge bg-info">قادمة</span>';
                                        } elseif ($now >= $start && $now <= $end) {
                                            echo '<span class="badge bg-success">جارية</span>';
                                        } else {
                                            echo '<span class="badge bg-secondary">منتهية</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات الاجتماع</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>معرف الاجتماع:</strong></td>
                                    <td><?php echo htmlspecialchars($session['zoom_meeting_id'] ?? 'غير متوفر'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>كلمة المرور:</strong></td>
                                    <td><?php echo htmlspecialchars($session['zoom_meeting_password'] ?? 'غير متوفر'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>عدد الحضور:</strong></td>
                                    <td><?php echo count($attendees); ?> طالب</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if ($session['description']): ?>
                    <div class="mt-3">
                        <h6 class="text-muted">وصف الجلسة</h6>
                        <p><?php echo nl2br(htmlspecialchars($session['description'])); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- الإجراءات -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">الإجراءات</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <!-- رابط الانضمام -->
                        <?php
                        $zoom_link = $session['zoom_join_url'] ?? null;
                        if (!$zoom_link && $session['zoom_meeting_id']) {
                            $zoom_link = 'https://zoom.us/j/' . $session['zoom_meeting_id'];
                        }
                        ?>

                        <?php if ($zoom_link): ?>
                        <a href="<?php echo htmlspecialchars($zoom_link); ?>"
                           target="_blank" class="btn btn-primary btn-lg">
                            <i class="fas fa-video me-2"></i>انضمام للجلسة المباشرة
                        </a>

                        <!-- نسخ الرابط -->
                        <button type="button" class="btn btn-outline-primary" onclick="copyLink('<?php echo htmlspecialchars($zoom_link); ?>')">
                            <i class="fas fa-copy me-2"></i>نسخ الرابط
                        </button>

                        <!-- مشاركة الرابط -->
                        <div class="mt-3">
                            <label class="form-label fw-bold">رابط المشاركة للطلاب:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="shareLink"
                                       value="<?php echo htmlspecialchars($zoom_link); ?>" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyLink('<?php echo htmlspecialchars($zoom_link); ?>')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <small class="text-muted">يمكنك مشاركة هذا الرابط مع الطلاب للانضمام للجلسة</small>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لم يتم إضافة رابط البث بعد
                        </div>
                        <?php endif; ?>
                        
                        <!-- تعديل الجلسة -->
                        <a href="edit-session.php?id=<?php echo $session['id']; ?>" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>تعديل الجلسة
                        </a>
                        
                        <!-- حذف الجلسة -->
                        <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash me-2"></i>حذف الجلسة
                        </button>
                        
                        <!-- العودة للقائمة -->
                        <a href="sessions.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="card shadow-sm mt-3">
                <div class="card-header">
                    <h6 class="mb-0">إحصائيات</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary"><?php echo count($attendees); ?></h4>
                                <small class="text-muted">الحضور</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo $session['duration']; ?></h4>
                            <small class="text-muted">دقيقة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- قائمة الحضور -->
    <?php if (!empty($attendees)): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">قائمة الحضور</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الطالب</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>وقت الانضمام</th>
                                    <th>وقت المغادرة</th>
                                    <th>مدة الحضور</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($attendees as $attendee): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($attendee['student_name']); ?></td>
                                    <td><?php echo htmlspecialchars($attendee['student_email']); ?></td>
                                    <td>
                                        <?php echo $attendee['join_time'] ? date('H:i:s', strtotime($attendee['join_time'])) : '-'; ?>
                                    </td>
                                    <td>
                                        <?php echo $attendee['leave_time'] ? date('H:i:s', strtotime($attendee['leave_time'])) : 'لا يزال متصل'; ?>
                                    </td>
                                    <td>
                                        <?php 
                                        if ($attendee['join_time'] && $attendee['leave_time']) {
                                            $join = new DateTime($attendee['join_time']);
                                            $leave = new DateTime($attendee['leave_time']);
                                            $duration = $join->diff($leave);
                                            echo $duration->format('%H:%I:%S');
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Modal حذف الجلسة -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد حذف الجلسة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">هل أنت متأكد من حذف هذه الجلسة؟</h5>
                    <p class="text-muted">
                        <strong><?php echo htmlspecialchars($session['title']); ?></strong><br>
                        هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بيانات الحضور المرتبطة بالجلسة.
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete_session">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>حذف الجلسة
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// تأكيد الحذف
document.querySelector('form[method="POST"]').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد من حذف هذه الجلسة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        e.preventDefault();
    }
});

// نسخ الرابط
function copyLink(link) {
    navigator.clipboard.writeText(link).then(function() {
        // إظهار رسالة نجاح
        showNotification('تم نسخ الرابط بنجاح!', 'success');
    }).catch(function(err) {
        // طريقة بديلة للنسخ
        const textArea = document.createElement('textarea');
        textArea.value = link;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('تم نسخ الرابط بنجاح!', 'success');
    });
}

// دالة لإظهار الإشعارات
function showNotification(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const icon = type === 'error' ? 'fas fa-exclamation-circle' :
                type === 'success' ? 'fas fa-check-circle' :
                type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.notification-toast');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed notification-toast`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 350px; max-width: 500px;';
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="${icon} me-2"></i>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 150);
        }
    }, 3000);
}
</script>

<?php include '../includes/footer.php'; ?>
