<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$course_id = $_GET['course_id'] ?? 0;

if (!$course_id) {
    header('Location: manage-courses.php');
    exit;
}

// جلب معلومات الكورس
try {
    $stmt = $conn->prepare("
        SELECT c.*, u.name as instructor_name 
        FROM courses c 
        LEFT JOIN users u ON c.instructor_id = u.id 
        WHERE c.id = ?
    ");
    $stmt->execute([$course_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: manage-courses.php?error=' . urlencode('الكورس غير موجود'));
        exit;
    }
} catch (Exception $e) {
    header('Location: manage-courses.php?error=' . urlencode('حدث خطأ في جلب بيانات الكورس'));
    exit;
}

// معالجة إضافة طالب جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_student'])) {
    $student_email = trim($_POST['student_email']);
    $payment_amount = (float)$_POST['payment_amount'];
    $payment_status = $_POST['payment_status'];
    
    if (empty($student_email)) {
        $error = 'يرجى إدخال بريد الطالب الإلكتروني';
    } else {
        try {
            // البحث عن الطالب
            $stmt = $conn->prepare("SELECT id, name FROM users WHERE email = ? AND role = 'student'");
            $stmt->execute([$student_email]);
            $student = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$student) {
                $error = 'الطالب غير موجود أو ليس له دور طالب';
            } else {
                // التحقق من عدم تسجيله مسبقاً
                $stmt = $conn->prepare("SELECT id FROM course_enrollments WHERE student_id = ? AND course_id = ?");
                $stmt->execute([$student['id'], $course_id]);
                
                if ($stmt->rowCount() > 0) {
                    $error = 'الطالب مسجل بالفعل في هذا الكورس';
                } else {
                    // تسجيل الطالب
                    $stmt = $conn->prepare("
                        INSERT INTO course_enrollments (student_id, course_id, payment_amount, payment_status, enrolled_at) 
                        VALUES (?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$student['id'], $course_id, $payment_amount, $payment_status]);
                    
                    logUserActivity($_SESSION['user_id'], 'تسجيل طالب', "تم تسجيل الطالب {$student['name']} في الكورس: " . $course['title']);
                    
                    $success = 'تم تسجيل الطالب بنجاح';
                }
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء تسجيل الطالب: ' . $e->getMessage();
        }
    }
}

// جلب قائمة الطلاب المسجلين
$students = [];
try {
    $stmt = $conn->prepare("
        SELECT u.*, e.enrolled_at, e.payment_status, e.payment_amount, e.status as enrollment_status,
               COALESCE((SELECT COUNT(*) FROM session_attendance sa 
                        JOIN sessions s ON sa.session_id = s.id 
                        WHERE s.course_id = ? AND sa.student_id = u.id), 0) as attended_sessions,
               COALESCE((SELECT COUNT(*) FROM sessions WHERE course_id = ?), 0) as total_sessions
        FROM course_enrollments e
        JOIN users u ON e.student_id = u.id
        WHERE e.course_id = ?
        ORDER BY e.enrolled_at DESC
    ");
    $stmt->execute([$course_id, $course_id, $course_id]);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error = 'حدث خطأ في جلب قائمة الطلاب';
}

// إحصائيات الطلاب
$stats = [
    'total_students' => count($students),
    'active_students' => 0,
    'completed_payments' => 0,
    'pending_payments' => 0,
    'total_revenue' => 0
];

foreach ($students as $student) {
    if ($student['enrollment_status'] === 'active') {
        $stats['active_students']++;
    }
    if ($student['payment_status'] === 'completed') {
        $stats['completed_payments']++;
        $stats['total_revenue'] += $student['payment_amount'];
    } elseif ($student['payment_status'] === 'pending') {
        $stats['pending_payments']++;
    }
}

$pageTitle = 'طلاب الكورس: ' . $course['title'];
$pageSubtitle = 'إدارة وتتبع الطلاب المسجلين في الكورس';
require_once 'includes/admin-header.php';
?>

<!-- معلومات الكورس -->
<div class="row mb-4 fade-in-up">
    <div class="col-12">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="course-icon me-3">
                            <?php if (!empty($course['image']) && file_exists('../' . $course['image'])): ?>
                                <img src="../<?php echo htmlspecialchars($course['image']); ?>"
                                     alt="Course" class="rounded" width="60" height="60" style="object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center"
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-book fa-2x"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <h4 class="mb-1"><?php echo htmlspecialchars($course['title']); ?></h4>
                            <p class="text-muted mb-0">
                                <i class="fas fa-chalkboard-teacher me-1"></i>
                                المدرب: <?php echo htmlspecialchars($course['instructor_name'] ?? 'غير محدد'); ?>
                                <?php if ($course['course_type'] === 'paid'): ?>
                                    <span class="ms-3"><i class="fas fa-dollar-sign me-1"></i>السعر: <?php echo number_format($course['price'], 2); ?> ريال</span>
                                <?php else: ?>
                                    <span class="ms-3 text-success"><i class="fas fa-gift me-1"></i>مجاني</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <a href="course-details.php?id=<?php echo $course_id; ?>" class="btn btn-outline-info">
                            <i class="fas fa-eye me-2"></i>تفاصيل الكورس
                        </a>
                        <a href="manage-sessions.php?course_id=<?php echo $course_id; ?>" class="btn btn-outline-primary">
                            <i class="fas fa-video me-2"></i>إدارة الجلسات
                        </a>
                        <a href="manage-courses.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للكورسات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الطلاب -->
<div class="row mb-4 fade-in-up">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">إجمالي الطلاب</h6>
                        <h3 class="mb-0 text-primary"><?php echo $stats['total_students']; ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-primary bg-soft text-primary">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">طلاب نشطين</h6>
                        <h3 class="mb-0 text-success"><?php echo $stats['active_students']; ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-success bg-soft text-success">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">مدفوعات مكتملة</h6>
                        <h3 class="mb-0 text-info"><?php echo $stats['completed_payments']; ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-info bg-soft text-info">
                            <i class="fas fa-credit-card fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">إجمالي الإيرادات</h6>
                        <h3 class="mb-0 text-warning"><?php echo number_format($stats['total_revenue'], 2); ?> ريال</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-warning bg-soft text-warning">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج إضافة طالب جديد -->
<div class="row mb-4">
    <div class="col-12">
        <div class="admin-card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user-plus me-2"></i>إضافة طالب جديد</h6>
            </div>
            <div class="card-body">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" class="row g-3">
                    <div class="col-md-4">
                        <label for="student_email" class="form-label">بريد الطالب الإلكتروني <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="student_email" name="student_email" required>
                        <small class="text-muted">يجب أن يكون الطالب مسجل مسبقاً في النظام</small>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="payment_amount" class="form-label">مبلغ الدفع (ريال)</label>
                        <input type="number" class="form-control" id="payment_amount" name="payment_amount" 
                               value="<?php echo $course['price'] ?? 0; ?>" min="0" step="0.01">
                    </div>
                    
                    <div class="col-md-3">
                        <label for="payment_status" class="form-label">حالة الدفع</label>
                        <select class="form-select" id="payment_status" name="payment_status">
                            <option value="completed">مكتمل</option>
                            <option value="pending">معلق</option>
                            <option value="failed">فاشل</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" name="add_student" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>إضافة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الطلاب -->
<div class="admin-card fade-in-up">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h6 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الطلاب المسجلين</h6>
            <small class="text-muted">إدارة وتتبع جميع الطلاب المسجلين في الكورس</small>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" onclick="exportStudents()">
                <i class="fas fa-download me-2"></i>تصدير
            </button>
        </div>
    </div>

    <div class="card-body">
        <!-- فلاتر البحث -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث في الطلاب...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="paymentFilter">
                    <option value="">جميع حالات الدفع</option>
                    <option value="completed">مكتمل</option>
                    <option value="pending">معلق</option>
                    <option value="failed">فاشل</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="completed">مكتمل</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                </button>
            </div>
        </div>

        <?php if (empty($students)): ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-users fa-4x text-muted"></i>
                </div>
                <h5 class="text-muted">لا يوجد طلاب مسجلين في الكورس</h5>
                <p class="text-muted">ابدأ بإضافة طلاب جدد للكورس</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="studentsTable">
                    <thead class="table-light">
                        <tr>
                            <th>الطالب</th>
                            <th>تاريخ التسجيل</th>
                            <th>حالة الدفع</th>
                            <th>المبلغ المدفوع</th>
                            <th>الحضور</th>
                            <th>حالة التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($students as $student): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm rounded-circle bg-info text-white me-3 d-flex align-items-center justify-content-center">
                                            <?php echo strtoupper(substr($student['name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($student['name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                            <?php if ($student['phone']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($student['phone']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo date('Y-m-d', strtotime($student['enrolled_at'])); ?></strong>
                                        <br><small class="text-muted"><?php echo date('H:i', strtotime($student['enrolled_at'])); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $payment_colors = [
                                        'completed' => 'success',
                                        'pending' => 'warning',
                                        'failed' => 'danger'
                                    ];
                                    $payment_text = [
                                        'completed' => 'مكتمل',
                                        'pending' => 'معلق',
                                        'failed' => 'فاشل'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $payment_colors[$student['payment_status']] ?? 'secondary'; ?>">
                                        <?php echo $payment_text[$student['payment_status']] ?? $student['payment_status']; ?>
                                    </span>
                                </td>
                                <td>
                                    <strong class="text-success"><?php echo number_format($student['payment_amount'], 2); ?> ريال</strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2"><?php echo $student['attended_sessions']; ?>/<?php echo $student['total_sessions']; ?></span>
                                        <?php
                                        $attendance_percentage = $student['total_sessions'] > 0 ?
                                            ($student['attended_sessions'] / $student['total_sessions']) * 100 : 0;
                                        ?>
                                        <div class="progress" style="width: 60px; height: 8px;">
                                            <div class="progress-bar bg-<?php echo $attendance_percentage >= 80 ? 'success' : ($attendance_percentage >= 60 ? 'warning' : 'danger'); ?>"
                                                 style="width: <?php echo $attendance_percentage; ?>%"></div>
                                        </div>
                                        <small class="ms-2 text-muted"><?php echo round($attendance_percentage); ?>%</small>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $status_colors = [
                                        'active' => 'success',
                                        'inactive' => 'secondary',
                                        'completed' => 'info'
                                    ];
                                    $status_text = [
                                        'active' => 'نشط',
                                        'inactive' => 'غير نشط',
                                        'completed' => 'مكتمل'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $status_colors[$student['enrollment_status']] ?? 'secondary'; ?>">
                                        <?php echo $status_text[$student['enrollment_status']] ?? $student['enrollment_status']; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-info" onclick="viewStudentDetails(<?php echo $student['id']; ?>)"
                                                data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>

                                        <button class="btn btn-sm btn-outline-primary" onclick="viewAttendance(<?php echo $student['id']; ?>, <?php echo $course_id; ?>)"
                                                data-bs-toggle="tooltip" title="سجل الحضور">
                                            <i class="fas fa-calendar-check"></i>
                                        </button>

                                        <button class="btn btn-sm btn-outline-warning" onclick="updatePaymentStatus(<?php echo $student['id']; ?>, <?php echo $course_id; ?>)"
                                                data-bs-toggle="tooltip" title="تحديث حالة الدفع">
                                            <i class="fas fa-credit-card"></i>
                                        </button>

                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleEnrollmentStatus(<?php echo $student['id']; ?>, <?php echo $course_id; ?>, '<?php echo $student['enrollment_status']; ?>')"
                                                data-bs-toggle="tooltip" title="تغيير حالة التسجيل">
                                            <i class="fas fa-toggle-on"></i>
                                        </button>

                                        <button class="btn btn-sm btn-outline-danger" onclick="removeStudent(<?php echo $student['id']; ?>, <?php echo $course_id; ?>, '<?php echo htmlspecialchars($student['name']); ?>')"
                                                data-bs-toggle="tooltip" title="إلغاء التسجيل">
                                            <i class="fas fa-user-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.bg-soft {
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
}

.bg-success.bg-soft {
    background-color: rgba(var(--bs-success-rgb), 0.1) !important;
}

.bg-info.bg-soft {
    background-color: rgba(var(--bs-info-rgb), 0.1) !important;
}

.bg-warning.bg-soft {
    background-color: rgba(var(--bs-warning-rgb), 0.1) !important;
}

.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 16px;
    font-weight: 600;
}

.table th {
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 6px !important;
    margin: 0 1px;
}

.progress {
    background-color: #e9ecef;
}
</style>

<script>
$(document).ready(function() {
    // تهيئة DataTables
    if ($.fn.DataTable.isDataTable('#studentsTable')) {
        $('#studentsTable').DataTable().destroy();
    }

    const table = $('#studentsTable').DataTable({
        order: [[1, 'desc']],
        pageLength: 25,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        columnDefs: [
            { orderable: false, targets: [6] }
        ]
    });

    // البحث المخصص
    $('#searchInput').on('keyup', function() {
        table.search(this.value).draw();
    });

    // فلتر حالة الدفع
    $('#paymentFilter').on('change', function() {
        const payment = this.value;
        if (payment) {
            table.column(2).search(payment).draw();
        } else {
            table.column(2).search('').draw();
        }
    });

    // فلتر حالة التسجيل
    $('#statusFilter').on('change', function() {
        const status = this.value;
        if (status) {
            table.column(5).search(status).draw();
        } else {
            table.column(5).search('').draw();
        }
    });
});

// إعادة تعيين الفلاتر
function resetFilters() {
    $('#searchInput').val('');
    $('#paymentFilter').val('');
    $('#statusFilter').val('');
    $('#studentsTable').DataTable().search('').columns().search('').draw();
}

// عرض تفاصيل الطالب
function viewStudentDetails(studentId) {
    window.location.href = `student-details.php?id=${studentId}`;
}

// عرض سجل الحضور
function viewAttendance(studentId, courseId) {
    showLoading('جاري جلب سجل الحضور...');

    $.get('ajax/get-student-attendance.php', {
        student_id: studentId,
        course_id: courseId
    })
    .done(function(response) {
        hideLoading();
        const data = JSON.parse(response);
        if (data.success) {
            let attendanceHtml = '<div class="table-responsive"><table class="table table-sm">';
            attendanceHtml += '<thead><tr><th>الجلسة</th><th>التاريخ</th><th>الحالة</th></tr></thead><tbody>';

            data.attendance.forEach(function(record) {
                const statusClass = record.attendance_status === 'present' ? 'success' :
                                  record.attendance_status === 'late' ? 'warning' : 'danger';
                const statusText = record.attendance_status === 'present' ? 'حاضر' :
                                 record.attendance_status === 'late' ? 'متأخر' : 'غائب';

                attendanceHtml += `<tr>
                    <td>${record.session_title}</td>
                    <td>${record.session_date}</td>
                    <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                </tr>`;
            });

            attendanceHtml += '</tbody></table></div>';

            Swal.fire({
                title: 'سجل الحضور',
                html: attendanceHtml,
                showConfirmButton: false,
                showCloseButton: true,
                width: 600
            });
        } else {
            Swal.fire('خطأ', data.message || 'حدث خطأ في جلب سجل الحضور', 'error');
        }
    })
    .fail(function() {
        hideLoading();
        Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
    });
}

// تحديث حالة الدفع
function updatePaymentStatus(studentId, courseId) {
    Swal.fire({
        title: 'تحديث حالة الدفع',
        html: `
            <div class="mb-3">
                <label class="form-label">حالة الدفع الجديدة:</label>
                <select class="form-select" id="newPaymentStatus">
                    <option value="completed">مكتمل</option>
                    <option value="pending">معلق</option>
                    <option value="failed">فاشل</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">المبلغ:</label>
                <input type="number" class="form-control" id="newPaymentAmount" min="0" step="0.01">
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'تحديث',
        cancelButtonText: 'إلغاء',
        preConfirm: () => {
            const status = document.getElementById('newPaymentStatus').value;
            const amount = document.getElementById('newPaymentAmount').value;

            if (!amount || amount < 0) {
                Swal.showValidationMessage('يرجى إدخال مبلغ صحيح');
                return false;
            }

            return { status: status, amount: amount };
        }
    }).then((result) => {
        if (result.isConfirmed) {
            showLoading('جاري تحديث حالة الدفع...');

            $.post('ajax/update-payment-status.php', {
                student_id: studentId,
                course_id: courseId,
                payment_status: result.value.status,
                payment_amount: result.value.amount
            })
            .done(function(response) {
                hideLoading();
                const data = JSON.parse(response);
                if (data.success) {
                    showToast('تم تحديث حالة الدفع بنجاح', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    Swal.fire('خطأ', data.message || 'حدث خطأ أثناء تحديث حالة الدفع', 'error');
                }
            })
            .fail(function() {
                hideLoading();
                Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

// تغيير حالة التسجيل
function toggleEnrollmentStatus(studentId, courseId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const statusText = newStatus === 'active' ? 'تفعيل' : 'إلغاء تفعيل';

    Swal.fire({
        title: `${statusText} التسجيل`,
        text: `هل أنت متأكد من ${statusText} تسجيل هذا الطالب؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: `نعم، ${statusText}`,
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            showLoading(`جاري ${statusText} التسجيل...`);

            $.post('ajax/toggle-enrollment-status.php', {
                student_id: studentId,
                course_id: courseId,
                status: newStatus
            })
            .done(function(response) {
                hideLoading();
                const data = JSON.parse(response);
                if (data.success) {
                    showToast(`تم ${statusText} التسجيل بنجاح`, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    Swal.fire('خطأ', data.message || `حدث خطأ أثناء ${statusText} التسجيل`, 'error');
                }
            })
            .fail(function() {
                hideLoading();
                Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

// إلغاء تسجيل الطالب
function removeStudent(studentId, courseId, studentName) {
    Swal.fire({
        title: 'إلغاء تسجيل الطالب',
        html: `
            <p>هل أنت متأكد من إلغاء تسجيل الطالب <strong>${studentName}</strong>؟</p>
            <div class="alert alert-warning text-start">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> سيتم حذف جميع بيانات الطالب المرتبطة بهذا الكورس:
                <ul class="mt-2 mb-0">
                    <li>سجل الحضور</li>
                    <li>بيانات الدفع</li>
                    <li>التقييمات</li>
                </ul>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، ألغِ التسجيل',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            showLoading('جاري إلغاء تسجيل الطالب...');

            $.post('ajax/remove-student.php', {
                student_id: studentId,
                course_id: courseId
            })
            .done(function(response) {
                hideLoading();
                const data = JSON.parse(response);
                if (data.success) {
                    Swal.fire({
                        title: 'تم الإلغاء!',
                        text: 'تم إلغاء تسجيل الطالب بنجاح',
                        icon: 'success'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ', data.message || 'حدث خطأ أثناء إلغاء تسجيل الطالب', 'error');
                }
            })
            .fail(function() {
                hideLoading();
                Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

// تصدير الطلاب
function exportStudents() {
    Swal.fire({
        title: 'تصدير بيانات الطلاب',
        html: `
            <div class="d-grid gap-2">
                <button class="btn btn-success" onclick="exportData('excel', 'export-students.php?course_id=<?php echo $course_id; ?>')">
                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                </button>
                <button class="btn btn-danger" onclick="exportData('pdf', 'export-students.php?course_id=<?php echo $course_id; ?>')">
                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                </button>
                <button class="btn btn-info" onclick="exportData('csv', 'export-students.php?course_id=<?php echo $course_id; ?>')">
                    <i class="fas fa-file-csv me-2"></i>تصدير CSV
                </button>
            </div>
        `,
        showConfirmButton: false,
        showCloseButton: true,
        width: 300
    });
}
</script>

<?php require_once 'includes/admin-footer.php'; ?>
