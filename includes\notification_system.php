<?php
/**
 * نظام الإشعارات المحسن
 * Enhanced Notification System
 * ============================
 */

require_once 'config/config.php';
require_once 'email_handler.php';

class NotificationSystem {
    private $conn;
    private $emailHandler;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
        $this->emailHandler = new EmailHandler();
        $this->createNotificationTables();
    }
    
    /**
     * إنشاء جداول الإشعارات
     */
    private function createNotificationTables() {
        try {
            // جدول الإشعارات الرئيسي
            $sql = "CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                type ENUM('email', 'push', 'sms', 'in_app') NOT NULL,
                category ENUM('course', 'assignment', 'payment', 'system', 'reminder') NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                data JSON NULL,
                status ENUM('pending', 'sent', 'failed', 'read') DEFAULT 'pending',
                priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
                scheduled_at TIMESTAMP NULL,
                sent_at TIMESTAMP NULL,
                read_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_type (type),
                INDEX idx_status (status),
                INDEX idx_category (category),
                INDEX idx_scheduled (scheduled_at),
                INDEX idx_created (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // جدول إعدادات الإشعارات للمستخدمين
            $sql = "CREATE TABLE IF NOT EXISTS notification_preferences (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                email_enabled BOOLEAN DEFAULT TRUE,
                push_enabled BOOLEAN DEFAULT TRUE,
                sms_enabled BOOLEAN DEFAULT FALSE,
                course_notifications BOOLEAN DEFAULT TRUE,
                assignment_notifications BOOLEAN DEFAULT TRUE,
                payment_notifications BOOLEAN DEFAULT TRUE,
                system_notifications BOOLEAN DEFAULT TRUE,
                reminder_notifications BOOLEAN DEFAULT TRUE,
                email_frequency ENUM('immediate', 'daily', 'weekly') DEFAULT 'immediate',
                quiet_hours_start TIME DEFAULT '22:00:00',
                quiet_hours_end TIME DEFAULT '08:00:00',
                timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user (user_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // جدول قوالب الإشعارات
            $sql = "CREATE TABLE IF NOT EXISTS notification_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                type ENUM('email', 'push', 'sms', 'in_app') NOT NULL,
                category ENUM('course', 'assignment', 'payment', 'system', 'reminder') NOT NULL,
                subject_template VARCHAR(255) NOT NULL,
                body_template TEXT NOT NULL,
                variables JSON NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_name (name),
                INDEX idx_type (type),
                INDEX idx_category (category)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // إدراج القوالب الافتراضية
            $this->insertDefaultTemplates();
            
        } catch (PDOException $e) {
            error_log("Error creating notification tables: " . $e->getMessage());
        }
    }
    
    /**
     * إدراج القوالب الافتراضية
     */
    private function insertDefaultTemplates() {
        $templates = [
            [
                'name' => 'course_enrollment',
                'type' => 'email',
                'category' => 'course',
                'subject_template' => 'تم تسجيلك في الكورس: {{course_title}}',
                'body_template' => 'مرحباً {{user_name}},\n\nتم تسجيلك بنجاح في الكورس "{{course_title}}".\n\nيمكنك الآن الوصول إلى محتوى الكورس من خلال لوحة التحكم الخاصة بك.\n\nرابط الكورس: {{course_url}}\n\nشكراً لك,\nفريق منصة التعلم الإلكتروني',
                'variables' => '["user_name", "course_title", "course_url"]'
            ],
            [
                'name' => 'assignment_due',
                'type' => 'email',
                'category' => 'assignment',
                'subject_template' => 'تذكير: موعد تسليم الواجب {{assignment_title}}',
                'body_template' => 'مرحباً {{user_name}},\n\nهذا تذكير بأن موعد تسليم الواجب "{{assignment_title}}" هو {{due_date}}.\n\nيرجى التأكد من تسليم الواجب في الموعد المحدد.\n\nرابط الواجب: {{assignment_url}}\n\nشكراً لك,\nفريق منصة التعلم الإلكتروني',
                'variables' => '["user_name", "assignment_title", "due_date", "assignment_url"]'
            ],
            [
                'name' => 'payment_success',
                'type' => 'email',
                'category' => 'payment',
                'subject_template' => 'تم استلام دفعتك بنجاح',
                'body_template' => 'مرحباً {{user_name}},\n\nتم استلام دفعتك بنجاح بمبلغ {{amount}} {{currency}} للكورس "{{course_title}}".\n\nرقم المعاملة: {{transaction_id}}\n\nيمكنك الآن الوصول إلى الكورس من خلال لوحة التحكم.\n\nشكراً لك,\nفريق منصة التعلم الإلكتروني',
                'variables' => '["user_name", "amount", "currency", "course_title", "transaction_id"]'
            ],
            [
                'name' => 'session_reminder',
                'type' => 'push',
                'category' => 'reminder',
                'subject_template' => 'تذكير: جلسة {{session_title}} تبدأ خلال {{time_remaining}}',
                'body_template' => 'جلسة "{{session_title}}" ستبدأ خلال {{time_remaining}}. انقر للانضمام.',
                'variables' => '["session_title", "time_remaining"]'
            ]
        ];
        
        foreach ($templates as $template) {
            try {
                $stmt = $this->conn->prepare("
                    INSERT IGNORE INTO notification_templates 
                    (name, type, category, subject_template, body_template, variables) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $template['name'],
                    $template['type'],
                    $template['category'],
                    $template['subject_template'],
                    $template['body_template'],
                    $template['variables']
                ]);
            } catch (PDOException $e) {
                error_log("Error inserting template {$template['name']}: " . $e->getMessage());
            }
        }
    }
    
    /**
     * إرسال إشعار
     */
    public function sendNotification($userId, $type, $category, $templateName, $variables = [], $options = []) {
        try {
            // الحصول على القالب
            $template = $this->getTemplate($templateName, $type);
            if (!$template) {
                throw new Exception("Template not found: $templateName");
            }
            
            // الحصول على إعدادات المستخدم
            $preferences = $this->getUserPreferences($userId);
            
            // التحقق من تفعيل نوع الإشعار
            if (!$this->isNotificationEnabled($preferences, $type, $category)) {
                return false;
            }
            
            // معالجة القالب
            $subject = $this->processTemplate($template['subject_template'], $variables);
            $message = $this->processTemplate($template['body_template'], $variables);
            
            // إنشاء الإشعار في قاعدة البيانات
            $notificationId = $this->createNotification([
                'user_id' => $userId,
                'type' => $type,
                'category' => $category,
                'title' => $subject,
                'message' => $message,
                'data' => json_encode($variables),
                'priority' => $options['priority'] ?? 'medium',
                'scheduled_at' => $options['scheduled_at'] ?? null
            ]);
            
            // إرسال الإشعار حسب النوع
            $result = false;
            switch ($type) {
                case 'email':
                    $result = $this->sendEmailNotification($userId, $subject, $message, $options);
                    break;
                case 'push':
                    $result = $this->sendPushNotification($userId, $subject, $message, $options);
                    break;
                case 'sms':
                    $result = $this->sendSMSNotification($userId, $message, $options);
                    break;
                case 'in_app':
                    $result = true; // الإشعارات داخل التطبيق تُحفظ في قاعدة البيانات فقط
                    break;
            }
            
            // تحديث حالة الإشعار
            $this->updateNotificationStatus($notificationId, $result ? 'sent' : 'failed');
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Notification Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إرسال إشعار بريد إلكتروني
     */
    private function sendEmailNotification($userId, $subject, $message, $options = []) {
        try {
            $user = $this->getUserInfo($userId);
            if (!$user || !$user['email']) {
                return false;
            }
            
            return $this->emailHandler->sendEmail(
                $user['email'],
                $subject,
                $message,
                $options['html'] ?? false
            );
        } catch (Exception $e) {
            error_log("Email Notification Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إرسال إشعار Push
     */
    private function sendPushNotification($userId, $title, $message, $options = []) {
        try {
            // هنا يمكن تكامل مع خدمات Push Notifications مثل Firebase
            // للآن سنحفظ الإشعار في قاعدة البيانات فقط
            
            $pushData = [
                'title' => $title,
                'body' => $message,
                'icon' => $options['icon'] ?? '/assets/images/icons/icon-192x192.png',
                'badge' => $options['badge'] ?? '/assets/images/icons/badge-72x72.png',
                'data' => $options['data'] ?? []
            ];
            
            // يمكن إضافة منطق إرسال Push Notification هنا
            
            return true;
        } catch (Exception $e) {
            error_log("Push Notification Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إرسال إشعار SMS
     */
    private function sendSMSNotification($userId, $message, $options = []) {
        try {
            $user = $this->getUserInfo($userId);
            if (!$user || !$user['phone']) {
                return false;
            }
            
            // هنا يمكن تكامل مع خدمات SMS مثل Twilio
            // للآن سنعتبر الإرسال ناجح
            
            return true;
        } catch (Exception $e) {
            error_log("SMS Notification Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على قالب الإشعار
     */
    private function getTemplate($name, $type) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM notification_templates 
                WHERE name = ? AND type = ? AND is_active = TRUE
            ");
            $stmt->execute([$name, $type]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error getting template: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * معالجة قالب الإشعار
     */
    private function processTemplate($template, $variables) {
        $processed = $template;
        
        foreach ($variables as $key => $value) {
            $processed = str_replace("{{$key}}", $value, $processed);
        }
        
        return $processed;
    }
    
    /**
     * إنشاء إشعار في قاعدة البيانات
     */
    private function createNotification($data) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO notifications 
                (user_id, type, category, title, message, data, priority, scheduled_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['user_id'],
                $data['type'],
                $data['category'],
                $data['title'],
                $data['message'],
                $data['data'],
                $data['priority'],
                $data['scheduled_at']
            ]);
            
            return $this->conn->lastInsertId();
        } catch (PDOException $e) {
            error_log("Error creating notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث حالة الإشعار
     */
    private function updateNotificationStatus($notificationId, $status) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE notifications 
                SET status = ?, sent_at = CASE WHEN ? = 'sent' THEN NOW() ELSE sent_at END 
                WHERE id = ?
            ");
            $stmt->execute([$status, $status, $notificationId]);
        } catch (PDOException $e) {
            error_log("Error updating notification status: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على إعدادات المستخدم
     */
    private function getUserPreferences($userId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM notification_preferences WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $preferences = $stmt->fetch();
            
            if (!$preferences) {
                // إنشاء إعدادات افتراضية
                $this->createDefaultPreferences($userId);
                return $this->getUserPreferences($userId);
            }
            
            return $preferences;
        } catch (PDOException $e) {
            error_log("Error getting user preferences: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * إنشاء إعدادات افتراضية للمستخدم
     */
    private function createDefaultPreferences($userId) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO notification_preferences (user_id) VALUES (?)
            ");
            $stmt->execute([$userId]);
        } catch (PDOException $e) {
            error_log("Error creating default preferences: " . $e->getMessage());
        }
    }
    
    /**
     * التحقق من تفعيل نوع الإشعار
     */
    private function isNotificationEnabled($preferences, $type, $category) {
        if (!$preferences) return false;
        
        // التحقق من تفعيل نوع الإشعار
        $typeEnabled = false;
        switch ($type) {
            case 'email':
                $typeEnabled = $preferences['email_enabled'];
                break;
            case 'push':
                $typeEnabled = $preferences['push_enabled'];
                break;
            case 'sms':
                $typeEnabled = $preferences['sms_enabled'];
                break;
            case 'in_app':
                $typeEnabled = true; // الإشعارات داخل التطبيق مفعلة دائماً
                break;
        }
        
        if (!$typeEnabled) return false;
        
        // التحقق من تفعيل فئة الإشعار
        $categoryEnabled = false;
        switch ($category) {
            case 'course':
                $categoryEnabled = $preferences['course_notifications'];
                break;
            case 'assignment':
                $categoryEnabled = $preferences['assignment_notifications'];
                break;
            case 'payment':
                $categoryEnabled = $preferences['payment_notifications'];
                break;
            case 'system':
                $categoryEnabled = $preferences['system_notifications'];
                break;
            case 'reminder':
                $categoryEnabled = $preferences['reminder_notifications'];
                break;
        }
        
        return $categoryEnabled;
    }
    
    /**
     * الحصول على معلومات المستخدم
     */
    private function getUserInfo($userId) {
        try {
            $stmt = $this->conn->prepare("
                SELECT id, name, email, phone FROM users WHERE id = ?
            ");
            $stmt->execute([$userId]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error getting user info: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * الحصول على الإشعارات غير المقروءة للمستخدم
     */
    public function getUnreadNotifications($userId, $limit = 10) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM notifications 
                WHERE user_id = ? AND status != 'read' 
                ORDER BY created_at DESC 
                LIMIT ?
            ");
            $stmt->execute([$userId, $limit]);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error getting unread notifications: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديد الإشعار كمقروء
     */
    public function markAsRead($notificationId, $userId) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE notifications 
                SET status = 'read', read_at = NOW() 
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$notificationId, $userId]);
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Error marking notification as read: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    public function markAllAsRead($userId) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE notifications 
                SET status = 'read', read_at = NOW() 
                WHERE user_id = ? AND status != 'read'
            ");
            $stmt->execute([$userId]);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Error marking all notifications as read: " . $e->getMessage());
            return false;
        }
    }
}

// دوال مساعدة سريعة
function sendQuickNotification($userId, $title, $message, $type = 'in_app', $category = 'system') {
    global $conn;
    
    try {
        $notificationSystem = new NotificationSystem($conn);
        
        // إنشاء قالب مؤقت
        $variables = [
            'user_name' => 'المستخدم',
            'title' => $title,
            'message' => $message
        ];
        
        return $notificationSystem->sendNotification(
            $userId, 
            $type, 
            $category, 
            'quick_notification', 
            $variables
        );
    } catch (Exception $e) {
        error_log("Quick Notification Error: " . $e->getMessage());
        return false;
    }
}

function notifyCourseEnrollment($userId, $courseTitle, $courseUrl) {
    global $conn;
    
    try {
        $notificationSystem = new NotificationSystem($conn);
        $user = getUserById($userId);
        
        $variables = [
            'user_name' => $user['name'],
            'course_title' => $courseTitle,
            'course_url' => $courseUrl
        ];
        
        return $notificationSystem->sendNotification(
            $userId, 
            'email', 
            'course', 
            'course_enrollment', 
            $variables
        );
    } catch (Exception $e) {
        error_log("Course Enrollment Notification Error: " . $e->getMessage());
        return false;
    }
}

function getUserById($userId) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Error getting user: " . $e->getMessage());
        return null;
    }
}
?>
