<?php
/**
 * اتصال بسيط وموثوق بقاعدة البيانات
 * Simple and Reliable Database Connection
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'zoom_learning_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إنشاء اتصال PDO
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
    ];

    $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
    
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    die("فشل في الاتصال بقاعدة البيانات");
}

/**
 * دوال مساعدة لقاعدة البيانات
 */

// دالة لتنفيذ استعلام وإرجاع النتائج
function executeQuery($sql, $params = []) {
    global $conn;
    try {
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Query Error: " . $e->getMessage());
        return false;
    }
}

// دالة لجلب سجل واحد
function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

// دالة لجلب جميع السجلات
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : [];
}

// دالة لعد السجلات
function countRecords($table, $conditions = '') {
    global $conn;
    try {
        $sql = "SELECT COUNT(*) as total FROM {$table}";
        if ($conditions) {
            $sql .= " WHERE {$conditions}";
        }
        $stmt = $conn->query($sql);
        $result = $stmt->fetch();
        return (int)$result['total'];
    } catch (PDOException $e) {
        error_log("Count Error: " . $e->getMessage());
        return 0;
    }
}

// دالة لإدراج سجل جديد
function insertRecord($table, $data) {
    global $conn;
    try {
        $columns = array_keys($data);
        $placeholders = ':' . implode(', :', $columns);
        
        $sql = "INSERT INTO {$table} (" . implode(', ', $columns) . ") VALUES ({$placeholders})";
        
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute($data);
        
        return $result ? $conn->lastInsertId() : false;
        
    } catch (PDOException $e) {
        error_log("Insert Error: " . $e->getMessage());
        return false;
    }
}

// دالة لتحديث سجل
function updateRecord($table, $id, $data) {
    global $conn;
    try {
        $setParts = [];
        $cleanData = [];

        foreach ($data as $column => $value) {
            // تجاهل القيم التي هي مصفوفات أو null
            if (!is_array($value) && $value !== null) {
                $setParts[] = "{$column} = :{$column}";
                $cleanData[$column] = $value;
            }
        }

        if (empty($setParts)) {
            return false;
        }

        $sql = "UPDATE {$table} SET " . implode(', ', $setParts) . " WHERE id = :id";
        $cleanData['id'] = $id;

        $stmt = $conn->prepare($sql);
        return $stmt->execute($cleanData);

    } catch (PDOException $e) {
        error_log("Update Error: " . $e->getMessage());
        return false;
    }
}

// دالة لحذف سجل
function deleteRecord($table, $conditions) {
    global $conn;
    try {
        if (is_numeric($conditions)) {
            // إذا كان رقم، فهو ID
            $sql = "DELETE FROM {$table} WHERE id = ?";
            $params = [$conditions];
        } elseif (is_array($conditions)) {
            // إذا كان مصفوفة، فهي شروط متعددة
            $whereParts = [];
            $params = [];
            foreach ($conditions as $column => $value) {
                $whereParts[] = "{$column} = ?";
                $params[] = $value;
            }
            $sql = "DELETE FROM {$table} WHERE " . implode(' AND ', $whereParts);
        } else {
            return false;
        }

        $stmt = $conn->prepare($sql);
        return $stmt->execute($params);

    } catch (PDOException $e) {
        error_log("Delete Error: " . $e->getMessage());
        return false;
    }
}

// دالة للتحقق من وجود جدول
function tableExists($tableName) {
    global $conn;
    try {
        $stmt = $conn->query("SHOW TABLES LIKE '{$tableName}'");
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// دالة للتحقق من وجود عمود في جدول
function columnExists($tableName, $columnName) {
    global $conn;
    try {
        $stmt = $conn->query("SHOW COLUMNS FROM {$tableName} LIKE '{$columnName}'");
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// دالة لإضافة عمود إلى جدول
function addColumn($tableName, $columnName, $definition) {
    global $conn;
    try {
        if (!columnExists($tableName, $columnName)) {
            $sql = "ALTER TABLE {$tableName} ADD COLUMN {$columnName} {$definition}";
            $conn->exec($sql);
            return true;
        }
        return false;
    } catch (PDOException $e) {
        error_log("Add Column Error: " . $e->getMessage());
        return false;
    }
}

// دالة لإنشاء جدول إذا لم يكن موجوداً
function createTableIfNotExists($tableName, $schema) {
    global $conn;
    try {
        if (!tableExists($tableName)) {
            $sql = "CREATE TABLE {$tableName} ({$schema}) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);
            return true;
        }
        return false;
    } catch (PDOException $e) {
        error_log("Create Table Error: " . $e->getMessage());
        return false;
    }
}

// دالة للحصول على إعداد من النظام
function getSystemSetting($key, $default = null) {
    $setting = fetchOne("SELECT setting_value FROM system_settings WHERE setting_key = ?", [$key]);
    return $setting ? $setting['setting_value'] : $default;
}

// دالة لحفظ إعداد في النظام
function setSystemSetting($key, $value, $type = 'string', $description = '') {
    global $conn;
    try {
        $sql = "INSERT INTO system_settings (setting_key, setting_value, setting_type, description) 
                VALUES (?, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                setting_type = VALUES(setting_type),
                description = VALUES(description),
                updated_at = CURRENT_TIMESTAMP";
        
        $stmt = $conn->prepare($sql);
        return $stmt->execute([$key, $value, $type, $description]);
        
    } catch (PDOException $e) {
        error_log("Set Setting Error: " . $e->getMessage());
        return false;
    }
}

// دالة لتسجيل نشاط المستخدم
function logUserActivity($userId, $action, $details = '', $ipAddress = null) {
    global $conn;

    if (!$ipAddress) {
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    try {
        // إنشاء جدول user_activities إذا لم يكن موجود
        $conn->exec("
            CREATE TABLE IF NOT EXISTS user_activities (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                action VARCHAR(255) NOT NULL,
                details TEXT,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_action (action),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        return insertRecord('user_activities', [
            'user_id' => $userId,
            'action' => $action,
            'details' => $details,
            'ip_address' => $ipAddress,
            'created_at' => date('Y-m-d H:i:s')
        ]);

    } catch (Exception $e) {
        error_log("Error logging user activity: " . $e->getMessage());
        return false;
    }
}

// دالة للتحقق من صحة الاتصال
function testConnection() {
    global $conn;
    try {
        $stmt = $conn->query("SELECT 1");
        return $stmt !== false;
    } catch (PDOException $e) {
        return false;
    }
}

// اختبار الاتصال عند تحميل الملف
if (!testConnection()) {
    error_log("Database connection test failed");
}

?>
