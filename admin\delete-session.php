<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$session_id = (int)($_GET['id'] ?? 0);
$course_id = (int)($_GET['course_id'] ?? 0);

if (!$session_id || !$course_id) {
    header('Location: manage-sessions.php');
    exit;
}

// جلب تفاصيل الجلسة للتأكد من وجودها
$session = fetchOne("SELECT * FROM sessions WHERE id = ? AND course_id = ?", [$session_id, $course_id]);

if (!$session) {
    header('Location: manage-sessions.php?course_id=' . $course_id);
    exit;
}

// حذف الجلسة وجميع البيانات المرتبطة بها
try {
    // حذف سجلات الحضور أولاً
    deleteRecord('session_attendance', ['session_id' => $session_id]);
    
    // حذف الجلسة
    if (deleteRecord('sessions', ['id' => $session_id])) {
        $_SESSION['success_message'] = 'تم حذف الجلسة بنجاح';
    } else {
        $_SESSION['error_message'] = 'حدث خطأ أثناء حذف الجلسة';
    }
} catch (Exception $e) {
    $_SESSION['error_message'] = 'حدث خطأ أثناء حذف الجلسة: ' . $e->getMessage();
}

// إعادة التوجيه إلى صفحة إدارة الجلسات
header('Location: manage-sessions.php?course_id=' . $course_id);
exit;
?>
