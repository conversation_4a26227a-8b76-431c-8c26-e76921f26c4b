<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

// إنشاء CSRF token إذا لم يكن موجوداً
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// دالة التحقق من CSRF
function verifyCsrfToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// دالة تنظيف وتعقيم الملفات المرفوعة
function sanitizeUploadedFile($file) {
    // التحقق من نوع الملف الحقيقي
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    $allowedMimes = [
        'video/mp4', 'video/avi', 'video/quicktime',
        'video/x-msvideo', 'video/x-flv', 'video/webm', 'video/x-matroska'
    ];

    return in_array($mimeType, $allowedMimes);
}

$pageTitle = 'إدارة الفيديوهات';
$breadcrumbs = [
    ['title' => 'إدارة الفيديوهات']
];

// التحقق من رسالة النجاح
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $success_message = 'تم رفع الفيديو بنجاح!';
}
if (isset($_GET['uploaded'])) {
    $success_message = 'تم رفع الفيديو "' . htmlspecialchars($_GET['uploaded']) . '" بنجاح!';
}

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'upload_video') {
        $course_id = $_POST['course_id'] ?? 0;
        $chapter_id = $_POST['chapter_id'] ?? 0;
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $duration = $_POST['duration'] ?? 0;
        $video_url = trim($_POST['video_url'] ?? '');
        $is_free = isset($_POST['is_free']) ? 1 : 0;
        $order_number = $_POST['order_number'] ?? 1;

        $errors = [];

        if (empty($title)) {
            $errors[] = 'عنوان الفيديو مطلوب';
        }

        if (empty($course_id)) {
            $errors[] = 'يجب اختيار كورس';
        }

        if (empty($video_url)) {
            $errors[] = 'رابط الفيديو مطلوب';
        }

        if (empty($errors)) {
            try {
                // التحقق من أن الكورس ينتمي للمدرب
                $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
                $stmt->execute([$course_id, $_SESSION['user_id']]);

                if ($stmt->fetch()) {
                    // التأكد من وجود جدول course_videos
                    $stmt = $conn->query("SHOW TABLES LIKE 'course_videos'");
                    if ($stmt->rowCount() == 0) {
                        // إنشاء الجدول
                        $conn->exec("CREATE TABLE course_videos (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            course_id INT NOT NULL,
                            chapter_id INT NULL,
                            title VARCHAR(255) NOT NULL,
                            description TEXT,
                            video_url VARCHAR(500) NOT NULL,
                            video_file_path VARCHAR(500) NULL,
                            file_size BIGINT DEFAULT NULL,
                            duration INT DEFAULT 0,
                            is_free TINYINT(1) DEFAULT 0,
                            order_number INT DEFAULT 1,
                            uploaded_by INT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_course_id (course_id),
                            INDEX idx_uploaded_by (uploaded_by)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
                    }

                    // التحقق من وجود الأعمدة المطلوبة
                    $stmt = $conn->query("DESCRIBE course_videos");
                    $video_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

                    // إضافة الأعمدة المفقودة
                    if (!in_array('video_file_path', $video_columns)) {
                        $conn->exec("ALTER TABLE course_videos ADD COLUMN video_file_path VARCHAR(500) NULL");
                    }
                    if (!in_array('file_size', $video_columns)) {
                        $conn->exec("ALTER TABLE course_videos ADD COLUMN file_size BIGINT DEFAULT NULL");
                    }
                    if (!in_array('order_number', $video_columns)) {
                        $conn->exec("ALTER TABLE course_videos ADD COLUMN order_number INT DEFAULT 1");
                    }
                    if (!in_array('chapter_id', $video_columns)) {
                        $conn->exec("ALTER TABLE course_videos ADD COLUMN chapter_id INT NULL");
                    }

                    $stmt = $conn->prepare("
                        INSERT INTO course_videos
                        (course_id, chapter_id, title, description, video_url, duration, is_free, order_number, uploaded_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $course_id, $chapter_id ?: null, $title, $description,
                        $video_url, $duration, $is_free, $order_number, $_SESSION['user_id']
                    ]);

                    $success_message = 'تم رفع الفيديو بنجاح';
                } else {
                    $error_message = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
                }
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ أثناء رفع الفيديو: ' . $e->getMessage();
            }
        }
    }

    if ($action === 'upload_local_video') {
        $course_id = $_POST['course_id'] ?? 0;
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $is_free = isset($_POST['is_free']) ? 1 : 0;

        $errors = [];

        if (empty($title)) {
            $errors[] = 'عنوان الفيديو مطلوب';
        }

        if (empty($course_id)) {
            $errors[] = 'يجب اختيار كورس';
        }

        // التحقق من الملف المرفوع
        if (!isset($_FILES['video_file']) || $_FILES['video_file']['error'] !== UPLOAD_ERR_OK) {
            if (isset($_FILES['video_file'])) {
                switch ($_FILES['video_file']['error']) {
                    case UPLOAD_ERR_INI_SIZE:
                    case UPLOAD_ERR_FORM_SIZE:
                        $errors[] = 'حجم الملف كبير جداً';
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        $errors[] = 'تم رفع جزء من الملف فقط';
                        break;
                    case UPLOAD_ERR_NO_FILE:
                        $errors[] = 'لم يتم اختيار ملف';
                        break;
                    default:
                        $errors[] = 'حدث خطأ أثناء رفع الملف';
                }
            } else {
                $errors[] = 'يجب اختيار ملف فيديو';
            }
        } else {
            $file = $_FILES['video_file'];
            $allowed_extensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
            $max_size = 1000 * 1024 * 1024; // 1 GB

            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

            if (!in_array($file_extension, $allowed_extensions)) {
                $errors[] = 'نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', $allowed_extensions);
            }

            if ($file['size'] > $max_size) {
                $errors[] = 'حجم الملف كبير جداً. الحد الأقصى 1 جيجابايت';
            }
        }

        if (empty($errors)) {
            try {
                // التحقق من أن الكورس ينتمي للمدرب
                $stmt = $conn->prepare("SELECT id, title FROM courses WHERE id = ? AND instructor_id = ?");
                $stmt->execute([$course_id, $_SESSION['user_id']]);
                $course = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($course) {
                    // التأكد من وجود مجلد الرفع
                    $upload_dir = '../uploads/course_videos/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    // إنشاء اسم ملف فريد
                    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                    $file_name = 'course_' . $course_id . '_' . time() . '_' . uniqid() . '.' . $file_extension;
                    $upload_path = $upload_dir . $file_name;

                    // رفع الملف
                    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                        // التأكد من وجود جدول course_videos
                        $stmt = $conn->query("SHOW TABLES LIKE 'course_videos'");
                        if ($stmt->rowCount() == 0) {
                            // إنشاء الجدول
                            $conn->exec("CREATE TABLE course_videos (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                course_id INT NOT NULL,
                                chapter_id INT NULL,
                                title VARCHAR(255) NOT NULL,
                                description TEXT,
                                video_url VARCHAR(500) NOT NULL,
                                video_file_path VARCHAR(500) NULL,
                                file_size BIGINT DEFAULT NULL,
                                duration INT DEFAULT 0,
                                is_free TINYINT(1) DEFAULT 0,
                                order_number INT DEFAULT 1,
                                uploaded_by INT,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                INDEX idx_course_id (course_id),
                                INDEX idx_uploaded_by (uploaded_by)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
                        }

                        // التحقق من وجود الأعمدة المطلوبة
                        $stmt = $conn->query("DESCRIBE course_videos");
                        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

                        // إضافة الأعمدة المفقودة
                        if (!in_array('video_file_path', $columns)) {
                            $conn->exec("ALTER TABLE course_videos ADD COLUMN video_file_path VARCHAR(500) NULL");
                        }
                        if (!in_array('file_size', $columns)) {
                            $conn->exec("ALTER TABLE course_videos ADD COLUMN file_size BIGINT DEFAULT NULL");
                        }

                        // إدراج البيانات في قاعدة البيانات
                        $video_url = 'uploads/course_videos/' . $file_name;
                        $stmt = $conn->prepare("
                            INSERT INTO course_videos
                            (course_id, title, description, video_url, video_file_path, file_size, is_free, uploaded_by)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([
                            $course_id,
                            $title,
                            $description,
                            $video_url,
                            $video_url,
                            $file['size'],
                            $is_free,
                            $_SESSION['user_id']
                        ]);

                        $success_message = 'تم رفع الفيديو من الجهاز بنجاح!';

                        // إعادة توجيه لتجنب إعادة الإرسال
                        header('Location: videos.php?success=1&uploaded=' . urlencode($title));
                        exit;
                    } else {
                        $error_message = 'فشل في رفع الملف. تأكد من صلاحيات المجلد.';
                    }
                } else {
                    $error_message = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
                }
            } catch (PDOException $e) {
                $error_message = 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage();
            } catch (Exception $e) {
                $error_message = 'حدث خطأ غير متوقع: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'delete_video') {
        $video_id = $_POST['video_id'] ?? 0;
        
        try {
            $stmt = $conn->prepare("
                DELETE cv FROM course_videos cv
                INNER JOIN courses c ON cv.course_id = c.id
                WHERE cv.id = ? AND c.instructor_id = ?
            ");
            $stmt->execute([$video_id, $_SESSION['user_id']]);
            
            $success_message = 'تم حذف الفيديو بنجاح';
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء حذف الفيديو';
        }
    }
    
    if ($action === 'update_order') {
        $video_orders = $_POST['video_orders'] ?? [];

        try {
            // التحقق من وجود عمود order_number
            $stmt = $conn->query("DESCRIBE course_videos");
            $video_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $has_order = in_array('order_number', $video_columns);

            if ($has_order) {
                foreach ($video_orders as $video_id => $order) {
                    $stmt = $conn->prepare("
                        UPDATE course_videos cv
                        INNER JOIN courses c ON cv.course_id = c.id
                        SET cv.order_number = ?
                        WHERE cv.id = ? AND c.instructor_id = ?
                    ");
                    $stmt->execute([$order, $video_id, $_SESSION['user_id']]);
                }
                $success_message = 'تم تحديث ترتيب الفيديوهات بنجاح';
            } else {
                $error_message = 'ميزة الترتيب غير متاحة';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء تحديث الترتيب';
        }
    }
}

// فلاتر البحث
$course_filter = $_GET['course_id'] ?? '';
$chapter_filter = $_GET['chapter_id'] ?? '';
$status_filter = $_GET['status'] ?? '';

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

// جلب الفصول إذا تم اختيار كورس
$chapters = [];
if (!empty($course_filter)) {
    try {
        // التحقق من وجود جدول course_chapters
        $stmt = $conn->query("SHOW TABLES LIKE 'course_chapters'");
        if ($stmt->rowCount() > 0) {
            // التحقق من وجود عمود order_number
            $stmt = $conn->query("DESCRIBE course_chapters");
            $ch_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $has_order = in_array('order_number', $ch_columns);

            $order_by = $has_order ? "ch.order_number" : "ch.id";

            $stmt = $conn->prepare("
                SELECT ch.id, ch.title
                FROM course_chapters ch
                INNER JOIN courses c ON ch.course_id = c.id
                WHERE ch.course_id = ? AND c.instructor_id = ?
                ORDER BY $order_by
            ");
            $stmt->execute([$course_filter, $_SESSION['user_id']]);
            $chapters = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (PDOException $e) {
        $chapters = [];
    }
}

// جلب الفيديوهات
try {
    $where_conditions = ["c.instructor_id = ?"];
    $params = [$_SESSION['user_id']];

    if (!empty($course_filter)) {
        $where_conditions[] = "cv.course_id = ?";
        $params[] = $course_filter;
    }

    if (!empty($chapter_filter)) {
        $where_conditions[] = "cv.chapter_id = ?";
        $params[] = $chapter_filter;
    }

    if (!empty($status_filter)) {
        if ($status_filter === 'free') {
            $where_conditions[] = "cv.is_free = 1";
        } elseif ($status_filter === 'premium') {
            $where_conditions[] = "cv.is_free = 0";
        }
    }

    $where_clause = implode(' AND ', $where_conditions);

    // التحقق من وجود الجداول والأعمدة
    $tables_exist = [];
    $stmt = $conn->query("SHOW TABLES");
    $all_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    $tables_exist['course_chapters'] = in_array('course_chapters', $all_tables);
    $tables_exist['video_watches'] = in_array('video_watches', $all_tables);

    // التحقق من أعمدة course_videos
    $stmt = $conn->query("DESCRIBE course_videos");
    $cv_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $has_chapter_id = in_array('chapter_id', $cv_columns);
    $has_video_order = in_array('order_number', $cv_columns);

    // بناء الاستعلام بناءً على الجداول والأعمدة المتاحة
    $select_fields = "cv.*, c.title as course_title";
    $joins = "INNER JOIN courses c ON cv.course_id = c.id";
    $order_clause = "c.title";

    // إضافة جدول الفصول إذا كان موجوداً
    if ($tables_exist['course_chapters'] && $has_chapter_id) {
        $select_fields .= ", ch.title as chapter_title";
        $joins .= " LEFT JOIN course_chapters ch ON cv.chapter_id = ch.id";

        // التحقق من عمود order_number في course_chapters
        $stmt = $conn->query("DESCRIBE course_chapters");
        $ch_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $has_chapter_order = in_array('order_number', $ch_columns);

        if ($has_chapter_order) {
            $order_clause .= ", ch.order_number";
        }
    } else {
        $select_fields .= ", NULL as chapter_title";
    }

    // إضافة جدول مشاهدات الفيديو إذا كان موجوداً
    if ($tables_exist['video_watches']) {
        $select_fields .= ", COUNT(DISTINCT vw.id) as total_views, AVG(vw.watch_percentage) as avg_completion";
        $joins .= " LEFT JOIN video_watches vw ON cv.id = vw.video_id";
        $group_by = " GROUP BY cv.id";
    } else {
        $select_fields .= ", 0 as total_views, 0 as avg_completion";
        $group_by = "";
    }

    // إضافة ترتيب الفيديوهات
    if ($has_video_order) {
        $order_clause .= ", cv.order_number";
    } else {
        $order_clause .= ", cv.id";
    }

    $sql = "SELECT $select_fields FROM course_videos cv $joins WHERE $where_clause $group_by ORDER BY $order_clause";

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات الفيديوهات
    if ($tables_exist['video_watches']) {
        $stmt = $conn->prepare("
            SELECT
                COUNT(DISTINCT cv.id) as total_videos,
                COUNT(DISTINCT CASE WHEN cv.is_free = 1 THEN cv.id END) as free_videos,
                COUNT(DISTINCT CASE WHEN cv.is_free = 0 THEN cv.id END) as premium_videos,
                SUM(cv.duration) as total_duration,
                COUNT(DISTINCT vw.id) as total_views
            FROM course_videos cv
            INNER JOIN courses c ON cv.course_id = c.id
            LEFT JOIN video_watches vw ON cv.id = vw.video_id
            WHERE c.instructor_id = ?
        ");
    } else {
        $stmt = $conn->prepare("
            SELECT
                COUNT(DISTINCT cv.id) as total_videos,
                COUNT(DISTINCT CASE WHEN cv.is_free = 1 THEN cv.id END) as free_videos,
                COUNT(DISTINCT CASE WHEN cv.is_free = 0 THEN cv.id END) as premium_videos,
                SUM(cv.duration) as total_duration,
                0 as total_views
            FROM course_videos cv
            INNER JOIN courses c ON cv.course_id = c.id
            WHERE c.instructor_id = ?
        ");
    }
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    // التحقق من وجود الجداول المطلوبة
    try {
        $stmt = $conn->query("SHOW TABLES LIKE 'course_videos'");
        if ($stmt->rowCount() == 0) {
            $error_message = 'جدول الفيديوهات غير موجود. <a href="../fix_videos_tables.php" class="alert-link">انقر هنا لإنشاء الجداول المطلوبة</a>';
        } else {
            $error_message = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
        }
    } catch (Exception $ex) {
        $error_message = 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage();
    }

    $videos = [];
    $stats = ['total_videos' => 0, 'free_videos' => 0, 'premium_videos' => 0, 'total_duration' => 0, 'total_views' => 0];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="h3 mb-2">
            <i class="fas fa-video text-danger me-2"></i>
            إدارة الفيديوهات
        </h2>
        <p class="text-muted mb-0">رفع وإدارة فيديوهات الكورسات التعليمية</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadLocalVideoModal">
            <i class="fas fa-cloud-upload-alt me-1"></i>رفع من الجهاز
        </button>
        <a href="quick-upload-video.php" class="btn btn-success">
            <i class="fas fa-bolt me-1"></i>رفع رابط سريع
        </a>
        <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#uploadVideoModal">
            <i class="fas fa-link me-1"></i>رفع رابط
        </button>
        <button class="btn btn-outline-info" onclick="enableSorting()">
            <i class="fas fa-sort me-1"></i>إعادة ترتيب
        </button>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (isset($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!empty($errors)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <ul class="mb-0">
        <?php foreach ($errors as $error): ?>
        <li><?php echo htmlspecialchars($error); ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- إحصائيات الفيديوهات -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-danger bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-video text-danger fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['total_videos']; ?></h5>
                        <p class="text-muted mb-0">إجمالي الفيديوهات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-unlock text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['free_videos']; ?></h5>
                        <p class="text-muted mb-0">فيديوهات مجانية</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-lock text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo $stats['premium_videos']; ?></h5>
                        <p class="text-muted mb-0">فيديوهات مدفوعة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3">
                            <i class="fas fa-clock text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-1"><?php echo gmdate('H:i', $stats['total_duration'] * 60); ?></h5>
                        <p class="text-muted mb-0">إجمالي المدة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="course_id" class="form-label">الكورس</label>
                <select name="course_id" id="course_id" class="form-select" onchange="loadChapters()">
                    <option value="">جميع الكورسات</option>
                    <?php foreach ($instructor_courses as $course): ?>
                    <option value="<?php echo $course['id']; ?>" 
                            <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($course['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="chapter_id" class="form-label">الفصل</label>
                <select name="chapter_id" id="chapter_id" class="form-select">
                    <option value="">جميع الفصول</option>
                    <?php foreach ($chapters as $chapter): ?>
                    <option value="<?php echo $chapter['id']; ?>" 
                            <?php echo $chapter_filter == $chapter['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($chapter['title']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">النوع</label>
                <select name="status" id="status" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="free" <?php echo $status_filter == 'free' ? 'selected' : ''; ?>>مجاني</option>
                    <option value="premium" <?php echo $status_filter == 'premium' ? 'selected' : ''; ?>>مدفوع</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الفيديوهات -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الفيديوهات
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($videos)): ?>
        <div class="text-center py-5">
            <i class="fas fa-video text-muted" style="font-size: 3rem;"></i>
            <h5 class="mt-3 text-muted">لا توجد فيديوهات</h5>
            <p class="text-muted">ابدأ برفع فيديوهات تعليمية للكورسات</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadVideoModal">
                <i class="fas fa-upload me-1"></i>رفع فيديو جديد
            </button>
        </div>
        <?php else: ?>
        <div id="videosList">
            <?php
            $current_course = '';
            foreach ($videos as $video):
                if ($current_course !== $video['course_title']):
                    if ($current_course !== '') echo '</div>';
                    $current_course = $video['course_title'];
            ?>
            <div class="course-section">
                <div class="course-header bg-light p-3 border-bottom">
                    <h6 class="mb-0 text-primary">
                        <i class="fas fa-graduation-cap me-2"></i>
                        <?php echo htmlspecialchars($current_course); ?>
                    </h6>
                </div>
            <?php endif; ?>

            <div class="video-item border-bottom p-3" data-video-id="<?php echo $video['id']; ?>">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <div class="video-thumbnail position-relative">
                            <div class="bg-dark rounded d-flex align-items-center justify-content-center"
                                 style="width: 60px; height: 40px;">
                                <i class="fas fa-play text-white"></i>
                            </div>
                            <div class="position-absolute top-0 start-0">
                                <?php if ($video['is_free']): ?>
                                <span class="badge bg-success" style="font-size: 0.6rem;">مجاني</span>
                                <?php else: ?>
                                <span class="badge bg-warning" style="font-size: 0.6rem;">مدفوع</span>
                                <?php endif; ?>
                            </div>
                            <div class="position-absolute top-0 end-0">
                                <?php if (isset($video['video_file_path']) && !empty($video['video_file_path'])): ?>
                                <span class="badge bg-primary" style="font-size: 0.6rem;">محلي</span>
                                <?php else: ?>
                                <span class="badge bg-info" style="font-size: 0.6rem;">رابط</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h6 class="mb-1"><?php echo htmlspecialchars($video['title']); ?></h6>
                        <?php if ($video['description']): ?>
                        <p class="text-muted mb-1 small">
                            <?php echo mb_substr(htmlspecialchars($video['description']), 0, 100) . '...'; ?>
                        </p>
                        <?php endif; ?>
                        <div class="d-flex gap-3 small text-muted">
                            <?php if ($video['chapter_title']): ?>
                            <span><i class="fas fa-folder me-1"></i><?php echo htmlspecialchars($video['chapter_title']); ?></span>
                            <?php endif; ?>
                            <span><i class="fas fa-clock me-1"></i>
                                <?php
                                if ($video['duration'] > 0) {
                                    echo gmdate('H:i:s', $video['duration'] * 60);
                                } else {
                                    echo 'غير محدد';
                                }
                                ?>
                            </span>
                            <?php if (isset($video['order_number']) && $video['order_number']): ?>
                            <span><i class="fas fa-sort me-1"></i>ترتيب: <?php echo $video['order_number']; ?></span>
                            <?php endif; ?>
                            <?php if (isset($video['file_size']) && $video['file_size']): ?>
                            <span><i class="fas fa-hdd me-1"></i><?php echo number_format($video['file_size'] / (1024*1024), 1); ?> ميجابايت</span>
                            <?php endif; ?>
                            <span><i class="fas fa-calendar me-1"></i><?php echo date('Y-m-d', strtotime($video['created_at'])); ?></span>
                        </div>
                    </div>

                    <div class="col-md-2 text-center">
                        <div class="mb-1">
                            <strong class="text-primary"><?php echo $video['total_views']; ?></strong>
                            <br><small class="text-muted">مشاهدة</small>
                        </div>
                        <?php if ($video['avg_completion']): ?>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-success" style="width: <?php echo $video['avg_completion']; ?>%"></div>
                        </div>
                        <small class="text-muted"><?php echo number_format($video['avg_completion'], 1); ?>% إكمال</small>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-3">
                        <div class="btn-group w-100" role="group">
                            <?php if (isset($video['video_file_path']) && !empty($video['video_file_path'])): ?>
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="previewLocalVideo(<?php echo $video['id']; ?>)" title="معاينة">
                                <i class="fas fa-play"></i>
                            </button>
                            <?php else: ?>
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="previewVideo('<?php echo htmlspecialchars($video['video_url']); ?>')" title="معاينة">
                                <i class="fas fa-play"></i>
                            </button>
                            <?php endif; ?>
                            <button class="btn btn-sm btn-outline-success"
                                    onclick="viewAnalytics(<?php echo $video['id']; ?>)" title="إحصائيات">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning"
                                    onclick="editVideo(<?php echo $video['id']; ?>)" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger"
                                    onclick="deleteVideo(<?php echo $video['id']; ?>)" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <?php endforeach; ?>
            <?php if ($current_course !== '') echo '</div>'; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal رفع فيديو جديد -->
<div class="modal fade" id="uploadVideoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفع فيديو جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="upload_video">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="upload_course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                            <select name="course_id" id="upload_course_id" class="form-select" required onchange="loadUploadChapters()">
                                <option value="">اختر الكورس</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>">
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="upload_chapter_id" class="form-label">الفصل</label>
                            <select name="chapter_id" id="upload_chapter_id" class="form-select">
                                <option value="">بدون فصل</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="upload_title" class="form-label">عنوان الفيديو <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="upload_title" class="form-control" required>
                        </div>
                        <div class="col-12">
                            <label for="upload_description" class="form-label">وصف الفيديو</label>
                            <textarea name="description" id="upload_description" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="col-12">
                            <label for="upload_video_url" class="form-label">رابط الفيديو <span class="text-danger">*</span></label>
                            <input type="url" name="video_url" id="upload_video_url" class="form-control" required
                                   placeholder="https://www.youtube.com/watch?v=... أو أي رابط فيديو آخر">
                            <div class="form-text">
                                <i class="fas fa-info-circle text-info me-1"></i>
                                يمكنك استخدام روابط من YouTube, Vimeo, أو أي منصة أخرى
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="upload_duration" class="form-label">المدة (بالدقائق)</label>
                            <input type="number" name="duration" id="upload_duration" class="form-control" min="1" value="10">
                        </div>
                        <div class="col-md-4">
                            <label for="upload_order" class="form-label">ترتيب الفيديو</label>
                            <input type="number" name="order_number" id="upload_order" class="form-control" min="1" value="1">
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mt-4">
                                <input type="checkbox" name="is_free" id="upload_is_free" class="form-check-input">
                                <label for="upload_is_free" class="form-check-label">
                                    <i class="fas fa-unlock text-success me-1"></i>
                                    فيديو مجاني
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>رفع الفيديو
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal معاينة الفيديو -->
<div class="modal fade" id="previewVideoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة الفيديو</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="videoPreviewContainer" class="text-center">
                    <!-- سيتم إدراج الفيديو هنا -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحميل الفصول عند اختيار كورس في الفلتر
function loadChapters() {
    const courseId = document.getElementById('course_id').value;
    const chapterSelect = document.getElementById('chapter_id');

    chapterSelect.innerHTML = '<option value="">جميع الفصول</option>';

    if (courseId) {
        fetch(`../ajax/get_chapters.php?course_id=${courseId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    data.chapters.forEach(chapter => {
                        const option = document.createElement('option');
                        option.value = chapter.id;
                        option.textContent = chapter.title;
                        chapterSelect.appendChild(option);
                    });
                }
            })
            .catch(error => console.error('Error:', error));
    }
}

// تحميل الفصول عند اختيار كورس في نموذج الرفع
function loadUploadChapters() {
    const courseId = document.getElementById('upload_course_id').value;
    const chapterSelect = document.getElementById('upload_chapter_id');

    chapterSelect.innerHTML = '<option value="">بدون فصل</option>';

    if (courseId) {
        fetch(`../ajax/get_chapters.php?course_id=${courseId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    data.chapters.forEach(chapter => {
                        const option = document.createElement('option');
                        option.value = chapter.id;
                        option.textContent = chapter.title;
                        chapterSelect.appendChild(option);
                    });
                }
            })
            .catch(error => console.error('Error:', error));
    }
}

// معاينة الفيديو المحلي
function previewLocalVideo(videoId) {
    const container = document.getElementById('videoPreviewContainer');

    container.innerHTML = `
        <video width="100%" height="400" controls>
            <source src="../view_video.php?video_id=${videoId}" type="video/mp4">
            المتصفح لا يدعم تشغيل الفيديو
        </video>
    `;

    new bootstrap.Modal(document.getElementById('previewVideoModal')).show();
}

// معاينة الفيديو من الرابط
function previewVideo(videoUrl) {
    const container = document.getElementById('videoPreviewContainer');

    if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
        // استخراج معرف الفيديو من YouTube
        let videoId = '';
        if (videoUrl.includes('youtube.com/watch?v=')) {
            videoId = videoUrl.split('v=')[1].split('&')[0];
        } else if (videoUrl.includes('youtu.be/')) {
            videoId = videoUrl.split('youtu.be/')[1].split('?')[0];
        }

        if (videoId) {
            container.innerHTML = `
                <iframe width="100%" height="400"
                        src="https://www.youtube.com/embed/${videoId}"
                        frameborder="0" allowfullscreen>
                </iframe>
            `;
        }
    } else if (videoUrl.includes('vimeo.com')) {
        // استخراج معرف الفيديو من Vimeo
        const videoId = videoUrl.split('/').pop();
        container.innerHTML = `
            <iframe width="100%" height="400"
                    src="https://player.vimeo.com/video/${videoId}"
                    frameborder="0" allowfullscreen>
            </iframe>
        `;
    } else {
        // فيديو مباشر
        container.innerHTML = `
            <video width="100%" height="400" controls>
                <source src="${videoUrl}" type="video/mp4">
                المتصفح لا يدعم تشغيل الفيديو
            </video>
        `;
    }

    const modal = new bootstrap.Modal(document.getElementById('previewVideoModal'));
    modal.show();
}

// عرض إحصائيات الفيديو
function viewAnalytics(videoId) {
    window.location.href = `video-analytics.php?id=${videoId}`;
}

// تعديل الفيديو
function editVideo(videoId) {
    window.location.href = `edit-video.php?id=${videoId}`;
}

// حذف الفيديو
function deleteVideo(videoId) {
    if (confirm('هل أنت متأكد من حذف هذا الفيديو؟\n\nسيتم حذف جميع بيانات المشاهدة المرتبطة به.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_video">
            <input type="hidden" name="video_id" value="${videoId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// تفعيل إعادة الترتيب
function enableSorting() {
    alert('ميزة إعادة الترتيب ستكون متاحة قريباً');
}

// تحديث معاينة الرابط عند الكتابة
document.addEventListener('DOMContentLoaded', function() {
    const videoUrlInput = document.getElementById('upload_video_url');
    if (videoUrlInput) {
        videoUrlInput.addEventListener('input', function() {
            const url = this.value;
            const container = this.parentNode;

            // إزالة المعاينة السابقة
            const existingPreview = container.querySelector('.url-preview');
            if (existingPreview) {
                existingPreview.remove();
            }

            if (url) {
                const preview = document.createElement('div');
                preview.className = 'url-preview mt-2 p-2 bg-light rounded';

                if (url.includes('youtube.com') || url.includes('youtu.be')) {
                    preview.innerHTML = '<i class="fab fa-youtube text-danger me-1"></i>فيديو YouTube';
                } else if (url.includes('vimeo.com')) {
                    preview.innerHTML = '<i class="fab fa-vimeo text-info me-1"></i>فيديو Vimeo';
                } else if (url.match(/\.(mp4|webm|ogg)$/i)) {
                    preview.innerHTML = '<i class="fas fa-file-video text-primary me-1"></i>ملف فيديو مباشر';
                } else {
                    preview.innerHTML = '<i class="fas fa-link text-secondary me-1"></i>رابط فيديو';
                }

                container.appendChild(preview);
            }
        });
    }
});

// التحقق من صحة الملف المحلي
function validateLocalFile() {
    const fileInput = document.getElementById('local_video_file');
    const fileInfo = document.getElementById('local_file_info');
    const uploadBtn = document.getElementById('local_upload_btn');

    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        const maxSize = 1000 * 1024 * 1024; // 1 GB
        const allowedExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];

        let isValid = true;
        let info = '<div class="alert alert-info small">';

        // معلومات الملف
        info += '<strong>اسم الملف:</strong> ' + file.name + '<br>';
        info += '<strong>الحجم:</strong> ' + (file.size / (1024*1024)).toFixed(2) + ' ميجابايت<br>';
        info += '<strong>النوع:</strong> ' + file.type + '<br>';

        // التحقق من النوع
        const fileExtension = file.name.split('.').pop().toLowerCase();
        if (!allowedExtensions.includes(fileExtension)) {
            info += '<span class="text-danger"><i class="fas fa-times me-1"></i>نوع الملف غير مدعوم</span><br>';
            isValid = false;
        } else {
            info += '<span class="text-success"><i class="fas fa-check me-1"></i>نوع الملف مدعوم</span><br>';
        }

        // التحقق من الحجم
        if (file.size > maxSize) {
            info += '<span class="text-danger"><i class="fas fa-times me-1"></i>حجم الملف كبير جداً</span>';
            isValid = false;
        } else {
            info += '<span class="text-success"><i class="fas fa-check me-1"></i>حجم الملف مناسب</span>';
        }

        info += '</div>';
        fileInfo.innerHTML = info;
        uploadBtn.disabled = !isValid;

        if (isValid) {
            uploadBtn.innerHTML = '<i class="fas fa-cloud-upload-alt me-1"></i>رفع الفيديو - جاهز!';
            uploadBtn.classList.remove('btn-secondary');
            uploadBtn.classList.add('btn-primary');
        } else {
            uploadBtn.innerHTML = '<i class="fas fa-times me-1"></i>ملف غير صالح';
            uploadBtn.classList.remove('btn-primary');
            uploadBtn.classList.add('btn-secondary');
        }
    }
}

// تقدم رفع الملف المحلي
document.getElementById('localUploadForm').addEventListener('submit', function(e) {
    const uploadBtn = document.getElementById('local_upload_btn');
    const progress = document.getElementById('local_upload_progress');

    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الرفع...';
    progress.style.display = 'block';

    // محاكاة تقدم الرفع
    let width = 0;
    const interval = setInterval(() => {
        width += Math.random() * 3;
        if (width >= 95) {
            clearInterval(interval);
        }
        progress.querySelector('.progress-bar').style.width = Math.min(width, 95) + '%';
    }, 500);
});
</script>

<?php include '../includes/footer.php'; ?>
                <div class="modal-body">
                    <input type="hidden" name="action" value="upload_video">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="upload_course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                            <select name="course_id" id="upload_course_id" class="form-select" required onchange="loadUploadChapters()">
                                <option value="">-- اختر كورس --</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>">
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="upload_chapter_id" class="form-label">الفصل (اختياري)</label>
                            <select name="chapter_id" id="upload_chapter_id" class="form-select" disabled>
                                <option value="">-- اختر الكورس أولاً --</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان الفيديو <span class="text-danger">*</span></label>
                        <input type="text" name="title" id="title" class="form-control"
                               placeholder="مثال: مقدمة في البرمجة - الدرس الأول" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الفيديو</label>
                        <textarea name="description" id="description" class="form-control" rows="3"
                                  placeholder="وصف مختصر عن محتوى الفيديو..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="video_url" class="form-label">رابط الفيديو <span class="text-danger">*</span></label>
                        <input type="url" name="video_url" id="video_url" class="form-control"
                               placeholder="https://youtube.com/watch?v=... أو رابط مباشر" required>
                        <div class="form-text">يمكنك استخدام روابط YouTube, Vimeo, أو روابط مباشرة للفيديوهات</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="duration" class="form-label">مدة الفيديو (بالدقائق)</label>
                            <input type="number" name="duration" id="duration" class="form-control"
                                   min="0" step="0.1" placeholder="مثال: 15.5">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="order_number" class="form-label">ترتيب الفيديو</label>
                            <input type="number" name="order_number" id="order_number" class="form-control"
                                   value="1" min="1" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="is_free" id="is_free">
                            <label class="form-check-label" for="is_free">
                                <i class="fas fa-unlock text-success me-1"></i>
                                فيديو مجاني (متاح للجميع)
                            </label>
                        </div>
                        <small class="text-muted">الفيديوهات المجانية متاحة للجميع، والمدفوعة تتطلب اشتراك</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">رفع الفيديو</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal حذف فيديو -->
<div class="modal fade" id="deleteVideoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_video">
                    <input type="hidden" name="video_id" id="deleteVideoId">

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من حذف هذا الفيديو؟
                    </div>

                    <p class="text-muted">
                        سيتم حذف الفيديو وجميع إحصائيات المشاهدة المرتبطة به نهائياً.
                        هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal رفع فيديو من الجهاز -->
<div class="modal fade" id="uploadLocalVideoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cloud-upload-alt text-primary me-2"></i>
                    رفع فيديو من الجهاز
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="localUploadForm">
                <input type="hidden" name="action" value="upload_local_video">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> الحد الأقصى لحجم الملف هو 1 جيجابايت.
                        الأنواع المدعومة: MP4, AVI, MOV, WMV, FLV, WebM, MKV
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="local_course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                            <select name="course_id" id="local_course_id" class="form-select" required>
                                <option value="">اختر الكورس</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>">
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                <input type="checkbox" name="is_free" id="local_is_free" class="form-check-input">
                                <label for="local_is_free" class="form-check-label">
                                    <i class="fas fa-unlock text-success me-1"></i>
                                    فيديو مجاني
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <label for="local_title" class="form-label">عنوان الفيديو <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="local_title" class="form-control" required>
                        </div>
                        <div class="col-12">
                            <label for="local_description" class="form-label">وصف الفيديو</label>
                            <textarea name="description" id="local_description" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="col-12">
                            <label for="local_video_file" class="form-label">ملف الفيديو <span class="text-danger">*</span></label>
                            <input type="file" name="video_file" id="local_video_file" class="form-control"
                                   accept="video/*" required onchange="validateLocalFile()">
                            <div class="form-text">
                                <i class="fas fa-info-circle text-info me-1"></i>
                                الأنواع المدعومة: MP4, AVI, MOV, WMV, FLV, WebM, MKV | الحد الأقصى: 1 جيجابايت
                            </div>
                            <div id="local_file_info" class="mt-2"></div>
                        </div>
                        <div class="col-12">
                            <div class="progress" id="local_upload_progress" style="display: none;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="local_upload_btn">
                        <i class="fas fa-cloud-upload-alt me-1"></i>رفع الفيديو
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal معاينة الفيديو -->
<div class="modal fade" id="previewVideoModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة الفيديو</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="videoPreviewContainer" class="text-center">
                    <!-- سيتم إدراج الفيديو هنا -->
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
<script>
// تحميل فصول الكورس في فلتر البحث
function loadChapters() {
    const courseId = document.getElementById('course_id').value;
    const chapterSelect = document.getElementById('chapter_id');

    if (!courseId) {
        chapterSelect.innerHTML = '<option value="">جميع الفصول</option>';
        return;
    }

    // محاكاة تحميل الفصول (يمكن تنفيذ هذا عبر AJAX)
    chapterSelect.innerHTML = '<option value="">-- جاري التحميل... --</option>';

    setTimeout(() => {
        chapterSelect.innerHTML = `
            <option value="">جميع الفصول</option>
            <option value="1">الفصل الأول: المقدمة</option>
            <option value="2">الفصل الثاني: الأساسيات</option>
            <option value="3">الفصل الثالث: التطبيق العملي</option>
        `;
    }, 500);
}

// تحميل فصول الكورس في نموذج الرفع
function loadUploadChapters() {
    const courseId = document.getElementById('upload_course_id').value;
    const chapterSelect = document.getElementById('upload_chapter_id');

    if (!courseId) {
        chapterSelect.disabled = true;
        chapterSelect.innerHTML = '<option value="">-- اختر الكورس أولاً --</option>';
        return;
    }

    chapterSelect.disabled = false;
    chapterSelect.innerHTML = '<option value="">-- جاري التحميل... --</option>';

    setTimeout(() => {
        chapterSelect.innerHTML = `
            <option value="">بدون فصل</option>
            <option value="1">الفصل الأول: المقدمة</option>
            <option value="2">الفصل الثاني: الأساسيات</option>
            <option value="3">الفصل الثالث: التطبيق العملي</option>
        `;
    }, 500);
}

// معاينة الفيديو
function previewVideo(videoUrl) {
    const container = document.getElementById('videoPreviewContainer');

    // تحديد نوع الفيديو وإنشاء المشغل المناسب
    if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
        const videoId = extractYouTubeId(videoUrl);
        container.innerHTML = `
            <iframe width="100%" height="400"
                    src="https://www.youtube.com/embed/${videoId}"
                    frameborder="0" allowfullscreen></iframe>
        `;
    } else if (videoUrl.includes('vimeo.com')) {
        const videoId = extractVimeoId(videoUrl);
        container.innerHTML = `
            <iframe width="100%" height="400"
                    src="https://player.vimeo.com/video/${videoId}"
                    frameborder="0" allowfullscreen></iframe>
        `;
    } else {
        container.innerHTML = `
            <video width="100%" height="400" controls>
                <source src="${videoUrl}" type="video/mp4">
                متصفحك لا يدعم تشغيل الفيديو
            </video>
        `;
    }

    new bootstrap.Modal(document.getElementById('previewVideoModal')).show();
}

// استخراج معرف YouTube
function extractYouTubeId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}

// استخراج معرف Vimeo
function extractVimeoId(url) {
    const regExp = /(?:vimeo)\.com.*(?:videos|video|channels|)\/([\d]+)/i;
    const match = url.match(regExp);
    return match ? match[1] : null;
}

// عرض إحصائيات الفيديو
function viewAnalytics(videoId) {
    window.open(`video-analytics.php?id=${videoId}`, '_blank');
}

// تعديل فيديو
function editVideo(videoId) {
    window.location.href = `edit-video.php?id=${videoId}`;
}

// حذف فيديو
function deleteVideo(videoId) {
    document.getElementById('deleteVideoId').value = videoId;
    new bootstrap.Modal(document.getElementById('deleteVideoModal')).show();
}

// تفعيل إعادة الترتيب
function enableSorting() {
    const videosList = document.getElementById('videosList');

    if (videosList.classList.contains('sortable-enabled')) {
        // إلغاء تفعيل الترتيب
        videosList.classList.remove('sortable-enabled');
        document.querySelector('[onclick="enableSorting()"]').innerHTML = '<i class="fas fa-sort me-1"></i>إعادة ترتيب';
        return;
    }

    // تفعيل الترتيب
    videosList.classList.add('sortable-enabled');
    document.querySelector('[onclick="enableSorting()"]').innerHTML = '<i class="fas fa-save me-1"></i>حفظ الترتيب';

    const courseSections = document.querySelectorAll('.course-section');
    courseSections.forEach(section => {
        const videoItems = section.querySelectorAll('.video-item');
        if (videoItems.length > 0) {
            Sortable.create(section, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                filter: '.course-header',
                onEnd: function(evt) {
                    saveVideoOrder();
                }
            });
        }
    });
}

// حفظ ترتيب الفيديوهات
function saveVideoOrder() {
    const videoItems = document.querySelectorAll('.video-item');
    const orders = {};

    videoItems.forEach((item, index) => {
        const videoId = item.dataset.videoId;
        orders[videoId] = index + 1;
    });

    // إرسال البيانات عبر AJAX
    const formData = new FormData();
    formData.append('action', 'update_order');
    Object.keys(orders).forEach(videoId => {
        formData.append(`video_orders[${videoId}]`, orders[videoId]);
    });

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(() => {
        location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء حفظ الترتيب');
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحسين نموذج الرفع
    const uploadForm = document.querySelector('#uploadVideoModal form');
    if (uploadForm) {
        uploadForm.addEventListener('submit', function() {
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الرفع...';
            }
        });
    }

    // تحسين رابط الفيديو
    const videoUrlInput = document.getElementById('video_url');
    if (videoUrlInput) {
        videoUrlInput.addEventListener('blur', function() {
            const url = this.value;
            if (url && (url.includes('youtube.com') || url.includes('youtu.be'))) {
                // محاولة استخراج معلومات الفيديو من YouTube
                const videoId = extractYouTubeId(url);
                if (videoId) {
                    // يمكن هنا استخدام YouTube API لجلب العنوان والوصف
                    console.log('YouTube Video ID:', videoId);
                }
            }
        });
    }

    // إضافة تأثيرات بصرية للفيديوهات
    const videoItems = document.querySelectorAll('.video-item');
    videoItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(0,123,255,0.05)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // تحسين البحث المباشر
    const searchInputs = document.querySelectorAll('select[name="course_id"], select[name="chapter_id"], select[name="status"]');
    searchInputs.forEach(input => {
        input.addEventListener('change', function() {
            // يمكن إضافة بحث مباشر عبر AJAX
        });
    });
});

// دوال مساعدة
function bulkUpload() {
    window.location.href = 'bulk-upload-videos.php';
}

function exportVideoList() {
    window.open('export-videos.php', '_blank');
}

function videoStatistics() {
    window.location.href = 'video-statistics.php';
}

// تحديث تلقائي للإحصائيات
setInterval(function() {
    // يمكن إضافة AJAX لتحديث الإحصائيات
    console.log('تحديث إحصائيات الفيديوهات...');
}, 300000); // كل 5 دقائق
</script>

<style>
.video-item {
    transition: all 0.3s ease;
}

.video-item:hover {
    background-color: rgba(0,123,255,0.05);
    transform: translateX(-2px);
}

.video-thumbnail {
    position: relative;
}

.video-thumbnail .badge {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
}

.course-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid var(--bs-primary);
}

.course-section {
    margin-bottom: 1rem;
}

.sortable-enabled .video-item {
    cursor: move;
    border: 2px dashed #dee2e6;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
}

.sortable-ghost {
    opacity: 0.5;
    background-color: rgba(0,123,255,0.1);
}

.progress {
    height: 4px;
    border-radius: 2px;
}

.btn-group .btn {
    border-radius: 0.375rem;
    margin-left: 0.25rem;
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.modal-xl {
    max-width: 90%;
}

@media (max-width: 768px) {
    .video-item .row {
        flex-direction: column;
    }

    .video-item .col-md-1,
    .video-item .col-md-6,
    .video-item .col-md-2,
    .video-item .col-md-3 {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .btn-group {
        display: flex;
        width: 100%;
    }

    .btn-group .btn {
        flex: 1;
        margin-left: 0;
        margin-right: 0.25rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }

    .course-header h6 {
        font-size: 0.9rem;
    }
}

/* تحسين عرض الفيديوهات */
.video-thumbnail {
    border-radius: 0.375rem;
    overflow: hidden;
}

.video-thumbnail .bg-dark {
    transition: all 0.3s ease;
}

.video-thumbnail:hover .bg-dark {
    background-color: var(--bs-primary) !important;
}

/* تحسين الإحصائيات */
.card-body .text-primary {
    font-weight: 600;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* تحسين النماذج */
.form-check-input:checked {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
}

.form-text {
    font-size: 0.8rem;
    color: var(--bs-gray-600);
}

/* تحسين التبويبات */
.nav-tabs .nav-link {
    border-radius: 0.5rem 0.5rem 0 0;
}

.nav-tabs .nav-link.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}
</style>

<?php include 'includes/footer.php'; ?>
