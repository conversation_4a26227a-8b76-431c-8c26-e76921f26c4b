<?php
/**
 * مدير قاعدة البيانات المحسن - نسخة مصححة
 */

class DatabaseManagerFixed {
    private $conn;
    private $lastInsertId;
    private $affectedRows;
    private $inTransaction = false;
    
    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }
    
    public function beginTransaction() {
        if (!$this->inTransaction) {
            $result = $this->conn->beginTransaction();
            $this->inTransaction = true;
            return $result;
        }
        return true;
    }
    
    public function commit() {
        if ($this->inTransaction) {
            $result = $this->conn->commit();
            $this->inTransaction = false;
            return $result;
        }
        return true;
    }
    
    public function rollback() {
        if ($this->inTransaction) {
            $result = $this->conn->rollback();
            $this->inTransaction = false;
            return $result;
        }
        return true;
    }
    
    public function create($table, $data, $returnId = true) {
        try {
            $columns = array_keys($data);
            $placeholders = ":" . implode(", :", $columns);
            
            $sql = "INSERT INTO {$table} (" . implode(", ", $columns) . ") VALUES ({$placeholders})";
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute($data);
            
            if ($result) {
                $this->lastInsertId = $this->conn->lastInsertId();
                $this->affectedRows = $stmt->rowCount();
                return $returnId ? $this->lastInsertId : true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            error_log("Create Error in {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    public function find($table, $id, $columns = "*") {
        try {
            $sql = "SELECT {$columns} FROM {$table} WHERE id = ? LIMIT 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Find Error in {$table}: " . $e->getMessage());
            return false;
        }
    }
    
    public function getAll($table, $columns = "*", $orderBy = "id DESC", $limit = null) {
        try {
            $sql = "SELECT {$columns} FROM {$table}";
            
            if ($orderBy) {
                $sql .= " ORDER BY {$orderBy}";
            }
            
            if ($limit) {
                $sql .= " LIMIT {$limit}";
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("GetAll Error in {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    public function getWhere($table, $conditions, $columns = "*", $orderBy = null, $limit = null) {
        try {
            $whereClause = $this->buildWhereClause($conditions);
            $sql = "SELECT {$columns} FROM {$table} WHERE {$whereClause['sql']}";

            if ($orderBy) {
                $sql .= " ORDER BY {$orderBy}";
            }

            if ($limit) {
                $sql .= " LIMIT {$limit}";
            }

            $stmt = $this->conn->prepare($sql);
            $stmt->execute($whereClause['params']);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("GetWhere Error in {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    public function count($table, $conditions = []) {
        try {
            $sql = "SELECT COUNT(*) as total FROM {$table}";
            $params = [];
            
            if (!empty($conditions)) {
                $whereClause = $this->buildWhereClause($conditions);
                $sql .= " WHERE {$whereClause['sql']}";
                $params = $whereClause['params'];
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result["total"];
            
        } catch (PDOException $e) {
            error_log("Count Error in {$table}: " . $e->getMessage());
            return 0;
        }
    }
    
    public function search($table, $searchTerm, $searchColumns, $conditions = [], $columns = "*", $limit = 50) {
        try {
            $searchConditions = [];
            $params = [];
            
            foreach ($searchColumns as $column) {
                $searchConditions[] = "{$column} LIKE ?";
                $params[] = "%{$searchTerm}%";
            }
            
            $sql = "SELECT {$columns} FROM {$table} WHERE (" . implode(" OR ", $searchConditions) . ")";
            
            if (!empty($conditions)) {
                $whereClause = $this->buildWhereClause($conditions);
                $sql .= " AND {$whereClause['sql']}";
                $params = array_merge($params, $whereClause['params']);
            }
            
            $sql .= " LIMIT {$limit}";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("Search Error in {$table}: " . $e->getMessage());
            return [];
        }
    }
    
    private function buildWhereClause($conditions) {
        $whereParts = [];
        $params = [];
        
        foreach ($conditions as $key => $value) {
            if (is_array($value)) {
                $placeholders = str_repeat("?,", count($value) - 1) . "?";
                $whereParts[] = "{$key} IN ({$placeholders})";
                $params = array_merge($params, $value);
            } elseif (strpos($key, " ") !== false) {
                $whereParts[] = $key;
                $params[] = $value;
            } else {
                $whereParts[] = "{$key} = ?";
                $params[] = $value;
            }
        }
        
        return [
            'sql' => implode(' AND ', $whereParts),
            'params' => $params
        ];
    }
}

// إنشاء مثيل محسن
$dbFixed = new DatabaseManagerFixed();
?>