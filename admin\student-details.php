<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$student_id = (int)($_GET['id'] ?? 0);

if (!$student_id) {
    header('Location: manage-students.php');
    exit;
}

// جلب تفاصيل الطالب
$student = fetchOne("
    SELECT u.*, 
           COUNT(DISTINCT ce.id) as enrolled_courses,
           COUNT(DISTINCT sa.id) as total_attendance,
           AVG(CASE WHEN sa.status = 'present' THEN 1 ELSE 0 END) * 100 as attendance_rate
    FROM users u
    LEFT JOIN course_enrollments ce ON u.id = ce.student_id
    LEFT JOIN session_attendance sa ON u.id = sa.student_id
    WHERE u.id = ? AND u.role = 'student'
    GROUP BY u.id
", [$student_id]);

if (!$student) {
    header('Location: manage-students.php');
    exit;
}

// جلب الكورسات المسجل بها
$enrollments = fetchAll("
    SELECT 
        c.id, c.title, c.image, c.course_type, c.price,
        ce.enrolled_at, ce.payment_status, ce.payment_amount,
        ce.progress_percentage, ce.status as enrollment_status,
        u.name as instructor_name,
        COUNT(DISTINCT s.id) as total_sessions,
        COUNT(DISTINCT sa.id) as attended_sessions
    FROM course_enrollments ce
    JOIN courses c ON ce.course_id = c.id
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN sessions s ON c.id = s.course_id
    LEFT JOIN session_attendance sa ON s.id = sa.session_id AND sa.student_id = ce.student_id AND sa.status = 'present'
    WHERE ce.student_id = ?
    GROUP BY c.id, ce.id
    ORDER BY ce.enrolled_at DESC
", [$student_id]);

// جلب سجل الحضور
$attendance_records = fetchAll("
    SELECT 
        s.id as session_id, s.title as session_title, s.session_date, s.start_time, s.end_time,
        c.title as course_title,
        sa.status, sa.attended_at, sa.notes
    FROM session_attendance sa
    JOIN sessions s ON sa.session_id = s.id
    JOIN courses c ON s.course_id = c.id
    WHERE sa.student_id = ?
    ORDER BY s.session_date DESC, s.start_time DESC
    LIMIT 20
", [$student_id]);

// جلب الأنشطة الأخيرة
$activities = fetchAll("
    SELECT 
        activity_type, description, created_at
    FROM user_activities
    WHERE user_id = ?
    ORDER BY created_at DESC
    LIMIT 10
", [$student_id]);

$pageTitle = 'تفاصيل الطالب: ' . $student['name'];
include 'includes/header.php';
?>

<div class="row">
    <!-- معلومات الطالب الأساسية -->
    <div class="col-lg-4">
        <div class="card-admin mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات الطالب
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <?php if (isset($student['profile_image']) && $student['profile_image']): ?>
                        <img src="../<?php echo htmlspecialchars($student['profile_image']); ?>" 
                             alt="صورة الطالب" class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
                    <?php else: ?>
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                             style="width: 100px; height: 100px; font-size: 2rem;">
                            <?php echo strtoupper(substr($student['name'], 0, 1)); ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <h4 class="mb-1"><?php echo htmlspecialchars($student['name']); ?></h4>
                <p class="text-muted mb-3"><?php echo htmlspecialchars($student['email']); ?></p>
                
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <h5 class="text-primary mb-0"><?php echo $student['enrolled_courses']; ?></h5>
                        <small class="text-muted">كورس</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-success mb-0"><?php echo $student['total_attendance']; ?></h5>
                        <small class="text-muted">جلسة</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-warning mb-0"><?php echo number_format($student['attendance_rate'], 1); ?>%</h5>
                        <small class="text-muted">حضور</small>
                    </div>
                </div>
                
                <div class="border-top pt-3">
                    <div class="row">
                        <div class="col-6 text-start">
                            <small class="text-muted">الهاتف:</small><br>
                            <span><?php echo htmlspecialchars($student['phone'] ?? 'غير محدد'); ?></span>
                        </div>
                        <div class="col-6 text-start">
                            <small class="text-muted">تاريخ التسجيل:</small><br>
                            <span><?php echo date('Y-m-d', strtotime($student['created_at'])); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>معدل الحضور</span>
                        <span class="fw-bold"><?php echo number_format($student['attendance_rate'], 1); ?>%</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-success" style="width: <?php echo $student['attendance_rate']; ?>%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>الكورسات النشطة</span>
                        <span class="fw-bold"><?php echo count(array_filter($enrollments, function($e) { return $e['enrollment_status'] === 'active'; })); ?></span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>إجمالي المدفوعات</span>
                        <span class="fw-bold"><?php echo number_format(array_sum(array_column($enrollments, 'payment_amount')), 2); ?> ر.س</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- المحتوى الرئيسي -->
    <div class="col-lg-8">
        <!-- الكورسات المسجل بها -->
        <div class="card-admin mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-book me-2"></i>
                    الكورسات المسجل بها (<?php echo count($enrollments); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($enrollments)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-book fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا يوجد كورسات</h6>
                        <p class="text-muted">لم يسجل الطالب في أي كورس بعد</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الكورس</th>
                                    <th>المدرب</th>
                                    <th>الحضور</th>
                                    <th>التقدم</th>
                                    <th>الدفع</th>
                                    <th>التسجيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($enrollments as $enrollment): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if ($enrollment['image']): ?>
                                                <img src="../<?php echo htmlspecialchars($enrollment['image']); ?>" 
                                                     alt="Course" class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center me-2" 
                                                     style="width: 40px; height: 40px; font-size: 0.8rem;">
                                                    <i class="fas fa-book"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <h6 class="mb-0"><?php echo htmlspecialchars($enrollment['title']); ?></h6>
                                                <small class="text-muted">
                                                    <span class="badge bg-<?php echo $enrollment['course_type'] === 'paid' ? 'warning' : 'success'; ?>">
                                                        <?php echo $enrollment['course_type'] === 'paid' ? 'مدفوع' : 'مجاني'; ?>
                                                    </span>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($enrollment['instructor_name']); ?></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $enrollment['attended_sessions']; ?>/<?php echo $enrollment['total_sessions']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" style="width: <?php echo $enrollment['progress_percentage']; ?>%">
                                                <?php echo number_format($enrollment['progress_percentage'], 1); ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $enrollment['payment_status'] === 'completed' ? 'success' : 'warning'; ?>">
                                            <?php echo $enrollment['payment_status']; ?>
                                        </span><br>
                                        <small><?php echo number_format($enrollment['payment_amount'], 2); ?> ر.س</small>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($enrollment['enrolled_at'])); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- سجل الحضور -->
        <div class="card-admin mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    سجل الحضور الأخير
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($attendance_records)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا يوجد سجل حضور</h6>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الجلسة</th>
                                    <th>الكورس</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>وقت الحضور</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($attendance_records as $record): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($record['session_title']); ?></td>
                                    <td><?php echo htmlspecialchars($record['course_title']); ?></td>
                                    <td>
                                        <?php echo date('Y-m-d', strtotime($record['session_date'])); ?><br>
                                        <small class="text-muted">
                                            <?php echo date('H:i', strtotime($record['start_time'])); ?> - 
                                            <?php echo date('H:i', strtotime($record['end_time'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $record['status'] === 'present' ? 'success' : 'danger'; ?>">
                                            <?php echo $record['status'] === 'present' ? 'حاضر' : 'غائب'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($record['attended_at']): ?>
                                            <?php echo date('H:i', strtotime($record['attended_at'])); ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- الأنشطة الأخيرة -->
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    الأنشطة الأخيرة
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($activities)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد أنشطة</h6>
                    </div>
                <?php else: ?>
                    <div class="timeline">
                        <?php foreach ($activities as $activity): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1"><?php echo htmlspecialchars($activity['activity_type']); ?></h6>
                                <p class="mb-1"><?php echo htmlspecialchars($activity['description']); ?></p>
                                <small class="text-muted"><?php echo date('Y-m-d H:i', strtotime($activity['created_at'])); ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}
</style>

<?php include 'includes/footer.php'; ?>
