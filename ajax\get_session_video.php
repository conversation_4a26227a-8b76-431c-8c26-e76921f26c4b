<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'غير مسموح']);
    exit;
}

// التحقق من وجود معرف الجلسة
if (!isset($_GET['session_id']) || empty($_GET['session_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الجلسة مطلوب']);
    exit;
}

$session_id = (int)$_GET['session_id'];

try {
    // جلب معلومات الفيديو
    if ($_SESSION['role'] === 'instructor') {
        // للمدرب - التحقق من أن الجلسة تنتمي له
        $stmt = $conn->prepare("
            SELECT s.video_file_path, s.video_title, s.video_description, s.has_video
            FROM sessions s
            INNER JOIN courses c ON s.course_id = c.id
            WHERE s.id = ? AND c.instructor_id = ? AND s.has_video = 1
        ");
        $stmt->execute([$session_id, $_SESSION['user_id']]);
    } else {
        // للطلاب - التحقق من التسجيل في الكورس
        $stmt = $conn->prepare("
            SELECT s.video_file_path, s.video_title, s.video_description, s.has_video
            FROM sessions s
            INNER JOIN courses c ON s.course_id = c.id
            INNER JOIN course_enrollments ce ON c.id = ce.course_id
            WHERE s.id = ? AND ce.student_id = ? AND ce.status = 'active' AND s.has_video = 1
        ");
        $stmt->execute([$session_id, $_SESSION['user_id']]);
    }
    
    $video = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($video && $video['video_file_path']) {
        echo json_encode([
            'success' => true,
            'video_url' => '../view_session_video.php?session_id=' . $session_id,
            'video_title' => $video['video_title'],
            'video_description' => $video['video_description']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'الفيديو غير موجود أو لا تملك صلاحية للوصول إليه'
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في قاعدة البيانات'
    ]);
}
?>
