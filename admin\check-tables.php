<?php
session_start();

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

echo "<h1>فحص جداول قاعدة البيانات</h1>";

try {
    require_once '../includes/database_manager_clean.php';
    
    echo "<div style='color: green;'>✅ تم تحميل database_manager_clean.php بنجاح</div><br>";
    
    // قائمة الجداول المطلوبة
    $requiredTables = [
        'users',
        'courses', 
        'sessions',
        'course_enrollments',
        'payments',
        'activity_logs',
        'assignments',
        'quizzes',
        'meals',
        'meal_categories',
        'file_uploads'
    ];
    
    // فحص الجداول
    $existingTables = $dbClean->query("SHOW TABLES");
    $tableNames = [];
    foreach ($existingTables as $table) {
        $tableNames[] = array_values($table)[0];
    }
    
    echo "<h2>الجداول الموجودة:</h2>";
    echo "<ul>";
    foreach ($tableNames as $table) {
        $count = $dbClean->count($table);
        echo "<li style='color: green;'>✅ $table ($count سجل)</li>";
    }
    echo "</ul>";
    
    echo "<h2>الجداول المطلوبة:</h2>";
    echo "<ul>";
    foreach ($requiredTables as $table) {
        if (in_array($table, $tableNames)) {
            $count = $dbClean->count($table);
            echo "<li style='color: green;'>✅ $table موجود ($count سجل)</li>";
        } else {
            echo "<li style='color: red;'>❌ $table غير موجود</li>";
        }
    }
    echo "</ul>";
    
    // فحص أعمدة جدول المدفوعات
    if (in_array('payments', $tableNames)) {
        echo "<h2>أعمدة جدول المدفوعات:</h2>";
        $columns = $dbClean->query("DESCRIBE payments");
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
    }
    
    // فحص أعمدة جدول التسجيلات
    if (in_array('course_enrollments', $tableNames)) {
        echo "<h2>أعمدة جدول التسجيلات:</h2>";
        $columns = $dbClean->query("DESCRIBE course_enrollments");
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
    }
    
    // فحص أعمدة جدول الكورسات
    if (in_array('courses', $tableNames)) {
        echo "<h2>أعمدة جدول الكورسات:</h2>";
        $columns = $dbClean->query("DESCRIBE courses");
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
    }
    
    echo "<div style='color: green; font-weight: bold; margin-top: 20px;'>🎉 فحص قاعدة البيانات مكتمل!</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div style='color: red;'>📍 التفاصيل: " . $e->getTraceAsString() . "</div>";
}

echo "<br><a href='analytics.php'>العودة للتحليلات</a> | <a href='dashboard.php'>لوحة التحكم</a>";
?>
