<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'فيديوهات الجلسات';

// جلب الكورسات المسجل فيها الطالب مع فيديوهات الجلسات
try {
    $stmt = $conn->prepare("
        SELECT 
            c.id as course_id,
            c.title as course_title,
            s.id as session_id,
            s.title as session_title,
            s.description as session_description,
            s.start_time,
            s.duration,
            s.status,
            s.has_video,
            s.video_title,
            s.video_description,
            s.video_uploaded_at
        FROM course_enrollments ce
        INNER JOIN courses c ON ce.course_id = c.id
        LEFT JOIN sessions s ON c.id = s.course_id AND s.has_video = 1
        WHERE ce.student_id = ? AND ce.status = 'active'
        ORDER BY c.title, s.start_time DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $sessions_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنظيم البيانات حسب الكورس
    $courses_sessions = [];
    foreach ($sessions_data as $row) {
        if ($row['session_id']) {
            $courses_sessions[$row['course_id']]['course_title'] = $row['course_title'];
            $courses_sessions[$row['course_id']]['sessions'][] = $row;
        }
    }
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات';
    $courses_sessions = [];
}

include '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="h3 mb-2">
                        <i class="fas fa-video text-primary me-2"></i>
                        فيديوهات الجلسات
                    </h2>
                    <p class="text-muted mb-0">مشاهدة فيديوهات الجلسات المسجلة للكورسات المسجل فيها</p>
                </div>
            </div>

            <!-- رسائل الخطأ -->
            <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <!-- قائمة الكورسات والجلسات -->
            <?php if (empty($courses_sessions)): ?>
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-video text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">لا توجد فيديوهات جلسات</h5>
                    <p class="text-muted">لم يتم رفع فيديوهات للجلسات في الكورسات المسجل فيها بعد</p>
                    <a href="courses.php" class="btn btn-primary">
                        <i class="fas fa-graduation-cap me-1"></i>تصفح الكورسات
                    </a>
                </div>
            </div>
            <?php else: ?>
            <?php foreach ($courses_sessions as $course_id => $course_data): ?>
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        <?php echo htmlspecialchars($course_data['course_title']); ?>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php foreach ($course_data['sessions'] as $session): ?>
                    <div class="session-item border-bottom p-3">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="mb-1">
                                    <i class="fas fa-video text-primary me-2"></i>
                                    <?php echo htmlspecialchars($session['video_title'] ?: $session['session_title']); ?>
                                </h6>
                                <p class="text-muted mb-1 small">
                                    <?php echo htmlspecialchars($session['video_description'] ?: $session['session_description']); ?>
                                </p>
                                <div class="d-flex gap-3 small text-muted">
                                    <span><i class="fas fa-calendar me-1"></i><?php echo date('Y-m-d', strtotime($session['start_time'])); ?></span>
                                    <span><i class="fas fa-clock me-1"></i><?php echo $session['duration']; ?> دقيقة</span>
                                    <span><i class="fas fa-upload me-1"></i>رُفع في: <?php echo date('Y-m-d', strtotime($session['video_uploaded_at'])); ?></span>
                                </div>
                            </div>
                            
                            <div class="col-md-4 text-end">
                                <button class="btn btn-primary" 
                                        onclick="watchVideo(<?php echo $session['session_id']; ?>, '<?php echo htmlspecialchars($session['video_title'] ?: $session['session_title']); ?>')">
                                    <i class="fas fa-play me-1"></i>مشاهدة الفيديو
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal مشاهدة الفيديو -->
<div class="modal fade" id="watchVideoModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="videoModalTitle">مشاهدة الفيديو</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="videoPlayerContainer" class="text-center">
                    <!-- سيتم إدراج الفيديو هنا -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// مشاهدة الفيديو
function watchVideo(sessionId, videoTitle) {
    const container = document.getElementById('videoPlayerContainer');
    const modalTitle = document.getElementById('videoModalTitle');
    
    modalTitle.textContent = videoTitle;
    container.innerHTML = '<div class="spinner-border" role="status"><span class="visually-hidden">جاري التحميل...</span></div>';
    
    // جلب معلومات الفيديو
    fetch(`../ajax/get_session_video.php?session_id=${sessionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.video_url) {
                container.innerHTML = `
                    <video width="100%" height="500" controls autoplay>
                        <source src="${data.video_url}" type="video/mp4">
                        المتصفح لا يدعم تشغيل الفيديو
                    </video>
                    <div class="mt-3 text-start">
                        <h6>${data.video_title}</h6>
                        <p class="text-muted">${data.video_description || ''}</p>
                    </div>
                `;
            } else {
                container.innerHTML = '<div class="alert alert-danger">فشل في تحميل الفيديو</div>';
            }
        })
        .catch(error => {
            container.innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء تحميل الفيديو</div>';
        });
    
    new bootstrap.Modal(document.getElementById('watchVideoModal')).show();
}
</script>

<?php include '../includes/footer.php'; ?>
