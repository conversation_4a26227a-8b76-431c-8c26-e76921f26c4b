<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة المدربين';
$pageSubtitle = 'إدارة وتتبع جميع المدربين في المنصة';
require_once 'includes/admin-header.php';

// معالجة إضافة مدرب جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_instructor'])) {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $username = trim($_POST['username']);
    $password = $_POST['password'];

    // التحقق من المدخلات
    if (empty($name) || empty($email) || empty($username) || empty($password)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صالح';
    } elseif (strlen($password) < 6) {
        $error = 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';
    } elseif (strlen($username) < 3) {
        $error = 'يجب أن يكون اسم المستخدم 3 أحرف على الأقل';
    } else {
        try {
            // التحقق من عدم وجود البريد الإلكتروني أو اسم المستخدم مسبقاً
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
            $stmt->execute([$email, $username]);
            if ($stmt->rowCount() > 0) {
                $error = 'البريد الإلكتروني أو اسم المستخدم مستخدم بالفعل';
            } else {
                // إنشاء حساب المدرب
                $stmt = $conn->prepare("
                    INSERT INTO users (name, email, username, password, role)
                    VALUES (?, ?, ?, ?, 'instructor')
                ");
                $stmt->execute([$name, $email, $username, password_hash($password, PASSWORD_DEFAULT)]);
                $success = 'تم إضافة المدرب بنجاح';
                
                // تسجيل النشاط
                logUserActivity($_SESSION['user_id'], 'إضافة مدرب', "تم إضافة مدرب جديد: $name");
                
                // إرسال بريد إلكتروني للمدرب
                try {
                    $to = $email;
                    $subject = 'تم إنشاء حسابك كمدرب';
                    $message = "
                        مرحباً $name,\n\n
                        تم إنشاء حسابك كمدرب في نظام التعلم عن بعد.\n
                        بيانات تسجيل الدخول:\n
                        اسم المستخدم: $username\n
                        كلمة المرور: $password\n\n
                        يرجى تغيير كلمة المرور بعد تسجيل الدخول لأول مرة.\n\n
                        مع أطيب التحيات,\n
                        نظام التعلم عن بعد
                    ";
                    $headers = 'From: <EMAIL>' . "\r\n" .
                        'Reply-To: <EMAIL>' . "\r\n" .
                        'X-Mailer: PHP/' . phpversion();

                    mail($to, $subject, $message, $headers);
                } catch (Exception $e) {
                    // لا نريد إيقاف العملية إذا فشل إرسال البريد
                    error_log("Failed to send email to instructor: " . $e->getMessage());
                }
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة المدرب';
        }
    }
}

// تعريف المتغيرات الافتراضية
$instructors = [];
$error = null;
$success = null;

// جلب إحصائيات المدربين
$stats = [
    'total_instructors' => 0,
    'active_instructors' => 0,
    'total_courses' => 0,
    'total_students' => 0,
    'total_revenue' => 0
];

try {
    $stats['total_instructors'] = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'instructor'")->fetchColumn();
    $stats['active_instructors'] = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'instructor' AND status = 'active'")->fetchColumn();
    $stats['total_courses'] = $conn->query("SELECT COUNT(*) FROM courses")->fetchColumn();
    $stats['total_students'] = $conn->query("SELECT COUNT(DISTINCT student_id) FROM course_enrollments")->fetchColumn();
    $stats['total_revenue'] = $conn->query("SELECT COALESCE(SUM(payment_amount), 0) FROM course_enrollments WHERE payment_status = 'completed'")->fetchColumn();
} catch (Exception $e) {
    error_log("Stats error: " . $e->getMessage());
}

// جلب قائمة المدربين مع إحصائياتهم
try {
    $stmt = $conn->prepare("
        SELECT u.*,
        COALESCE((SELECT COUNT(*) FROM courses WHERE instructor_id = u.id), 0) as course_count,
        COALESCE((SELECT COUNT(DISTINCT e.student_id)
         FROM courses c
         LEFT JOIN course_enrollments e ON c.id = e.course_id
         WHERE c.instructor_id = u.id), 0) as student_count,
        COALESCE((SELECT SUM(e.payment_amount)
         FROM courses c
         LEFT JOIN course_enrollments e ON c.id = e.course_id
         WHERE c.instructor_id = u.id AND e.payment_status = 'completed'), 0) as total_earnings,
        COALESCE((SELECT AVG(rating) FROM instructor_reviews WHERE instructor_id = u.id), 0) as avg_rating
        FROM users u
        WHERE u.role = 'instructor'
        ORDER BY u.created_at DESC
    ");
    $stmt->execute();
    $instructors = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    error_log("Database error in manage-instructors.php: " . $e->getMessage());
    $error = 'حدث خطأ أثناء جلب قائمة المدربين';
    $instructors = [];
}
?>

<!-- إحصائيات المدربين -->
<div class="row mb-4 fade-in-up">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">إجمالي المدربين</h6>
                        <h3 class="mb-0 text-primary"><?php echo number_format($stats['total_instructors']); ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-primary bg-soft text-primary">
                            <i class="fas fa-chalkboard-teacher fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">المدربين النشطين</h6>
                        <h3 class="mb-0 text-success"><?php echo number_format($stats['active_instructors']); ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-success bg-soft text-success">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">إجمالي الكورسات</h6>
                        <h3 class="mb-0 text-info"><?php echo number_format($stats['total_courses']); ?></h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-info bg-soft text-info">
                            <i class="fas fa-book fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">إجمالي الإيرادات</h6>
                        <h3 class="mb-0 text-warning"><?php echo number_format($stats['total_revenue'], 2); ?> ريال</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-sm rounded-circle bg-warning bg-soft text-warning">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة المدربين -->
<div class="admin-card fade-in-up">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h5 class="mb-0">قائمة المدربين</h5>
            <small class="text-muted">إدارة وتتبع جميع المدربين في المنصة</small>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" onclick="exportInstructors()">
                <i class="fas fa-download me-2"></i>تصدير
            </button>
            <a href="add-instructor.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مدرب جديد
            </a>
        </div>
    </div>

    <div class="card-body">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- فلاتر البحث -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث في المدربين...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="specializationFilter">
                    <option value="">جميع التخصصات</option>
                    <option value="البرمجة وتطوير المواقع">البرمجة وتطوير المواقع</option>
                    <option value="التصميم الجرافيكي">التصميم الجرافيكي</option>
                    <option value="إدارة الأعمال">إدارة الأعمال</option>
                    <option value="التسويق الرقمي">التسويق الرقمي</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                </button>
            </div>
        </div>

        <?php if (empty($instructors)): ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-chalkboard-teacher fa-4x text-muted"></i>
                </div>
                <h5 class="text-muted">لا يوجد مدربين مسجلين حالياً</h5>
                <p class="text-muted">ابدأ بإضافة مدربين جدد للمنصة</p>
                <a href="add-instructor.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة مدرب جديد
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover" id="instructorsTable">
                    <thead class="table-light">
                        <tr>
                            <th>المدرب</th>
                            <th>التخصص</th>
                            <th>الكورسات</th>
                            <th>الطلاب</th>
                            <th>الإيرادات</th>
                            <th>التقييم</th>
                            <th>الحالة</th>
                            <th>آخر نشاط</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($instructors as $instructor): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm rounded-circle bg-primary text-white me-3 d-flex align-items-center justify-content-center">
                                            <?php echo strtoupper(substr($instructor['name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($instructor['name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($instructor['email']); ?></small>
                                            <?php if ($instructor['phone']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($instructor['phone']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($instructor['specialization']): ?>
                                        <span class="badge bg-info"><?php echo htmlspecialchars($instructor['specialization']); ?></span>
                                        <?php if ($instructor['experience_years']): ?>
                                            <br><small class="text-muted"><?php echo $instructor['experience_years']; ?> سنوات خبرة</small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary rounded-pill"><?php echo $instructor['course_count']; ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-success rounded-pill"><?php echo $instructor['student_count']; ?></span>
                                </td>
                                <td>
                                    <strong class="text-warning"><?php echo number_format($instructor['total_earnings'], 2); ?> ريال</strong>
                                </td>
                                <td>
                                    <?php if ($instructor['avg_rating'] > 0): ?>
                                        <div class="d-flex align-items-center">
                                            <span class="me-1"><?php echo number_format($instructor['avg_rating'], 1); ?></span>
                                            <div class="text-warning">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star<?php echo $i <= $instructor['avg_rating'] ? '' : '-o'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">لا يوجد تقييم</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($instructor['status'] === 'active'): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php
                                        echo $instructor['last_login']
                                            ? 'منذ ' . timeAgo($instructor['last_login'])
                                            : 'لم يسجل الدخول بعد';
                                        ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="instructor-details.php?id=<?php echo $instructor['id']; ?>"
                                           class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-warning"
                                                onclick="editInstructor(<?php echo $instructor['id']; ?>)"
                                                data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary"
                                                onclick="resetPassword(<?php echo $instructor['id']; ?>)"
                                                data-bs-toggle="tooltip" title="إعادة تعيين كلمة المرور">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger"
                                                onclick="deleteInstructor(<?php echo $instructor['id']; ?>, '<?php echo htmlspecialchars($instructor['name']); ?>')"
                                                data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.bg-soft {
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
}

.bg-success.bg-soft {
    background-color: rgba(var(--bs-success-rgb), 0.1) !important;
}

.bg-info.bg-soft {
    background-color: rgba(var(--bs-info-rgb), 0.1) !important;
}

.bg-warning.bg-soft {
    background-color: rgba(var(--bs-warning-rgb), 0.1) !important;
}

.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 16px;
    font-weight: 600;
}

.table th {
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 6px !important;
    margin: 0 2px;
}
</style>

<script>
$(document).ready(function() {
    // تهيئة DataTables مع فحص عدم إعادة التهيئة
    if (!$.fn.DataTable.isDataTable('#instructorsTable')) {
        const table = $('#instructorsTable').DataTable({
            order: [[7, 'desc']],
            pageLength: 25,
            responsive: true,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
            },
            columnDefs: [
                { orderable: false, targets: [8] }
            ],
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip'
        });
    } else {
        const table = $('#instructorsTable').DataTable();
    }

    // الحصول على مرجع الجدول
    const table = $('#instructorsTable').DataTable();

    // البحث المخصص
    $('#searchInput').on('keyup', function() {
        table.search(this.value).draw();
    });

    // فلتر الحالة
    $('#statusFilter').on('change', function() {
        const status = this.value;
        if (status) {
            table.column(6).search(status).draw();
        } else {
            table.column(6).search('').draw();
        }
    });

    // فلتر التخصص
    $('#specializationFilter').on('change', function() {
        const specialization = this.value;
        if (specialization) {
            table.column(1).search(specialization).draw();
        } else {
            table.column(1).search('').draw();
        }
    });
});

// إعادة تعيين الفلاتر
function resetFilters() {
    $('#searchInput').val('');
    $('#statusFilter').val('');
    $('#specializationFilter').val('');
    $('#instructorsTable').DataTable().search('').columns().search('').draw();
}

// تصدير البيانات
function exportInstructors() {
    Swal.fire({
        title: 'تصدير بيانات المدربين',
        html: `
            <div class="d-grid gap-2">
                <button class="btn btn-success" onclick="exportData('excel', 'export-instructors.php')">
                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                </button>
                <button class="btn btn-danger" onclick="exportData('pdf', 'export-instructors.php')">
                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                </button>
                <button class="btn btn-info" onclick="exportData('csv', 'export-instructors.php')">
                    <i class="fas fa-file-csv me-2"></i>تصدير CSV
                </button>
            </div>
        `,
        showConfirmButton: false,
        showCloseButton: true,
        width: 300
    });
}

// تعديل مدرب
function editInstructor(instructorId) {
    window.location.href = `edit-instructor.php?id=${instructorId}`;
}

// إعادة تعيين كلمة المرور
function resetPassword(instructorId) {
    Swal.fire({
        title: 'إعادة تعيين كلمة المرور',
        text: 'هل أنت متأكد من إعادة تعيين كلمة المرور؟ سيتم إرسال كلمة المرور الجديدة عبر البريد الإلكتروني.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، أعد التعيين',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#f39c12'
    }).then((result) => {
        if (result.isConfirmed) {
            showLoading('جاري إعادة تعيين كلمة المرور...');

            $.post('ajax/reset-instructor-password.php', {
                instructor_id: instructorId
            })
            .done(function(response) {
                hideLoading();
                const data = JSON.parse(response);
                if (data.success) {
                    Swal.fire({
                        title: 'تم بنجاح!',
                        text: 'تم إعادة تعيين كلمة المرور وإرسالها عبر البريد الإلكتروني',
                        icon: 'success'
                    });
                } else {
                    Swal.fire({
                        title: 'خطأ!',
                        text: data.message || 'حدث خطأ أثناء إعادة تعيين كلمة المرور',
                        icon: 'error'
                    });
                }
            })
            .fail(function() {
                hideLoading();
                Swal.fire({
                    title: 'خطأ!',
                    text: 'حدث خطأ في الاتصال',
                    icon: 'error'
                });
            });
        }
    });
}

// حذف مدرب
function deleteInstructor(instructorId, instructorName) {
    Swal.fire({
        title: 'حذف المدرب',
        html: `
            <p>هل أنت متأكد من حذف المدرب <strong>${instructorName}</strong>؟</p>
            <div class="alert alert-warning text-start">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> سيتم حذف جميع البيانات المرتبطة بهذا المدرب:
                <ul class="mt-2 mb-0">
                    <li>جميع الكورسات</li>
                    <li>جميع الجلسات</li>
                    <li>جميع التقييمات</li>
                </ul>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545',
        input: 'checkbox',
        inputPlaceholder: 'أؤكد أنني أريد حذف هذا المدرب نهائياً'
    }).then((result) => {
        if (result.isConfirmed && result.value) {
            showLoading('جاري حذف المدرب...');

            $.post('ajax/delete-instructor.php', {
                instructor_id: instructorId
            })
            .done(function(response) {
                hideLoading();
                const data = JSON.parse(response);
                if (data.success) {
                    Swal.fire({
                        title: 'تم الحذف!',
                        text: 'تم حذف المدرب بنجاح',
                        icon: 'success'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'خطأ!',
                        text: data.message || 'حدث خطأ أثناء حذف المدرب',
                        icon: 'error'
                    });
                }
            })
            .fail(function() {
                hideLoading();
                Swal.fire({
                    title: 'خطأ!',
                    text: 'حدث خطأ في الاتصال',
                    icon: 'error'
                });
            });
        } else if (result.isConfirmed && !result.value) {
            Swal.fire({
                title: 'مطلوب تأكيد',
                text: 'يجب تأكيد الحذف بوضع علامة في المربع',
                icon: 'info'
            });
        }
    });
}

// دالة مساعدة لحساب الوقت المنقضي
function timeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'الآن';
    if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' دقيقة';
    if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' ساعة';
    if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' يوم';
    if (diffInSeconds < 31536000) return Math.floor(diffInSeconds / 2592000) + ' شهر';
    return Math.floor(diffInSeconds / 31536000) + ' سنة';
}

<?php
// دالة PHP لحساب الوقت المنقضي
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    return floor($time/31536000) . ' سنة';
}
?>
</script>

<?php require_once 'includes/admin-footer.php'; ?>
