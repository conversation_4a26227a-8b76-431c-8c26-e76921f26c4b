<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$success = '';
$error = '';

// التحقق من معرف الجلسة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: sessions.php');
    exit;
}

$session_id = (int)$_GET['id'];

// معالجة تحديث الجلسة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $title = isset($_POST['title']) ? trim($_POST['title']) : '';
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        $start_time = isset($_POST['start_time']) ? $_POST['start_time'] : '';
        $duration = isset($_POST['duration']) ? (int)$_POST['duration'] : 60;
        $zoom_link = isset($_POST['zoom_link']) ? trim($_POST['zoom_link']) : '';
        $zoom_meeting_id = isset($_POST['zoom_meeting_id']) ? trim($_POST['zoom_meeting_id']) : '';
        $zoom_password = isset($_POST['zoom_password']) ? trim($_POST['zoom_password']) : '';

        // التحقق من البيانات المطلوبة
        if (empty($title) || empty($start_time)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }

        // التحقق من أن الجلسة تخص المدرب الحالي
        $stmt = $conn->prepare("
            SELECT s.id 
            FROM sessions s 
            JOIN courses c ON s.course_id = c.id 
            WHERE s.id = ? AND c.instructor_id = ?
        ");
        $stmt->execute([$session_id, $_SESSION['user_id']]);
        
        if ($stmt->rowCount() === 0) {
            throw new Exception('لا يمكنك تعديل هذه الجلسة');
        }

        // تحديث بيانات الجلسة
        $stmt = $conn->prepare("
            UPDATE sessions 
            SET title = ?, description = ?, start_time = ?, duration = ?, 
                zoom_meeting_id = ?, zoom_meeting_password = ?, zoom_join_url = ?
            WHERE id = ?
        ");
        $stmt->execute([
            $title, $description, $start_time, $duration, 
            $zoom_meeting_id, $zoom_password, $zoom_link, $session_id
        ]);

        $success = 'تم تحديث الجلسة بنجاح';

    } catch (Exception $e) {
        $error = $e->getMessage();
        error_log($e->getMessage());
    }
}

// جلب تفاصيل الجلسة
try {
    $stmt = $conn->prepare("
        SELECT s.*, c.title as course_title, c.id as course_id
        FROM sessions s
        JOIN courses c ON s.course_id = c.id
        WHERE s.id = ? AND c.instructor_id = ?
    ");
    $stmt->execute([$session_id, $_SESSION['user_id']]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        header('Location: sessions.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب تفاصيل الجلسة';
    error_log($e->getMessage());
}

$pageTitle = 'تعديل الجلسة';
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo $success; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo $error; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الجلسة: <?php echo htmlspecialchars($session['title']); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- معلومات أساسية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-info-circle me-1"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <label for="title" class="form-label fw-bold">
                                    عنوان الجلسة <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="<?php echo htmlspecialchars($session['title']); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="course_title" class="form-label fw-bold">الكورس</label>
                                <input type="text" class="form-control" id="course_title" 
                                       value="<?php echo htmlspecialchars($session['course_title']); ?>" readonly>
                            </div>
                        </div>

                        <!-- التوقيت والمدة -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-clock me-1"></i>
                                    التوقيت والمدة
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <label for="start_time" class="form-label fw-bold">
                                    وقت البدء <span class="text-danger">*</span>
                                </label>
                                <input type="datetime-local" class="form-control" id="start_time" name="start_time" 
                                       value="<?php echo date('Y-m-d\TH:i', strtotime($session['start_time'])); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="duration" class="form-label fw-bold">المدة (بالدقائق)</label>
                                <input type="number" class="form-control" id="duration" name="duration" 
                                       value="<?php echo $session['duration']; ?>" min="15" max="480" required>
                            </div>
                        </div>

                        <!-- وصف الجلسة -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="description" class="form-label fw-bold">وصف الجلسة</label>
                                <textarea class="form-control" id="description" name="description" rows="3"
                                          placeholder="وصف مختصر عن محتوى الجلسة..."><?php echo htmlspecialchars($session['description'] ?? ''); ?></textarea>
                            </div>
                        </div>

                        <!-- معلومات Zoom -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-video me-1"></i>
                                    معلومات البث المباشر (Zoom)
                                </h6>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>نصيحة:</strong> يمكنك إضافة رابط Zoom أو أي منصة بث أخرى هنا ليتمكن الطلاب من الانضمام للجلسة.
                                </div>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="zoom_link" class="form-label fw-bold">
                                    <i class="fas fa-link me-1"></i>
                                    رابط البث المباشر
                                </label>
                                <input type="url" class="form-control" id="zoom_link" name="zoom_link" 
                                       value="<?php echo htmlspecialchars($session['zoom_join_url'] ?? ''); ?>"
                                       placeholder="https://zoom.us/j/1234567890 أو أي رابط بث آخر">
                                <div class="form-text">
                                    <i class="fas fa-lightbulb text-warning me-1"></i>
                                    يمكنك نسخ رابط الاجتماع من Zoom أو Google Meet أو أي منصة أخرى
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="zoom_meeting_id" class="form-label fw-bold">
                                    <i class="fas fa-hashtag me-1"></i>
                                    معرف الاجتماع
                                </label>
                                <input type="text" class="form-control" id="zoom_meeting_id" name="zoom_meeting_id" 
                                       value="<?php echo htmlspecialchars($session['zoom_meeting_id'] ?? ''); ?>"
                                       placeholder="123-456-7890">
                            </div>
                            <div class="col-md-6">
                                <label for="zoom_password" class="form-label fw-bold">
                                    <i class="fas fa-key me-1"></i>
                                    كلمة مرور الاجتماع
                                </label>
                                <input type="text" class="form-control" id="zoom_password" name="zoom_password" 
                                       value="<?php echo htmlspecialchars($session['zoom_meeting_password'] ?? ''); ?>"
                                       placeholder="كلمة المرور (اختيارية)">
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ التغييرات
                                    </button>
                                    <a href="session-details.php?id=<?php echo $session['id']; ?>" class="btn btn-outline-secondary btn-lg">
                                        <i class="fas fa-eye me-2"></i>
                                        عرض التفاصيل
                                    </a>
                                    <a href="sessions.php" class="btn btn-outline-secondary btn-lg">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        العودة للقائمة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معلومات مساعدة -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-1"></i>
                        كيفية الحصول على رابط Zoom
                    </h6>
                </div>
                <div class="card-body">
                    <div class="step-guide">
                        <div class="step mb-3">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <strong>سجل دخول لـ Zoom</strong>
                                <p class="small text-muted">ادخل على موقع zoom.us وسجل دخولك</p>
                            </div>
                        </div>
                        <div class="step mb-3">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <strong>أنشئ اجتماع جديد</strong>
                                <p class="small text-muted">اضغط على "Schedule a Meeting"</p>
                            </div>
                        </div>
                        <div class="step mb-3">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <strong>انسخ الرابط</strong>
                                <p class="small text-muted">انسخ رابط الدعوة والصقه في الحقل أعلاه</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معاينة الرابط -->
            <div class="card shadow-sm mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-eye me-1"></i>
                        معاينة الرابط
                    </h6>
                </div>
                <div class="card-body">
                    <div id="link-preview">
                        <p class="text-muted">أدخل رابط البث لمعاينته</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.step-guide .step {
    display: flex;
    align-items: flex-start;
}

.step-number {
    width: 30px;
    height: 30px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #004085;
}
</style>

<script>
// معاينة الرابط
document.getElementById('zoom_link').addEventListener('input', function() {
    const link = this.value.trim();
    const preview = document.getElementById('link-preview');
    
    if (link) {
        if (link.includes('zoom.us')) {
            preview.innerHTML = `
                <div class="text-success">
                    <i class="fas fa-check-circle me-1"></i>
                    رابط Zoom صحيح
                </div>
                <small class="text-muted d-block mt-1">
                    ${link}
                </small>
            `;
        } else if (link.includes('meet.google.com')) {
            preview.innerHTML = `
                <div class="text-success">
                    <i class="fas fa-check-circle me-1"></i>
                    رابط Google Meet صحيح
                </div>
                <small class="text-muted d-block mt-1">
                    ${link}
                </small>
            `;
        } else if (link.startsWith('http')) {
            preview.innerHTML = `
                <div class="text-info">
                    <i class="fas fa-info-circle me-1"></i>
                    رابط صحيح
                </div>
                <small class="text-muted d-block mt-1">
                    ${link}
                </small>
            `;
        } else {
            preview.innerHTML = `
                <div class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    تأكد من صحة الرابط
                </div>
            `;
        }
    } else {
        preview.innerHTML = '<p class="text-muted">أدخل رابط البث لمعاينته</p>';
    }
});

// تحديث معاينة الرابط عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('zoom_link').dispatchEvent(new Event('input'));
});
</script>

<?php include '../includes/footer.php'; ?>
