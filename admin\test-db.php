<?php
session_start();

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

echo "<h1>اختبار قاعدة البيانات</h1>";

try {
    require_once '../includes/database_manager_clean.php';
    
    echo "<div style='color: green;'>✅ تم تحميل database_manager_clean.php بنجاح</div><br>";
    
    // اختبار الاتصال
    $testConnection = new DatabaseManagerClean();
    echo "<div style='color: green;'>✅ تم إنشاء اتصال قاعدة البيانات بنجاح</div><br>";
    
    // اختبار العد
    $userCount = $testConnection->count('users');
    echo "<div style='color: blue;'>📊 عدد المستخدمين: " . $userCount . "</div><br>";
    
    $courseCount = $testConnection->count('courses');
    echo "<div style='color: blue;'>📊 عدد الكورسات: " . $courseCount . "</div><br>";
    
    // اختبار جلب البيانات
    $users = $testConnection->getAll('users', '*', 'id DESC', 5);
    echo "<div style='color: blue;'>📊 آخر 5 مستخدمين:</div>";
    echo "<pre>" . print_r($users, true) . "</pre><br>";
    
    // اختبار البحث
    if ($userCount > 0) {
        $searchResults = $testConnection->search('users', 'admin', ['name', 'email']);
        echo "<div style='color: blue;'>🔍 نتائج البحث عن 'admin':</div>";
        echo "<pre>" . print_r($searchResults, true) . "</pre><br>";
    }
    
    echo "<div style='color: green; font-weight: bold;'>🎉 جميع الاختبارات نجحت!</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div style='color: red;'>📍 التفاصيل: " . $e->getTraceAsString() . "</div>";
}

echo "<br><a href='dashboard.php'>العودة للوحة التحكم</a>";
?>
