<?php
// إعداد قاعدة البيانات الشامل والمحسن
// =====================================

require_once 'database.php';
require_once 'config.php';

class DatabaseSetup {
    private $conn;
    private $errors = [];
    private $success_messages = [];
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    // إنشاء جميع الجداول المطلوبة
    public function createAllTables() {
        $this->createSystemSettingsTable();
        $this->createCategoriesTable();
        $this->createCoursesEnhancedTable();
        $this->createCourseChaptersTable();
        $this->createCourseVideosTable();
        $this->createVideoWatchesTable();
        $this->createCourseReviewsTable();
        $this->createPaymentsTable();
        $this->createCertificatesTable();
        $this->createForumsTable();
        $this->createMessagesTable();
        $this->createAnnouncementsTable();
        $this->createSystemLogsTable();
        $this->createBackupsTable();
        
        return ['errors' => $this->errors, 'success' => $this->success_messages];
    }
    
    // جدول إعدادات النظام
    private function createSystemSettingsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS system_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                description TEXT,
                is_public BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_key (setting_key),
                INDEX idx_public (is_public)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول إعدادات النظام بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول إعدادات النظام: " . $e->getMessage();
        }
    }
    
    // جدول التصنيفات المحسن
    private function createCategoriesTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                name_en VARCHAR(255) NULL,
                description TEXT,
                parent_id INT NULL,
                icon VARCHAR(100) NULL,
                color VARCHAR(7) DEFAULT '#667eea',
                sort_order INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                seo_title VARCHAR(255) NULL,
                seo_description TEXT NULL,
                seo_keywords TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
                INDEX idx_parent (parent_id),
                INDEX idx_active (is_active),
                INDEX idx_sort (sort_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول التصنيفات المحسن بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول التصنيفات: " . $e->getMessage();
        }
    }
    
    // تحسين جدول الكورسات
    private function createCoursesEnhancedTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS courses_enhanced (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                title_en VARCHAR(255) NULL,
                slug VARCHAR(255) NOT NULL UNIQUE,
                description TEXT,
                short_description VARCHAR(500),
                instructor_id INT NOT NULL,
                category_id INT NULL,
                subcategory_id INT NULL,
                level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
                language VARCHAR(10) DEFAULT 'ar',
                duration_hours DECIMAL(5,2) DEFAULT 0,
                price DECIMAL(10,2) DEFAULT 0,
                discount_price DECIMAL(10,2) NULL,
                course_type ENUM('free', 'paid', 'subscription') DEFAULT 'free',
                max_students INT DEFAULT 0,
                thumbnail VARCHAR(500) NULL,
                preview_video VARCHAR(500) NULL,
                requirements TEXT,
                what_you_learn TEXT,
                target_audience TEXT,
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                featured BOOLEAN DEFAULT FALSE,
                rating DECIMAL(3,2) DEFAULT 0,
                total_reviews INT DEFAULT 0,
                total_students INT DEFAULT 0,
                total_lessons INT DEFAULT 0,
                total_quizzes INT DEFAULT 0,
                certificate_enabled BOOLEAN DEFAULT TRUE,
                forum_enabled BOOLEAN DEFAULT TRUE,
                qa_enabled BOOLEAN DEFAULT TRUE,
                downloadable_resources BOOLEAN DEFAULT TRUE,
                lifetime_access BOOLEAN DEFAULT TRUE,
                mobile_access BOOLEAN DEFAULT TRUE,
                seo_title VARCHAR(255) NULL,
                seo_description TEXT NULL,
                seo_keywords TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                published_at TIMESTAMP NULL,
                FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
                FOREIGN KEY (subcategory_id) REFERENCES categories(id) ON DELETE SET NULL,
                INDEX idx_instructor (instructor_id),
                INDEX idx_category (category_id),
                INDEX idx_status (status),
                INDEX idx_featured (featured),
                INDEX idx_price (price),
                INDEX idx_rating (rating),
                INDEX idx_slug (slug),
                FULLTEXT idx_search (title, description, short_description)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول الكورسات المحسن بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول الكورسات المحسن: " . $e->getMessage();
        }
    }
    
    // جدول فصول الكورسات
    private function createCourseChaptersTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS course_chapters (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                sort_order INT DEFAULT 1,
                is_free BOOLEAN DEFAULT FALSE,
                duration_minutes INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                INDEX idx_course (course_id),
                INDEX idx_order (sort_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول فصول الكورسات بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول فصول الكورسات: " . $e->getMessage();
        }
    }
    
    // جدول فيديوهات الكورسات المحسن
    private function createCourseVideosTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS course_videos (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                chapter_id INT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                video_type ENUM('upload', 'youtube', 'vimeo', 'external') DEFAULT 'upload',
                video_url VARCHAR(1000) NULL,
                video_path VARCHAR(500) NULL,
                thumbnail_path VARCHAR(500) NULL,
                duration_seconds INT DEFAULT 0,
                file_size BIGINT DEFAULT 0,
                video_quality VARCHAR(10) DEFAULT '720p',
                is_free BOOLEAN DEFAULT FALSE,
                sort_order INT DEFAULT 1,
                view_count INT DEFAULT 0,
                download_enabled BOOLEAN DEFAULT TRUE,
                transcript TEXT NULL,
                captions_file VARCHAR(500) NULL,
                status ENUM('processing', 'ready', 'error') DEFAULT 'ready',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (chapter_id) REFERENCES course_chapters(id) ON DELETE SET NULL,
                INDEX idx_course (course_id),
                INDEX idx_chapter (chapter_id),
                INDEX idx_order (sort_order),
                INDEX idx_status (status),
                INDEX idx_free (is_free)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول فيديوهات الكورسات المحسن بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول فيديوهات الكورسات: " . $e->getMessage();
        }
    }
    
    // جدول مشاهدة الفيديوهات
    private function createVideoWatchesTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS video_watches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                video_id INT NOT NULL,
                user_id INT NOT NULL,
                watch_percentage DECIMAL(5,2) DEFAULT 0,
                watch_time_seconds INT DEFAULT 0,
                total_watch_time INT DEFAULT 0,
                completed BOOLEAN DEFAULT FALSE,
                last_position INT DEFAULT 0,
                device_type VARCHAR(50) NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (video_id) REFERENCES course_videos(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_watch (video_id, user_id),
                INDEX idx_video (video_id),
                INDEX idx_user (user_id),
                INDEX idx_completed (completed)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول مشاهدة الفيديوهات بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول مشاهدة الفيديوهات: " . $e->getMessage();
        }
    }
    
    // جدول تقييمات الكورسات
    private function createCourseReviewsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS course_reviews (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                user_id INT NOT NULL,
                rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                review_title VARCHAR(255) NULL,
                review_text TEXT NULL,
                is_approved BOOLEAN DEFAULT FALSE,
                is_featured BOOLEAN DEFAULT FALSE,
                helpful_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_review (course_id, user_id),
                INDEX idx_course (course_id),
                INDEX idx_user (user_id),
                INDEX idx_rating (rating),
                INDEX idx_approved (is_approved)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول تقييمات الكورسات بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول تقييمات الكورسات: " . $e->getMessage();
        }
    }
    
    // جدول المدفوعات المحسن
    private function createPaymentsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                payment_method ENUM('stripe', 'paypal', 'bank_transfer', 'wallet') NOT NULL,
                transaction_id VARCHAR(255) NOT NULL UNIQUE,
                payment_intent_id VARCHAR(255) NULL,
                amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'USD',
                platform_fee DECIMAL(10,2) DEFAULT 0,
                instructor_amount DECIMAL(10,2) DEFAULT 0,
                status ENUM('pending', 'completed', 'failed', 'refunded', 'cancelled') DEFAULT 'pending',
                payment_date TIMESTAMP NULL,
                refund_date TIMESTAMP NULL,
                refund_reason TEXT NULL,
                gateway_response JSON NULL,
                notes TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                INDEX idx_user (user_id),
                INDEX idx_course (course_id),
                INDEX idx_status (status),
                INDEX idx_transaction (transaction_id),
                INDEX idx_payment_date (payment_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول المدفوعات المحسن بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول المدفوعات: " . $e->getMessage();
        }
    }
    
    // جدول الشهادات
    private function createCertificatesTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS certificates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                certificate_number VARCHAR(50) NOT NULL UNIQUE,
                certificate_hash VARCHAR(64) NOT NULL UNIQUE,
                issue_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completion_date TIMESTAMP NULL,
                grade DECIMAL(5,2) NULL,
                certificate_path VARCHAR(500) NULL,
                is_verified BOOLEAN DEFAULT TRUE,
                verification_url VARCHAR(500) NULL,
                template_used VARCHAR(100) DEFAULT 'default',
                custom_fields JSON NULL,
                download_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                UNIQUE KEY unique_certificate (user_id, course_id),
                INDEX idx_user (user_id),
                INDEX idx_course (course_id),
                INDEX idx_number (certificate_number),
                INDEX idx_hash (certificate_hash)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول الشهادات بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول الشهادات: " . $e->getMessage();
        }
    }
    
    // إضافة المزيد من الجداول...
    private function createForumsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS forums (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول المنتديات بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول المنتديات: " . $e->getMessage();
        }
    }
    
    private function createMessagesTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                sender_id INT NOT NULL,
                receiver_id INT NOT NULL,
                subject VARCHAR(255) NULL,
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول الرسائل بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول الرسائل: " . $e->getMessage();
        }
    }
    
    private function createAnnouncementsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS announcements (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                type ENUM('general', 'course', 'maintenance') DEFAULT 'general',
                target_audience ENUM('all', 'students', 'instructors') DEFAULT 'all',
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول الإعلانات بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول الإعلانات: " . $e->getMessage();
        }
    }
    
    private function createSystemLogsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS system_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
                message TEXT NOT NULL,
                context JSON NULL,
                user_id INT NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_level (level),
                INDEX idx_created (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول سجلات النظام بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول سجلات النظام: " . $e->getMessage();
        }
    }
    
    private function createBackupsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS backups (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size BIGINT DEFAULT 0,
                backup_type ENUM('full', 'incremental', 'differential') DEFAULT 'full',
                status ENUM('in_progress', 'completed', 'failed') DEFAULT 'in_progress',
                created_by INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP NULL,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            $this->success_messages[] = "تم إنشاء جدول النسخ الاحتياطية بنجاح";
        } catch (PDOException $e) {
            $this->errors[] = "خطأ في إنشاء جدول النسخ الاحتياطية: " . $e->getMessage();
        }
    }
}

// تشغيل إعداد قاعدة البيانات إذا تم استدعاء الملف مباشرة
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $setup = new DatabaseSetup($conn);
    $result = $setup->createAllTables();
    
    echo "<h2>نتائج إعداد قاعدة البيانات</h2>";
    
    if (!empty($result['success'])) {
        echo "<h3 style='color: green;'>العمليات الناجحة:</h3><ul>";
        foreach ($result['success'] as $message) {
            echo "<li>$message</li>";
        }
        echo "</ul>";
    }
    
    if (!empty($result['errors'])) {
        echo "<h3 style='color: red;'>الأخطاء:</h3><ul>";
        foreach ($result['errors'] as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul>";
    }
}
?>
