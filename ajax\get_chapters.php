<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// التحقق من معرف الكورس
if (!isset($_GET['course_id']) || !is_numeric($_GET['course_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الكورس مطلوب']);
    exit;
}

$course_id = (int)$_GET['course_id'];

try {
    // التحقق من أن الكورس ينتمي للمدرب
    $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $_SESSION['user_id']]);
    
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه']);
        exit;
    }
    
    // التحقق من وجود عمود order_number
    $stmt = $conn->query("DESCRIBE course_chapters");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $has_order = in_array('order_number', $columns);

    // جلب الفصول
    if ($has_order) {
        $stmt = $conn->prepare("
            SELECT id, title, order_number
            FROM course_chapters
            WHERE course_id = ?
            ORDER BY order_number ASC
        ");
    } else {
        $stmt = $conn->prepare("
            SELECT id, title
            FROM course_chapters
            WHERE course_id = ?
            ORDER BY id ASC
        ");
    }
    $stmt->execute([$course_id]);
    $chapters = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'chapters' => $chapters
    ]);
    
} catch (PDOException $e) {
    error_log("Get chapters error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء جلب الفصول'
    ]);
}
?>
