<?php
session_start();
require_once '../includes/database_manager_clean.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'التحليلات والإحصائيات';
$pageSubtitle = 'تحليل شامل لأداء المنصة والمستخدمين';

// تهيئة المتغيرات
$general_stats = [];
$growth_stats = [];
$top_courses = [];
$top_instructors = [];
$attendance_stats = [];
$error_message = '';

// جلب الإحصائيات
try {
    // إحصائيات عامة
    $general_stats = [
        'total_users' => $dbClean->count('users'),
        'total_students' => $dbClean->count('users', ['role' => 'student']),
        'total_instructors' => $dbClean->count('users', ['role' => 'instructor']),
        'total_courses' => $dbClean->count('courses'),
        'active_courses' => $dbClean->count('courses', ['status' => 'published']),
        'total_sessions' => $dbClean->count('sessions'),
        'completed_sessions' => $dbClean->count('sessions', ['status' => 'completed']),
        'total_enrollments' => $dbClean->count('course_enrollments'),
        'total_revenue' => 0,
        'monthly_revenue' => 0
    ];

    // حساب الإيرادات من جدول المدفوعات إذا كان موجوداً
    try {
        $revenueQuery = "SELECT
            COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_revenue,
            COALESCE(SUM(CASE WHEN status = 'completed' AND DATE(payment_date) >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN amount ELSE 0 END), 0) as monthly_revenue
            FROM payments";
        $revenueData = $dbClean->query($revenueQuery);
        if (!empty($revenueData)) {
            $general_stats['total_revenue'] = $revenueData[0]['total_revenue'];
            $general_stats['monthly_revenue'] = $revenueData[0]['monthly_revenue'];
        }
    } catch (Exception $e) {
        // إذا لم يوجد جدول المدفوعات، نحسب من الكورسات المدفوعة
        $paidCoursesQuery = "SELECT
            COALESCE(SUM(c.price), 0) as total_revenue
            FROM courses c
            INNER JOIN course_enrollments ce ON c.id = ce.course_id
            WHERE c.is_free = 0";
        $paidData = $dbClean->query($paidCoursesQuery);
        if (!empty($paidData)) {
            $general_stats['total_revenue'] = $paidData[0]['total_revenue'];
        }
    }

    // إحصائيات النمو الشهري
    $growth_stats = $dbClean->query("
        SELECT
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as new_users
        FROM users
        WHERE role != 'admin' AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");

    // أفضل الكورسات
    $top_courses = $dbClean->query("
        SELECT
            c.title,
            c.price,
            c.is_free,
            COUNT(ce.id) as enrollments,
            COALESCE(c.price * COUNT(ce.id), 0) as revenue,
            0 as avg_rating
        FROM courses c
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id
        GROUP BY c.id, c.title, c.price, c.is_free
        ORDER BY enrollments DESC
        LIMIT 10
    ");

    // أفضل المدربين
    $top_instructors = $dbClean->query("
        SELECT
            u.name,
            u.email,
            COUNT(DISTINCT c.id) as courses_count,
            COUNT(DISTINCT ce.id) as total_enrollments,
            COALESCE(SUM(c.price * 0.7), 0) as total_earnings
        FROM users u
        LEFT JOIN courses c ON u.id = c.instructor_id
        LEFT JOIN course_enrollments ce ON c.id = ce.course_id
        WHERE u.role = 'instructor'
        GROUP BY u.id, u.name, u.email
        ORDER BY total_enrollments DESC
        LIMIT 10
    ");

    // إحصائيات الحضور
    $attendance_stats = $dbClean->query("
        SELECT
            s.status,
            COUNT(*) as count
        FROM sessions s
        GROUP BY s.status
    ");

} catch (Exception $e) {
    $error_message = 'حدث خطأ أثناء جلب الإحصائيات: ' . $e->getMessage();

    // قيم افتراضية في حالة الخطأ
    $general_stats = [
        'total_users' => 0,
        'total_students' => 0,
        'total_instructors' => 0,
        'total_courses' => 0,
        'active_courses' => 0,
        'total_sessions' => 0,
        'completed_sessions' => 0,
        'total_enrollments' => 0,
        'total_revenue' => 0,
        'monthly_revenue' => 0
    ];

    $growth_stats = [];
    $top_courses = [];
    $top_instructors = [];
    $attendance_stats = [];
}

include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل الخطأ -->
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary text-white mb-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($general_stats['total_users']); ?></h3>
                    <p class="text-muted mb-0">إجمالي المستخدمين</p>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>
                        <?php echo number_format($general_stats['total_students'] + $general_stats['total_instructors']); ?> نشط
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success text-white mb-3">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($general_stats['total_courses']); ?></h3>
                    <p class="text-muted mb-0">إجمالي الكورسات</p>
                    <small class="text-info">
                        <i class="fas fa-check-circle me-1"></i>
                        <?php echo number_format($general_stats['active_courses']); ?> نشط
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-warning text-white mb-3">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($general_stats['total_enrollments']); ?></h3>
                    <p class="text-muted mb-0">إجمالي التسجيلات</p>
                    <small class="text-success">
                        <i class="fas fa-chart-line me-1"></i>
                        نمو مستمر
                    </small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="admin-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-info text-white mb-3">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <h3 class="mb-1"><?php echo number_format($general_stats['total_revenue'], 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">إجمالي الإيرادات</p>
                    <small class="text-success">
                        <i class="fas fa-calendar me-1"></i>
                        <?php echo number_format($general_stats['monthly_revenue'], 2); ?> ر.س هذا الشهر
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- أفضل الكورسات -->
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-trophy me-2"></i>أفضل الكورسات</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($top_courses)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد كورسات بعد</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الكورس</th>
                                        <th>التسجيلات</th>
                                        <th>الإيرادات</th>
                                        <th>التقييم</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($top_courses as $course): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars(substr($course['title'], 0, 30)); ?></strong>
                                                    <br><small class="text-muted">
                                                        <?php echo $course['is_free'] ? 'مجاني' : number_format($course['price'], 2) . ' ر.س'; ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $course['enrollments']; ?></span>
                                            </td>
                                            <td>
                                                <strong><?php echo number_format($course['revenue'], 2); ?> ر.س</strong>
                                            </td>
                                            <td>
                                                <?php if ($course['avg_rating'] > 0): ?>
                                                    <div class="d-flex align-items-center">
                                                        <span class="me-1"><?php echo number_format($course['avg_rating'], 1); ?></span>
                                                        <div class="text-warning">
                                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                <i class="fas fa-star<?php echo $i <= $course['avg_rating'] ? '' : '-o'; ?> fa-sm"></i>
                                                            <?php endfor; ?>
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">لا يوجد</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- أفضل المدربين -->
        <div class="col-lg-6 mb-4">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chalkboard-teacher me-2"></i>أفضل المدربين</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($top_instructors)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مدربين بعد</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المدرب</th>
                                        <th>الكورسات</th>
                                        <th>الطلاب</th>
                                        <th>الأرباح</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($top_instructors as $instructor): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($instructor['name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($instructor['email']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $instructor['courses_count']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success"><?php echo $instructor['total_enrollments']; ?></span>
                                            </td>
                                            <td>
                                                <strong><?php echo number_format($instructor['total_earnings'], 2); ?> ر.س</strong>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الجلسات -->
    <div class="row">
        <div class="col-lg-12">
            <div class="admin-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-video me-2"></i>إحصائيات الجلسات</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <h4 class="text-primary"><?php echo number_format($general_stats['total_sessions']); ?></h4>
                            <p class="text-muted mb-0">إجمالي الجلسات</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success"><?php echo number_format($general_stats['completed_sessions']); ?></h4>
                            <p class="text-muted mb-0">جلسات مكتملة</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <?php 
                            $completion_rate = $general_stats['total_sessions'] > 0 ? 
                                ($general_stats['completed_sessions'] / $general_stats['total_sessions']) * 100 : 0;
                            ?>
                            <h4 class="text-info"><?php echo number_format($completion_rate, 1); ?>%</h4>
                            <p class="text-muted mb-0">معدل الإكمال</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <?php
                            $scheduled_sessions = 0;
                            if (is_array($attendance_stats)) {
                                foreach ($attendance_stats as $stat) {
                                    if ($stat['status'] === 'scheduled') {
                                        $scheduled_sessions = $stat['count'];
                                        break;
                                    }
                                }
                            }
                            ?>
                            <h4 class="text-warning"><?php echo number_format($scheduled_sessions); ?></h4>
                            <p class="text-muted mb-0">جلسات مجدولة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 24px;
}

.admin-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border: none;
    transition: all 0.3s ease;
}

.admin-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.admin-card .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: white;
    border: none;
    padding: 15px 20px;
    font-weight: 600;
}

.table th {
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}
</style>

<?php include 'includes/footer.php'; ?>
