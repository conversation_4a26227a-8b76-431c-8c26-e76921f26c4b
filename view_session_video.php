<?php
require_once 'includes/session_config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('HTTP/1.0 403 Forbidden');
    exit('غير مسموح');
}

// التحقق من وجود معرف الجلسة
if (!isset($_GET['session_id']) || empty($_GET['session_id'])) {
    header('HTTP/1.0 400 Bad Request');
    exit('معرف الجلسة مطلوب');
}

$session_id = (int)$_GET['session_id'];

try {
    // جلب معلومات الفيديو مع التحقق من الصلاحيات
    if ($_SESSION['role'] === 'instructor') {
        // للمدرب - التحقق من أن الجلسة تنتمي له
        $stmt = $conn->prepare("
            SELECT s.video_file_path, s.video_title, s.has_video
            FROM sessions s
            INNER JOIN courses c ON s.course_id = c.id
            WHERE s.id = ? AND c.instructor_id = ? AND s.has_video = 1
        ");
        $stmt->execute([$session_id, $_SESSION['user_id']]);
    } elseif ($_SESSION['role'] === 'student') {
        // للطلاب - التحقق من التسجيل في الكورس
        $stmt = $conn->prepare("
            SELECT s.video_file_path, s.video_title, s.has_video
            FROM sessions s
            INNER JOIN courses c ON s.course_id = c.id
            INNER JOIN course_enrollments ce ON c.id = ce.course_id
            WHERE s.id = ? AND ce.student_id = ? AND ce.status = 'active' AND s.has_video = 1
        ");
        $stmt->execute([$session_id, $_SESSION['user_id']]);
    } elseif ($_SESSION['role'] === 'admin') {
        // للأدمن - الوصول لجميع الفيديوهات
        $stmt = $conn->prepare("
            SELECT s.video_file_path, s.video_title, s.has_video
            FROM sessions s
            WHERE s.id = ? AND s.has_video = 1
        ");
        $stmt->execute([$session_id]);
    } else {
        header('HTTP/1.0 403 Forbidden');
        exit('غير مسموح');
    }
    
    $video = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$video || !$video['video_file_path']) {
        header('HTTP/1.0 404 Not Found');
        exit('الفيديو غير موجود');
    }
    
    $file_path = $video['video_file_path'];
    $full_path = __DIR__ . '/' . $file_path;
    
    // التحقق من وجود الملف
    if (!file_exists($full_path)) {
        header('HTTP/1.0 404 Not Found');
        exit('ملف الفيديو غير موجود');
    }
    
    // تحديد نوع المحتوى
    $file_info = pathinfo($full_path);
    $extension = strtolower($file_info['extension']);
    
    $content_types = [
        'mp4' => 'video/mp4',
        'avi' => 'video/x-msvideo',
        'mov' => 'video/quicktime',
        'wmv' => 'video/x-ms-wmv',
        'flv' => 'video/x-flv',
        'webm' => 'video/webm'
    ];
    
    $content_type = $content_types[$extension] ?? 'application/octet-stream';
    
    // إعداد الهيدرز
    header('Content-Type: ' . $content_type);
    header('Content-Length: ' . filesize($full_path));
    header('Content-Disposition: inline; filename="' . basename($full_path) . '"');
    header('Accept-Ranges: bytes');
    
    // دعم التشغيل المتدرج (Range Requests)
    if (isset($_SERVER['HTTP_RANGE'])) {
        $range = $_SERVER['HTTP_RANGE'];
        $file_size = filesize($full_path);
        
        // تحليل Range header
        if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
            $start = intval($matches[1]);
            $end = $matches[2] ? intval($matches[2]) : $file_size - 1;
            
            if ($start < $file_size && $end < $file_size && $start <= $end) {
                header('HTTP/1.1 206 Partial Content');
                header("Content-Range: bytes $start-$end/$file_size");
                header('Content-Length: ' . ($end - $start + 1));
                
                $file = fopen($full_path, 'rb');
                fseek($file, $start);
                
                $chunk_size = 8192;
                $bytes_left = $end - $start + 1;
                
                while ($bytes_left > 0 && !feof($file)) {
                    $bytes_to_read = min($chunk_size, $bytes_left);
                    echo fread($file, $bytes_to_read);
                    $bytes_left -= $bytes_to_read;
                    
                    if (ob_get_level()) {
                        ob_flush();
                    }
                    flush();
                }
                
                fclose($file);
                exit;
            }
        }
    }
    
    // إرسال الملف كاملاً
    readfile($full_path);
    
} catch (PDOException $e) {
    header('HTTP/1.0 500 Internal Server Error');
    exit('خطأ في قاعدة البيانات');
} catch (Exception $e) {
    header('HTTP/1.0 500 Internal Server Error');
    exit('حدث خطأ غير متوقع');
}
?>
