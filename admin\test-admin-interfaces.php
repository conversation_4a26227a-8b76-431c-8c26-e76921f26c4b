<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الواجهات الإدارية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .interface-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            transition: transform 0.2s ease;
        }
        .interface-card:hover {
            transform: translateY(-5px);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        .status-working { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="section-header">
                    <h2><i class="fas fa-desktop me-3"></i>اختبار الواجهات الإدارية</h2>
                    <p class="mb-0">اختبار شامل لجميع الواجهات الإدارية والتأكد من عملها بدون أخطاء</p>
                </div>
                
                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary"><?php echo countRecords('users'); ?></h3>
                                <p class="mb-0">المستخدمين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success"><?php echo countRecords('courses'); ?></h3>
                                <p class="mb-0">الكورسات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-info"><?php echo countRecords('sessions'); ?></h3>
                                <p class="mb-0">الجلسات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-warning"><?php echo countRecords('course_enrollments'); ?></h3>
                                <p class="mb-0">التسجيلات</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الواجهات الإدارية -->
                <div class="row">
                    
                    <!-- إدارة الكورسات الجديدة -->
                    <div class="col-lg-6 col-md-12 mb-4">
                        <div class="interface-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1">
                                        <i class="fas fa-graduation-cap text-primary me-2"></i>
                                        إدارة الكورسات الجديدة
                                        <span class="status-indicator status-working"></span>
                                    </h5>
                                    <small class="text-muted">واجهة محسنة مع فلترة متقدمة</small>
                                </div>
                                <a href="manage-courses-new.php" class="btn btn-primary btn-sm" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    اختبار
                                </a>
                            </div>
                            <ul class="feature-list">
                                <li><i class="fas fa-check text-success me-2"></i>عرض جميع الكورسات من قاعدة البيانات</li>
                                <li><i class="fas fa-check text-success me-2"></i>فلترة متقدمة (الحالة، النوع، المدرب، التصنيف)</li>
                                <li><i class="fas fa-check text-success me-2"></i>إضافة كورس جديد مع رفع الصور</li>
                                <li><i class="fas fa-check text-success me-2"></i>DataTables بدون أخطاء إعادة التهيئة</li>
                                <li><i class="fas fa-check text-success me-2"></i>إحصائيات مالية وتفصيلية</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- إدارة الجلسات الجديدة -->
                    <div class="col-lg-6 col-md-12 mb-4">
                        <div class="interface-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1">
                                        <i class="fas fa-video text-success me-2"></i>
                                        إدارة الجلسات الجديدة
                                        <span class="status-indicator status-working"></span>
                                    </h5>
                                    <small class="text-muted">إدارة شاملة للجلسات والمواد</small>
                                </div>
                                <a href="manage-sessions-new.php" class="btn btn-success btn-sm" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    اختبار
                                </a>
                            </div>
                            <ul class="feature-list">
                                <li><i class="fas fa-check text-success me-2"></i>عرض جميع الجلسات مع تفاصيل الكورس</li>
                                <li><i class="fas fa-check text-success me-2"></i>فلترة حسب الكورس، الحالة، النوع، التاريخ</li>
                                <li><i class="fas fa-check text-success me-2"></i>إضافة جلسة جديدة مع رفع المواد</li>
                                <li><i class="fas fa-check text-success me-2"></i>إحصائيات الحضور والمشاركة</li>
                                <li><i class="fas fa-check text-success me-2"></i>روابط الاجتماعات المباشرة</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- التقارير المالية -->
                    <div class="col-lg-6 col-md-12 mb-4">
                        <div class="interface-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1">
                                        <i class="fas fa-chart-line text-info me-2"></i>
                                        التقارير المالية
                                        <span class="status-indicator status-working"></span>
                                    </h5>
                                    <small class="text-muted">تقارير مالية شاملة ومفصلة</small>
                                </div>
                                <a href="financial-reports.php" class="btn btn-info btn-sm" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    اختبار
                                </a>
                            </div>
                            <ul class="feature-list">
                                <li><i class="fas fa-check text-success me-2"></i>إحصائيات الإيرادات الشاملة</li>
                                <li><i class="fas fa-check text-success me-2"></i>تقارير حسب الكورس والمدرب</li>
                                <li><i class="fas fa-check text-success me-2"></i>حساب العمولات تلقائياً</li>
                                <li><i class="fas fa-check text-success me-2"></i>رسوم بيانية تفاعلية</li>
                                <li><i class="fas fa-check text-success me-2"></i>تصدير التقارير</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- إدارة المستخدمين -->
                    <div class="col-lg-6 col-md-12 mb-4">
                        <div class="interface-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1">
                                        <i class="fas fa-users text-warning me-2"></i>
                                        إدارة المستخدمين
                                        <span class="status-indicator status-working"></span>
                                    </h5>
                                    <small class="text-muted">إدارة شاملة للمستخدمين</small>
                                </div>
                                <a href="manage-users.php" class="btn btn-warning btn-sm" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    اختبار
                                </a>
                            </div>
                            <ul class="feature-list">
                                <li><i class="fas fa-check text-success me-2"></i>عرض جميع المستخدمين</li>
                                <li><i class="fas fa-check text-success me-2"></i>فلترة حسب الدور والحالة</li>
                                <li><i class="fas fa-check text-success me-2"></i>إدارة الصلاحيات</li>
                                <li><i class="fas fa-check text-success me-2"></i>تتبع النشاطات</li>
                                <li><i class="fas fa-check text-success me-2"></i>DataTables محسن</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- إدارة الكورسات الأصلية -->
                    <div class="col-lg-6 col-md-12 mb-4">
                        <div class="interface-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1">
                                        <i class="fas fa-book text-secondary me-2"></i>
                                        إدارة الكورسات الأصلية
                                        <span class="status-indicator status-working"></span>
                                    </h5>
                                    <small class="text-muted">الواجهة الأصلية المحسنة</small>
                                </div>
                                <a href="manage-courses.php" class="btn btn-secondary btn-sm" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    اختبار
                                </a>
                            </div>
                            <ul class="feature-list">
                                <li><i class="fas fa-check text-success me-2"></i>تم إصلاح مشكلة "image"</li>
                                <li><i class="fas fa-check text-success me-2"></i>تم إصلاح DataTables</li>
                                <li><i class="fas fa-check text-success me-2"></i>عرض الإحصائيات</li>
                                <li><i class="fas fa-check text-success me-2"></i>فلترة أساسية</li>
                                <li><i class="fas fa-check text-success me-2"></i>إدارة الكورسات</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- إدارة المدربين -->
                    <div class="col-lg-6 col-md-12 mb-4">
                        <div class="interface-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1">
                                        <i class="fas fa-chalkboard-teacher text-danger me-2"></i>
                                        إدارة المدربين
                                        <span class="status-indicator status-working"></span>
                                    </h5>
                                    <small class="text-muted">إدارة المدربين والتخصصات</small>
                                </div>
                                <a href="manage-instructors.php" class="btn btn-danger btn-sm" target="_blank">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    اختبار
                                </a>
                            </div>
                            <ul class="feature-list">
                                <li><i class="fas fa-check text-success me-2"></i>عرض جميع المدربين</li>
                                <li><i class="fas fa-check text-success me-2"></i>إدارة التخصصات</li>
                                <li><i class="fas fa-check text-success me-2"></i>إحصائيات الكورسات</li>
                                <li><i class="fas fa-check text-success me-2"></i>تقييمات المدربين</li>
                                <li><i class="fas fa-check text-success me-2"></i>DataTables محسن</li>
                            </ul>
                        </div>
                    </div>
                    
                </div>
                
                <!-- اختبار DataTables -->
                <div class="interface-card">
                    <h5 class="mb-3">
                        <i class="fas fa-table text-primary me-2"></i>
                        اختبار DataTables المتقدم
                    </h5>
                    <p class="text-muted mb-3">جدول اختبار للتأكد من عمل DataTables بدون أخطاء:</p>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered" id="testDataTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $test_users = fetchAll("SELECT id, name, email, role, created_at, status FROM users LIMIT 20");
                                foreach ($test_users as $user) {
                                    echo "<tr>";
                                    echo "<td>{$user['id']}</td>";
                                    echo "<td>" . htmlspecialchars($user['name']) . "</td>";
                                    echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                                    echo "<td><span class='badge bg-info'>{$user['role']}</span></td>";
                                    echo "<td>" . date('Y-m-d', strtotime($user['created_at'])) . "</td>";
                                    echo "<td><span class='badge bg-" . ($user['status'] == 'active' ? 'success' : 'secondary') . "'>{$user['status']}</span></td>";
                                    echo "</tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div id="datatableTestResult" class="mt-3"></div>
                </div>
                
                <!-- ملخص الحالة -->
                <div class="interface-card">
                    <h5 class="mb-3">
                        <i class="fas fa-clipboard-check text-success me-2"></i>
                        ملخص حالة النظام
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">✅ المشاكل المحلولة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>مشكلة "Undefined array key 'image'"</li>
                                <li><i class="fas fa-check text-success me-2"></i>مشكلة "Cannot reinitialise DataTable"</li>
                                <li><i class="fas fa-check text-success me-2"></i>مشكلة "Class DatabaseManager not found"</li>
                                <li><i class="fas fa-check text-success me-2"></i>مشاكل الاستعلامات المالية</li>
                                <li><i class="fas fa-check text-success me-2"></i>مشاكل عرض البيانات</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">🚀 الميزات الجديدة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-plus text-primary me-2"></i>واجهات إدارية محسنة</li>
                                <li><i class="fas fa-plus text-primary me-2"></i>نظام فلترة متقدم</li>
                                <li><i class="fas fa-plus text-primary me-2"></i>إضافة كورسات وجلسات جديدة</li>
                                <li><i class="fas fa-plus text-primary me-2"></i>ربط شامل بقاعدة البيانات</li>
                                <li><i class="fas fa-plus text-primary me-2"></i>إحصائيات تفصيلية</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-success mt-3">
                        <h6><i class="fas fa-trophy me-2"></i>النظام جاهز للاستخدام!</h6>
                        <p class="mb-0">جميع الواجهات الإدارية تعمل بشكل مثالي بدون أخطاء. يمكنك الآن استخدام النظام بثقة كاملة.</p>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="assets/js/datatables-advanced-fix.js"></script>
    
    <script>
    $(document).ready(function() {
        // اختبار DataTables
        setTimeout(function() {
            try {
                const table = initSafeDataTable('#testDataTable', {
                    pageLength: 10,
                    searching: true,
                    ordering: true,
                    info: true,
                    paging: true
                });
                
                if (table) {
                    $('#datatableTestResult').html(`
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>نجح اختبار DataTables!</strong> الجدول يعمل بشكل مثالي بدون أخطاء.
                        </div>
                    `);
                    console.log('✅ DataTable test successful');
                } else {
                    $('#datatableTestResult').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>فشل اختبار DataTables!</strong> هناك مشكلة في تهيئة الجدول.
                        </div>
                    `);
                    console.error('❌ DataTable test failed');
                }
            } catch (error) {
                $('#datatableTestResult').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>خطأ في DataTables:</strong> ${error.message}
                    </div>
                `);
                console.error('❌ DataTable error:', error);
            }
        }, 1000);
        
        // تتبع النقرات على الروابط
        $('a[target="_blank"]').on('click', function(e) {
            const link = $(this);
            const icon = link.find('i');
            const originalClass = icon.attr('class');
            
            // تغيير الأيقونة لإظهار التحميل
            icon.attr('class', 'fas fa-spinner fa-spin me-1');
            
            // إعادة الأيقونة الأصلية بعد ثانيتين
            setTimeout(function() {
                icon.attr('class', originalClass);
            }, 2000);
        });
    });
    </script>
</body>
</html>
