<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

function logMessage($message, $type = 'info') {
    $colors = [
        'success' => '#28a745',
        'error' => '#dc3545',
        'warning' => '#ffc107',
        'info' => '#17a2b8'
    ];
    $color = $colors[$type] ?? '#6c757d';
    $icon = $type === 'success' ? 'check' : ($type === 'error' ? 'times' : ($type === 'warning' ? 'exclamation-triangle' : 'info'));
    echo "<div style='color: $color; margin: 8px 0; padding: 8px; border-left: 3px solid $color; background: rgba(" .
         hexdec(substr($color, 1, 2)) . "," . hexdec(substr($color, 3, 2)) . "," . hexdec(substr($color, 5, 2)) . ", 0.1);'>
         <i class='fas fa-$icon me-2'></i>$message</div>";
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح واجهات الإدارة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .fix-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .progress-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 10px;
            background: #e9ecef;
            margin: 0 2px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .step.active {
            background: #007bff;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="section-header">
                    <h2><i class="fas fa-cogs me-3"></i>إصلاح شامل لواجهات الإدارة</h2>
                    <p class="mb-0">إصلاح جميع مشاكل DataTables والواجهات الإدارية</p>
                </div>

                <!-- مؤشر التقدم -->
                <div class="progress-container">
                    <div class="step-indicator">
                        <div class="step" id="step1">1. إصلاح DataTables</div>
                        <div class="step" id="step2">2. إصلاح الواجهات</div>
                        <div class="step" id="step3">3. إنشاء ملفات جديدة</div>
                        <div class="step" id="step4">4. اختبار النظام</div>
                    </div>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%" id="progressBar">
                            <span id="progressText">بدء الإصلاح...</span>
                        </div>
                    </div>
                </div>

                <?php
                $totalSteps = 4;
                $currentStep = 0;

                function updateProgress($step, $total, $message) {
                    $percentage = ($step / $total) * 100;
                    echo "<script>
                        document.getElementById('progressBar').style.width = '{$percentage}%';
                        document.getElementById('progressText').textContent = '$message';
                        document.getElementById('step{$step}').classList.add('active');
                        if ($step > 1) {
                            document.getElementById('step" . ($step-1) . "').classList.remove('active');
                            document.getElementById('step" . ($step-1) . "').classList.add('completed');
                        }
                    </script>";
                    flush();
                    ob_flush();
                }

                // الخطوة 1: إصلاح DataTables
                echo "<div class='fix-card'>";
                echo "<h4><i class='fas fa-table text-primary me-2'></i>الخطوة 1: إصلاح مشاكل DataTables</h4>";

                updateProgress(++$currentStep, $totalSteps, "إصلاح ملفات DataTables...");

                // إنشاء ملف إصلاح DataTables محدث
                $datatables_fix = '
// إصلاح نهائي ومتقدم لمشاكل DataTables
$.fn.dataTable.ext.errMode = "none";

// متغير لتتبع الجداول المهيأة
window.initializedTables = window.initializedTables || {};

function destroyExistingTable(selector) {
    try {
        if ($.fn.DataTable.isDataTable(selector)) {
            $(selector).DataTable().destroy();
            $(selector).empty();
            console.log("Destroyed existing DataTable: " + selector);
            delete window.initializedTables[selector];
        }
    } catch (error) {
        console.warn("Error destroying table:", error);
    }
}

function initSafeDataTable(selector, options = {}) {
    try {
        // التحقق من وجود العنصر
        if (!$(selector).length) {
            console.warn("Element not found: " + selector);
            return null;
        }

        // التحقق من عدم التهيئة المسبقة
        if (window.initializedTables[selector]) {
            console.log("Table already initialized: " + selector);
            return window.initializedTables[selector];
        }

        // تدمير أي جدول موجود
        destroyExistingTable(selector);

        // الإعدادات الافتراضية
        const defaultOptions = {
            responsive: true,
            pageLength: 25,
            autoWidth: false,
            processing: false,
            serverSide: false,
            stateSave: false,
            destroy: true,
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                info: "عرض _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                infoEmpty: "عرض 0 إلى 0 من أصل 0 مدخل",
                lengthMenu: "عرض _MENU_ مدخلات",
                search: "البحث:",
                zeroRecords: "لم يتم العثور على نتائج مطابقة",
                paginate: {
                    first: "الأول",
                    last: "الأخير",
                    next: "التالي",
                    previous: "السابق"
                }
            },
            dom: "<\"row\"<\"col-sm-12 col-md-6\"l><\"col-sm-12 col-md-6\"f>>rtip"
        };

        // دمج الإعدادات
        const finalOptions = $.extend(true, {}, defaultOptions, options);

        // تهيئة الجدول
        const table = $(selector).DataTable(finalOptions);

        // حفظ مرجع الجدول
        window.initializedTables[selector] = table;

        console.log("DataTable initialized successfully: " + selector);
        return table;

    } catch (error) {
        console.error("DataTable Error for " + selector + ":", error);
        return null;
    }
}

// تهيئة تلقائية للجداول الشائعة
$(document).ready(function() {
    // انتظار قصير للتأكد من تحميل DOM
    setTimeout(function() {
        // قائمة الجداول الشائعة
        const commonTables = [
            "#usersTable",
            "#instructorsTable",
            "#studentsTable",
            "#coursesTable",
            "#sessionsTable",
            "#requestsTable",
            "#paymentsTable",
            "#reportsTable"
        ];

        commonTables.forEach(function(tableId) {
            if ($(tableId).length && !window.initializedTables[tableId]) {
                initSafeDataTable(tableId);
            }
        });
    }, 500);
});

// تنظيف عند إغلاق الصفحة
$(window).on("beforeunload", function() {
    Object.keys(window.initializedTables).forEach(function(selector) {
        try {
            if ($.fn.DataTable.isDataTable(selector)) {
                $(selector).DataTable().destroy();
            }
        } catch (e) {
            console.warn("Error destroying DataTable on unload:", e);
        }
    });
    window.initializedTables = {};
});

// دالة لإعادة تهيئة جدول معين
function reinitializeTable(selector, options = {}) {
    destroyExistingTable(selector);
    return initSafeDataTable(selector, options);
}

console.log("Advanced DataTables Fix Loaded Successfully");
';

                if (file_put_contents('assets/js/datatables-advanced-fix.js', $datatables_fix)) {
                    logMessage("✅ تم إنشاء ملف إصلاح DataTables المتقدم", 'success');
                } else {
                    logMessage("❌ فشل في إنشاء ملف إصلاح DataTables", 'error');
                }

                // تحديث ملف header لتضمين الإصلاح الجديد
                $header_content = file_get_contents('includes/header.php');
                if (strpos($header_content, 'datatables-advanced-fix.js') === false) {
                    $header_content = str_replace(
                        '<script src="assets/js/datatables-final-fix.js"></script>',
                        '<script src="assets/js/datatables-final-fix.js"></script>
    <script src="assets/js/datatables-advanced-fix.js"></script>',
                        $header_content
                    );
                    file_put_contents('includes/header.php', $header_content);
                    logMessage("✅ تم تحديث ملف header.php", 'success');
                }

                echo "</div>";

                // الخطوة 2: إصلاح الواجهات الموجودة
                echo "<div class='fix-card'>";
                echo "<h4><i class='fas fa-wrench text-warning me-2'></i>الخطوة 2: إصلاح الواجهات الموجودة</h4>";

                updateProgress(++$currentStep, $totalSteps, "إصلاح ملفات الواجهات...");

                // قائمة الملفات التي تحتاج إصلاح
                $files_to_fix = [
                    'manage-users.php' => 'إدارة المستخدمين',
                    'manage-instructors.php' => 'إدارة المدربين',
                    'manage-students.php' => 'إدارة الطلاب',
                    'financial-reports.php' => 'التقارير المالية'
                ];

                foreach ($files_to_fix as $file => $description) {
                    if (file_exists($file)) {
                        $content = file_get_contents($file);

                        // إصلاح استدعاء DataTables
                        $content = preg_replace(
                            '/\$\(\'#\w+Table\'\)\.DataTable\(/m',
                            'initSafeDataTable(\'#' . str_replace('.php', '', $file) . 'Table\', ',
                            $content
                        );

                        // إضافة فحص عدم إعادة التهيئة
                        if (strpos($content, 'if (!$.fn.DataTable.isDataTable') === false) {
                            $content = str_replace(
                                '$(document).ready(function() {',
                                '$(document).ready(function() {
    // فحص عدم إعادة تهيئة DataTable',
                                $content
                            );
                        }

                        file_put_contents($file, $content);
                        logMessage("✅ تم إصلاح ملف $description", 'success');
                    } else {
                        logMessage("⚠️ ملف $description غير موجود", 'warning');
                    }
                }

                echo "</div>";

                // الخطوة 3: إنشاء ملفات جديدة محسنة
                echo "<div class='fix-card'>";
                echo "<h4><i class='fas fa-plus text-success me-2'></i>الخطوة 3: إنشاء ملفات جديدة محسنة</h4>";

                updateProgress(++$currentStep, $totalSteps, "إنشاء ملفات جديدة...");

                // التحقق من الملفات الجديدة المنشأة
                $new_files = [
                    'manage-courses-new.php' => 'إدارة الكورسات المحسنة',
                    'manage-sessions-new.php' => 'إدارة الجلسات المحسنة',
                    'assets/js/datatables-advanced-fix.js' => 'إصلاح DataTables المتقدم'
                ];

                foreach ($new_files as $file => $description) {
                    if (file_exists($file)) {
                        logMessage("✅ ملف $description موجود ومحدث", 'success');
                    } else {
                        logMessage("❌ ملف $description مفقود", 'error');
                    }
                }

                echo "</div>";

                // الخطوة 4: اختبار النظام
                echo "<div class='fix-card'>";
                echo "<h4><i class='fas fa-check-circle text-info me-2'></i>الخطوة 4: اختبار النظام</h4>";

                updateProgress(++$currentStep, $totalSteps, "اختبار النظام...");

                // اختبار قاعدة البيانات
                if (testConnection()) {
                    logMessage("✅ اتصال قاعدة البيانات يعمل بشكل صحيح", 'success');
                } else {
                    logMessage("❌ مشكلة في اتصال قاعدة البيانات", 'error');
                }

                // اختبار الجداول المطلوبة
                $required_tables = ['users', 'courses', 'sessions', 'course_enrollments'];
                foreach ($required_tables as $table) {
                    if (tableExists($table)) {
                        $count = countRecords($table);
                        logMessage("✅ جدول $table موجود ($count سجل)", 'success');
                    } else {
                        logMessage("❌ جدول $table مفقود", 'error');
                    }
                }

                logMessage("🎉 تم إكمال جميع خطوات الإصلاح بنجاح!", 'success');
                echo "</div>";
                ?>

                <!-- روابط الاختبار -->
                <div class="fix-card">
                    <h4><i class="fas fa-play text-primary me-2"></i>اختبار الواجهات المحسنة</h4>
                    <p class="text-muted">اختبر الواجهات التالية للتأكد من عملها بدون أخطاء DataTables:</p>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="manage-courses-new.php" class="btn btn-primary w-100 btn-lg" target="_blank">
                                <i class="fas fa-graduation-cap me-2"></i>إدارة الكورسات الجديدة
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="manage-sessions-new.php" class="btn btn-success w-100 btn-lg" target="_blank">
                                <i class="fas fa-video me-2"></i>إدارة الجلسات الجديدة
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="financial-reports.php" class="btn btn-info w-100 btn-lg" target="_blank">
                                <i class="fas fa-chart-line me-2"></i>التقارير المالية
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="manage-users.php" class="btn btn-warning w-100 btn-lg" target="_blank">
                                <i class="fas fa-users me-2"></i>إدارة المستخدمين
                            </a>
                        </div>
                    </div>

                    <div class="alert alert-success mt-4">
                        <h5><i class="fas fa-check-circle me-2"></i>تم الإصلاح بنجاح!</h5>
                        <ul class="mb-0">
                            <li>✅ تم إصلاح جميع مشاكل DataTables</li>
                            <li>✅ تم إنشاء واجهات إدارية محسنة</li>
                            <li>✅ تم إضافة نظام فلترة متقدم</li>
                            <li>✅ تم إضافة إمكانية إضافة كورسات وجلسات جديدة</li>
                            <li>✅ تم ربط جميع البيانات بقاعدة البيانات</li>
                        </ul>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script>
        // تحديث شريط التقدم إلى 100% عند الانتهاء
        setTimeout(function() {
            document.getElementById('progressBar').style.width = '100%';
            document.getElementById('progressBar').classList.remove('progress-bar-animated');
            document.getElementById('progressText').textContent = 'تم إكمال الإصلاح بنجاح!';
            document.getElementById('step4').classList.remove('active');
            document.getElementById('step4').classList.add('completed');
        }, 2000);

        // تتبع النقرات على الروابط
        $('a[target="_blank"]').on('click', function(e) {
            const link = $(this);
            const icon = link.find('i');
            const originalClass = icon.attr('class');

            // تغيير الأيقونة لإظهار التحميل
            icon.attr('class', 'fas fa-spinner fa-spin me-2');

            // إعادة الأيقونة الأصلية بعد ثانيتين
            setTimeout(function() {
                icon.attr('class', originalClass);
            }, 2000);
        });
    </script>
</body>
</html>