<?php
// ملف إعداد قاعدة البيانات وإصلاح الأخطاء
require_once 'config/database.php';

echo "<h2>إعداد قاعدة البيانات</h2>";

try {
    // إسقاط الجداول الموجودة وإعادة إنشائها
    echo "<p>إسقاط الجداول الموجودة...</p>";
    
    $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    $tables = ['assignment_submissions', 'assignments', 'session_attendees', 'sessions', 'course_enrollments', 'join_requests', 'courses', 'activity_logs', 'notifications', 'users'];
    
    foreach ($tables as $table) {
        try {
            $conn->exec("DROP TABLE IF EXISTS $table");
            echo "تم حذف جدول $table<br>";
        } catch (PDOException $e) {
            echo "خطأ في حذف جدول $table: " . $e->getMessage() . "<br>";
        }
    }
    
    $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    echo "<p>إنشاء الجداول من جديد...</p>";
    
    // إنشاء جدول المستخدمين
    $conn->exec("CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        username VARCHAR(50) NULL,
        phone VARCHAR(20) NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'instructor', 'student') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        status ENUM('active', 'inactive') DEFAULT 'active'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "تم إنشاء جدول المستخدمين<br>";
    
    // إنشاء جدول الكورسات
    $conn->exec("CREATE TABLE courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructor_id INT,
        max_students INT DEFAULT 50,
        price DECIMAL(10,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'inactive') DEFAULT 'active',
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "تم إنشاء جدول الكورسات<br>";
    
    // إنشاء جدول الجلسات
    $conn->exec("CREATE TABLE sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        start_time DATETIME NOT NULL,
        duration INT NOT NULL,
        zoom_meeting_id VARCHAR(255),
        zoom_meeting_password VARCHAR(255),
        zoom_join_url VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('scheduled', 'completed', 'cancelled') DEFAULT 'scheduled',
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "تم إنشاء جدول الجلسات<br>";
    
    // إنشاء جدول التسجيل في الكورسات
    $conn->exec("CREATE TABLE course_enrollments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT,
        student_id INT,
        enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'completed', 'dropped') DEFAULT 'active',
        progress_percentage DECIMAL(5,2) DEFAULT 0,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "تم إنشاء جدول التسجيل في الكورسات<br>";
    
    // إنشاء جدول حضور الجلسات
    $conn->exec("CREATE TABLE session_attendees (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id INT,
        user_id INT,
        join_time TIMESTAMP NULL,
        leave_time TIMESTAMP NULL,
        FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "تم إنشاء جدول حضور الجلسات<br>";
    
    // إنشاء جدول سجل الأنشطة
    $conn->exec("CREATE TABLE activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(255) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "تم إنشاء جدول سجل الأنشطة<br>";
    
    // إنشاء جدول الإشعارات
    $conn->exec("CREATE TABLE notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'danger') DEFAULT 'info',
        category VARCHAR(50) DEFAULT 'general',
        action_url VARCHAR(500),
        is_read TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "تم إنشاء جدول الإشعارات<br>";
    
    // إنشاء جدول طلبات الانضمام
    $conn->exec("CREATE TABLE join_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        course_id INT NOT NULL,
        message TEXT,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed_at TIMESTAMP NULL,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "تم إنشاء جدول طلبات الانضمام<br>";
    
    // إنشاء جدول الواجبات
    $conn->exec("CREATE TABLE assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        instructor_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructions TEXT,
        due_date DATETIME NOT NULL,
        max_score INT DEFAULT 100,
        max_grade INT DEFAULT 100,
        is_required TINYINT(1) DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "تم إنشاء جدول الواجبات<br>";
    
    // إنشاء جدول تسليم الواجبات
    $conn->exec("CREATE TABLE assignment_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        assignment_id INT NOT NULL,
        student_id INT NOT NULL,
        submission_text TEXT,
        file_path VARCHAR(500),
        score INT NULL,
        feedback TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        graded_at TIMESTAMP NULL,
        status ENUM('submitted', 'graded', 'late') DEFAULT 'submitted',
        FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "تم إنشاء جدول تسليم الواجبات<br>";
    
    echo "<h3>إدراج البيانات التجريبية...</h3>";
    
    // إدراج المدير
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $conn->exec("INSERT INTO users (name, email, password, role) VALUES 
                ('المدير العام', '<EMAIL>', '$admin_password', 'admin')");
    echo "تم إدراج المدير<br>";
    
    // إدراج المدرب
    $instructor_password = password_hash('instructor123', PASSWORD_DEFAULT);
    $conn->exec("INSERT INTO users (name, email, password, role) VALUES 
                ('أحمد محمد', '<EMAIL>', '$instructor_password', 'instructor')");
    echo "تم إدراج المدرب<br>";
    
    // إدراج الطلاب
    $student_password = password_hash('student123', PASSWORD_DEFAULT);
    $conn->exec("INSERT INTO users (name, email, password, role) VALUES 
                ('سارة أحمد', '<EMAIL>', '$student_password', 'student'),
                ('محمد علي', '<EMAIL>', '$student_password', 'student'),
                ('فاطمة حسن', '<EMAIL>', '$student_password', 'student')");
    echo "تم إدراج الطلاب<br>";
    
    // إدراج الكورسات
    $conn->exec("INSERT INTO courses (title, description, instructor_id, price) VALUES 
                ('البرمجة بـ PHP', 'تعلم أساسيات البرمجة بلغة PHP', 2, 299.99),
                ('تطوير المواقع', 'تعلم تطوير المواقع الحديثة', 2, 399.99),
                ('قواعد البيانات', 'تعلم إدارة قواعد البيانات', 2, 199.99)");
    echo "تم إدراج الكورسات<br>";
    
    // إدراج الواجبات
    $conn->exec("INSERT INTO assignments (course_id, instructor_id, title, description, due_date, max_score) VALUES 
                (1, 2, 'واجب PHP الأول', 'إنشاء صفحة ويب بسيطة باستخدام PHP', DATE_ADD(NOW(), INTERVAL 7 DAY), 100),
                (1, 2, 'مشروع قاعدة البيانات', 'تصميم قاعدة بيانات لنظام إدارة المكتبة', DATE_ADD(NOW(), INTERVAL 14 DAY), 150),
                (2, 2, 'واجب HTML/CSS', 'تصميم موقع شخصي باستخدام HTML و CSS', DATE_ADD(NOW(), INTERVAL 3 DAY), 80)");
    echo "تم إدراج الواجبات<br>";
    
    // إدراج تسجيلات الطلاب في الكورسات
    $conn->exec("INSERT INTO course_enrollments (course_id, student_id, status) VALUES
                (1, 3, 'active'),
                (1, 4, 'active'),
                (1, 5, 'active'),
                (2, 3, 'active'),
                (2, 4, 'active'),
                (3, 5, 'active')");
    echo "تم إدراج تسجيلات الطلاب في الكورسات<br>";

    // إدراج بعض التسليمات التجريبية
    $conn->exec("INSERT INTO assignment_submissions (assignment_id, student_id, submission_text, status) VALUES
                (1, 3, 'تم إنشاء صفحة PHP بسيطة تعرض معلومات المستخدم', 'submitted'),
                (1, 4, 'صفحة PHP تحتوي على نموذج تسجيل دخول', 'submitted'),
                (2, 3, 'تصميم قاعدة بيانات للمكتبة مع 5 جداول رئيسية', 'submitted')");
    echo "تم إدراج التسليمات التجريبية<br>";
    
    echo "<h3 style='color: green;'>تم إعداد قاعدة البيانات بنجاح!</h3>";
    echo "<p><a href='instructor/assignments.php'>الذهاب لصفحة الواجبات</a></p>";
    echo "<p><a href='instructor/quick-login.php'>تسجيل دخول سريع للمدرب</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>خطأ: " . $e->getMessage() . "</h3>";
}
?>
