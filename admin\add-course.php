<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$success_message = '';
$error_message = '';

// معالجة إضافة الكورس
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $instructor_id = (int)($_POST['instructor_id'] ?? 0);
    $category_id = (int)($_POST['category_id'] ?? 0);
    $course_type = $_POST['course_type'] ?? 'free';
    $price = (float)($_POST['price'] ?? 0);
    $max_students = (int)($_POST['max_students'] ?? 0);
    $status = $_POST['status'] ?? 'active';
    
    // التحقق من صحة البيانات
    if (empty($title) || empty($description) || !$instructor_id) {
        $error_message = 'جميع الحقول المطلوبة يجب ملؤها';
    } elseif ($course_type === 'paid' && $price <= 0) {
        $error_message = 'سعر الكورس المدفوع يجب أن يكون أكبر من صفر';
    } else {
        // معالجة رفع الصورة
        $image_path = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/courses/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
            
            if (in_array($file_extension, $allowed_extensions)) {
                $new_filename = 'course_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
                $image_path = 'uploads/courses/' . $new_filename;
                
                if (!move_uploaded_file($_FILES['image']['tmp_name'], '../' . $image_path)) {
                    $error_message = 'فشل في رفع الصورة';
                    $image_path = '';
                }
            } else {
                $error_message = 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG أو GIF';
            }
        }
        
        if (empty($error_message)) {
            // إضافة الكورس
            $course_data = [
                'title' => $title,
                'description' => $description,
                'instructor_id' => $instructor_id,
                'category_id' => $category_id ?: null,
                'price' => $price,
                'max_students' => $max_students ?: null,
                'status' => $status,
                'image' => $image_path ?: null
            ];
            
            if (insertRecord('courses', $course_data)) {
                $success_message = 'تم إضافة الكورس بنجاح';
                // إعادة تعيين النموذج
                $_POST = [];
            } else {
                $error_message = 'حدث خطأ أثناء إضافة الكورس';
            }
        }
    }
}

// جلب قائمة المدربين
$instructors = fetchAll("SELECT id, name FROM users WHERE role = 'instructor' ORDER BY name");

// جلب قائمة التصنيفات
$categories = fetchAll("SELECT id, name FROM categories ORDER BY name");

$pageTitle = 'إضافة كورس جديد';
include 'includes/header.php';
?>

<!-- رسائل النجاح والخطأ -->
<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <!-- نموذج إضافة الكورس -->
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة كورس جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="addCourseForm">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="title" class="form-label">عنوان الكورس <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">وصف الكورس <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="4" required><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="instructor_id" class="form-label">المدرب <span class="text-danger">*</span></label>
                            <select class="form-select" id="instructor_id" name="instructor_id" required>
                                <option value="">اختر المدرب</option>
                                <?php foreach ($instructors as $instructor): ?>
                                    <option value="<?php echo $instructor['id']; ?>" 
                                            <?php echo (($_POST['instructor_id'] ?? '') == $instructor['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($instructor['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">التصنيف</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">اختر التصنيف</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" 
                                            <?php echo (($_POST['category_id'] ?? '') == $category['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="course_type" class="form-label">نوع الكورس</label>
                            <select class="form-select" id="course_type" name="course_type" onchange="togglePriceField()">
                                <option value="free" <?php echo (($_POST['course_type'] ?? 'free') === 'free') ? 'selected' : ''; ?>>مجاني</option>
                                <option value="paid" <?php echo (($_POST['course_type'] ?? '') === 'paid') ? 'selected' : ''; ?>>مدفوع</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3" id="priceField" style="<?php echo (($_POST['course_type'] ?? 'free') === 'free') ? 'display: none;' : ''; ?>">
                            <label for="price" class="form-label">السعر (ريال)</label>
                            <input type="number" class="form-control" id="price" name="price" 
                                   value="<?php echo htmlspecialchars($_POST['price'] ?? ''); ?>" min="0" step="0.01">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="max_students" class="form-label">الحد الأقصى للطلاب</label>
                            <input type="number" class="form-control" id="max_students" name="max_students" 
                                   value="<?php echo htmlspecialchars($_POST['max_students'] ?? ''); ?>" min="1">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">حالة الكورس</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo (($_POST['status'] ?? 'active') === 'active') ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo (($_POST['status'] ?? '') === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="image" class="form-label">صورة الكورس</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                            <small class="text-muted">اختياري - يفضل صورة بحجم 800x600 بكسل</small>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>إضافة الكورس
                        </button>
                        <a href="manage-courses-new.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- معاينة الصورة -->
        <div class="card-admin mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-image me-2"></i>
                    معاينة الصورة
                </h5>
            </div>
            <div class="card-body text-center">
                <div id="imagePreview">
                    <div class="bg-light rounded p-4">
                        <i class="fas fa-image fa-3x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد صورة</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نصائح -->
        <div class="card-admin">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اختر عنواناً واضحاً ومفهوماً للكورس
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        اكتب وصفاً شاملاً يوضح محتوى الكورس
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        حدد السعر المناسب للكورسات المدفوعة
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        ارفع صورة جذابة تمثل محتوى الكورس
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// تبديل حقل السعر حسب نوع الكورس
function togglePriceField() {
    const courseType = document.getElementById('course_type').value;
    const priceField = document.getElementById('priceField');
    const priceInput = document.getElementById('price');
    
    if (courseType === 'paid') {
        priceField.style.display = 'block';
        priceInput.required = true;
    } else {
        priceField.style.display = 'none';
        priceInput.required = false;
        priceInput.value = '0';
    }
}

// معاينة الصورة قبل الرفع
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('imagePreview').innerHTML = 
                `<img src="${e.target.result}" alt="معاينة الصورة" class="img-fluid rounded" style="max-height: 200px;">`;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// التحقق من صحة النموذج
document.getElementById('addCourseForm').addEventListener('submit', function(e) {
    const courseType = document.getElementById('course_type').value;
    const price = parseFloat(document.getElementById('price').value);
    
    if (courseType === 'paid' && price <= 0) {
        e.preventDefault();
        alert('يرجى إدخال سعر صحيح للكورس المدفوع');
        return false;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
