<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

function logMessage($message, $type = 'info') {
    $colors = [
        'success' => '#28a745',
        'error' => '#dc3545', 
        'warning' => '#ffc107',
        'info' => '#17a2b8'
    ];
    $color = $colors[$type] ?? '#6c757d';
    $icon = $type === 'success' ? 'check' : ($type === 'error' ? 'times' : ($type === 'warning' ? 'exclamation-triangle' : 'info'));
    echo "<div style='color: $color; margin: 8px 0; padding: 8px; border-left: 3px solid $color; background: rgba(" . 
         hexdec(substr($color, 1, 2)) . "," . hexdec(substr($color, 3, 2)) . "," . hexdec(substr($color, 5, 2)) . ", 0.1);'>
         <i class='fas fa-$icon me-2'></i>$message</div>";
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإصلاح النهائي للنظام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .fix-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .progress-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 10px;
            background: #e9ecef;
            margin: 0 2px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .step.active {
            background: #007bff;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="section-header">
                    <h2><i class="fas fa-tools me-3"></i>الإصلاح النهائي لنظام منصة التعلم</h2>
                    <p class="mb-0">إصلاح شامل لجميع مشاكل النظام وقاعدة البيانات</p>
                </div>
                
                <!-- مؤشر التقدم -->
                <div class="progress-container">
                    <div class="step-indicator">
                        <div class="step" id="step1">1. قاعدة البيانات</div>
                        <div class="step" id="step2">2. البيانات المالية</div>
                        <div class="step" id="step3">3. إعدادات النظام</div>
                        <div class="step" id="step4">4. DataTables</div>
                        <div class="step" id="step5">5. الاختبار</div>
                    </div>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="progressBar">
                            <span id="progressText">بدء الإصلاح...</span>
                        </div>
                    </div>
                </div>

                <?php
                $totalSteps = 5;
                $currentStep = 0;
                
                function updateProgress($step, $total, $message) {
                    $percentage = ($step / $total) * 100;
                    echo "<script>
                        document.getElementById('progressBar').style.width = '{$percentage}%';
                        document.getElementById('progressText').textContent = '$message';
                        document.getElementById('step{$step}').classList.add('active');
                        if ($step > 1) {
                            document.getElementById('step" . ($step-1) . "').classList.remove('active');
                            document.getElementById('step" . ($step-1) . "').classList.add('completed');
                        }
                    </script>";
                    flush();
                    ob_flush();
                }

                // الخطوة 1: إصلاح قاعدة البيانات
                echo "<div class='fix-card'>";
                echo "<h4><i class='fas fa-database text-primary me-2'></i>الخطوة 1: إصلاح هيكل قاعدة البيانات</h4>";
                
                updateProgress(++$currentStep, $totalSteps, "إصلاح جدول course_enrollments...");
                
                // إنشاء جدول course_enrollments إذا لم يكن موجوداً
                if (!tableExists('course_enrollments')) {
                    $schema = "
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        course_id INT NOT NULL,
                        student_id INT NOT NULL,
                        enrollment_type ENUM('free', 'paid', 'scholarship') DEFAULT 'free',
                        payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
                        payment_amount DECIMAL(10,2) DEFAULT 0.00,
                        payment_method VARCHAR(50) DEFAULT NULL,
                        payment_reference VARCHAR(255) DEFAULT NULL,
                        progress_percentage DECIMAL(5,2) DEFAULT 0.00,
                        completion_date DATETIME DEFAULT NULL,
                        certificate_issued BOOLEAN DEFAULT FALSE,
                        certificate_url VARCHAR(500) DEFAULT NULL,
                        status ENUM('active', 'completed', 'dropped', 'suspended') DEFAULT 'active',
                        enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        
                        INDEX idx_course_id (course_id),
                        INDEX idx_student_id (student_id),
                        INDEX idx_status (status),
                        INDEX idx_payment_status (payment_status),
                        UNIQUE KEY unique_enrollment (course_id, student_id)
                    ";
                    
                    if (createTableIfNotExists('course_enrollments', $schema)) {
                        logMessage("✅ تم إنشاء جدول course_enrollments", 'success');
                    }
                } else {
                    logMessage("✅ جدول course_enrollments موجود", 'success');
                    
                    // إضافة الأعمدة المفقودة
                    $columns_to_add = [
                        'payment_amount' => 'DECIMAL(10,2) DEFAULT 0.00',
                        'payment_status' => "ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending'",
                        'enrollment_type' => "ENUM('free', 'paid', 'scholarship') DEFAULT 'free'"
                    ];
                    
                    foreach ($columns_to_add as $column => $definition) {
                        if (addColumn('course_enrollments', $column, $definition)) {
                            logMessage("✅ تم إضافة عمود $column", 'success');
                        } else {
                            logMessage("⚠️ عمود $column موجود بالفعل", 'warning');
                        }
                    }
                }
                echo "</div>";
                
                // الخطوة 2: تحديث البيانات المالية
                echo "<div class='fix-card'>";
                echo "<h4><i class='fas fa-money-bill-wave text-success me-2'></i>الخطوة 2: تحديث البيانات المالية</h4>";
                
                updateProgress(++$currentStep, $totalSteps, "تحديث البيانات المالية...");
                
                // تحديث البيانات المالية
                $updated = executeQuery("
                    UPDATE course_enrollments ce 
                    JOIN courses c ON ce.course_id = c.id 
                    SET ce.payment_amount = CASE 
                        WHEN c.course_type = 'paid' THEN c.price 
                        ELSE 0 
                    END,
                    ce.payment_status = CASE 
                        WHEN c.course_type = 'free' THEN 'completed'
                        WHEN c.course_type = 'paid' THEN 'completed'
                        ELSE 'pending'
                    END,
                    ce.enrollment_type = c.course_type
                    WHERE ce.payment_amount IS NULL OR ce.payment_amount = 0
                ");
                
                if ($updated) {
                    logMessage("✅ تم تحديث البيانات المالية بنجاح", 'success');
                } else {
                    logMessage("⚠️ لا توجد بيانات تحتاج تحديث", 'warning');
                }
                echo "</div>";
                
                // الخطوة 3: إعدادات النظام
                echo "<div class='fix-card'>";
                echo "<h4><i class='fas fa-cogs text-warning me-2'></i>الخطوة 3: إعدادات النظام</h4>";
                
                updateProgress(++$currentStep, $totalSteps, "إصلاح إعدادات النظام...");
                
                // إنشاء جدول system_settings
                if (!tableExists('system_settings')) {
                    $schema = "
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        setting_key VARCHAR(100) UNIQUE NOT NULL,
                        setting_value TEXT,
                        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ";
                    
                    if (createTableIfNotExists('system_settings', $schema)) {
                        logMessage("✅ تم إنشاء جدول system_settings", 'success');
                        
                        // إضافة الإعدادات الافتراضية
                        $settings = [
                            ['platform_commission', '30', 'number', 'نسبة عمولة المنصة (%)'],
                            ['site_name', 'منصة التعلم', 'string', 'اسم الموقع'],
                            ['max_file_size', '50', 'number', 'الحد الأقصى لحجم الملف (MB)'],
                            ['currency', 'ر.س', 'string', 'العملة المستخدمة'],
                            ['timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية']
                        ];
                        
                        foreach ($settings as $setting) {
                            setSystemSetting($setting[0], $setting[1], $setting[2], $setting[3]);
                        }
                        logMessage("✅ تم إضافة الإعدادات الافتراضية", 'success');
                    }
                } else {
                    logMessage("✅ جدول system_settings موجود", 'success');
                }
                echo "</div>";
                
                // الخطوة 4: إصلاح DataTables
                echo "<div class='fix-card'>";
                echo "<h4><i class='fas fa-table text-info me-2'></i>الخطوة 4: إصلاح DataTables</h4>";
                
                updateProgress(++$currentStep, $totalSteps, "إنشاء ملف إصلاح DataTables...");
                
                if (!is_dir('assets/js')) {
                    mkdir('assets/js', 0755, true);
                }
                
                $datatables_fix = '
// إصلاح نهائي لمشاكل DataTables
$.fn.dataTable.ext.errMode = "none";

function initSafeDataTable(selector, options = {}) {
    try {
        if (!$(selector).length) {
            console.warn("Element not found: " + selector);
            return null;
        }
        
        if ($.fn.DataTable.isDataTable(selector)) {
            $(selector).DataTable().destroy();
            console.log("Destroyed existing DataTable: " + selector);
        }
        
        const defaultOptions = {
            responsive: true,
            pageLength: 25,
            autoWidth: false,
            processing: false,
            serverSide: false,
            stateSave: false,
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                info: "عرض _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                infoEmpty: "عرض 0 إلى 0 من أصل 0 مدخل",
                lengthMenu: "عرض _MENU_ مدخلات",
                search: "البحث:",
                zeroRecords: "لم يتم العثور على نتائج مطابقة",
                paginate: {
                    first: "الأول",
                    last: "الأخير", 
                    next: "التالي",
                    previous: "السابق"
                }
            }
        };
        
        const finalOptions = $.extend(true, {}, defaultOptions, options);
        const table = $(selector).DataTable(finalOptions);
        
        console.log("DataTable initialized successfully: " + selector);
        return table;
        
    } catch (error) {
        console.error("DataTable Error:", error);
        return null;
    }
}

$(document).ready(function() {
    setTimeout(function() {
        if ($("#usersTable").length) initSafeDataTable("#usersTable");
        if ($("#instructorsTable").length) initSafeDataTable("#instructorsTable");
        if ($("#studentsTable").length) initSafeDataTable("#studentsTable");
        if ($("#coursesTable").length) initSafeDataTable("#coursesTable");
        if ($("#requestsTable").length) initSafeDataTable("#requestsTable");
    }, 500);
});

$(window).on("beforeunload", function() {
    $(".dataTable").each(function() {
        if ($.fn.DataTable.isDataTable(this)) {
            try {
                $(this).DataTable().destroy();
            } catch (e) {
                console.warn("Error destroying DataTable:", e);
            }
        }
    });
});

console.log("Final DataTables Fix Loaded Successfully");
';
                
                if (file_put_contents('assets/js/datatables-final-fix.js', $datatables_fix)) {
                    logMessage("✅ تم إنشاء ملف إصلاح DataTables النهائي", 'success');
                } else {
                    logMessage("❌ فشل في إنشاء ملف إصلاح DataTables", 'error');
                }
                echo "</div>";
                
                // الخطوة 5: اختبار النظام
                echo "<div class='fix-card'>";
                echo "<h4><i class='fas fa-check-circle text-success me-2'></i>الخطوة 5: اختبار النظام</h4>";
                
                updateProgress(++$currentStep, $totalSteps, "اختبار النظام...");
                
                // إحصائيات النظام
                $stats = [];
                $stats['users'] = countRecords('users');
                $stats['students'] = countRecords('users', "role = 'student'");
                $stats['instructors'] = countRecords('users', "role = 'instructor'");
                $stats['courses'] = countRecords('courses');
                $stats['enrollments'] = countRecords('course_enrollments');
                
                $revenue_result = fetchOne("SELECT COALESCE(SUM(payment_amount), 0) as total FROM course_enrollments WHERE payment_status = 'completed'");
                $stats['revenue'] = $revenue_result['total'] ?? 0;
                
                echo "<div class='row mt-3'>";
                echo "<div class='col-md-2'><div class='alert alert-primary text-center'><h4>{$stats['users']}</h4>إجمالي المستخدمين</div></div>";
                echo "<div class='col-md-2'><div class='alert alert-info text-center'><h4>{$stats['students']}</h4>الطلاب</div></div>";
                echo "<div class='col-md-2'><div class='alert alert-warning text-center'><h4>{$stats['instructors']}</h4>المدربين</div></div>";
                echo "<div class='col-md-2'><div class='alert alert-success text-center'><h4>{$stats['courses']}</h4>الكورسات</div></div>";
                echo "<div class='col-md-2'><div class='alert alert-secondary text-center'><h4>{$stats['enrollments']}</h4>التسجيلات</div></div>";
                echo "<div class='col-md-2'><div class='alert alert-danger text-center'><h4>" . number_format($stats['revenue'], 2) . "</h4>إجمالي الإيرادات</div></div>";
                echo "</div>";
                
                // اختبار الاستعلامات المالية
                $test_result = fetchOne("
                    SELECT 
                        COUNT(*) as total_enrollments,
                        COALESCE(SUM(payment_amount), 0) as total_revenue,
                        COUNT(CASE WHEN payment_status = 'completed' THEN 1 END) as completed_payments
                    FROM course_enrollments
                ");
                
                if ($test_result) {
                    logMessage("✅ اختبار الاستعلامات المالية: {$test_result['total_enrollments']} تسجيل، {$test_result['total_revenue']} ر.س إيرادات", 'success');
                } else {
                    logMessage("❌ فشل في اختبار الاستعلامات المالية", 'error');
                }
                
                logMessage("🎉 تم إكمال جميع خطوات الإصلاح بنجاح!", 'success');
                echo "</div>";
                ?>
                
                <!-- أزرار الاختبار -->
                <div class="fix-card">
                    <h4><i class="fas fa-play text-primary me-2"></i>اختبار النظام</h4>
                    <p class="text-muted">اختبر الصفحات التالية للتأكد من عمل النظام بشكل صحيح:</p>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="financial-reports.php" class="btn btn-primary w-100 btn-lg">
                                <i class="fas fa-chart-line me-2"></i>التقارير المالية
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="manage-users.php" class="btn btn-info w-100 btn-lg">
                                <i class="fas fa-users me-2"></i>إدارة المستخدمين
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="manage-instructors.php" class="btn btn-warning w-100 btn-lg">
                                <i class="fas fa-chalkboard-teacher me-2"></i>إدارة المدربين
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="dashboard.php" class="btn btn-success w-100 btn-lg">
                                <i class="fas fa-home me-2"></i>لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
    
    <script>
        // تحديث شريط التقدم إلى 100% عند الانتهاء
        setTimeout(function() {
            document.getElementById('progressBar').style.width = '100%';
            document.getElementById('progressBar').classList.remove('progress-bar-animated');
            document.getElementById('progressText').textContent = 'تم إكمال الإصلاح بنجاح!';
            document.getElementById('step5').classList.remove('active');
            document.getElementById('step5').classList.add('completed');
        }, 2000);
    </script>
</body>
</html>
