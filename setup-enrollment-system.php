<?php
require_once 'config/database.php';

echo "<h2>إعداد نظام التسجيل والموافقة</h2>";

try {
    // التحقق من وجود جدول course_enrollments
    $stmt = $conn->query("SHOW TABLES LIKE 'course_enrollments'");
    if ($stmt->rowCount() == 0) {
        echo "<p>إنشاء جدول course_enrollments...</p>";
        $conn->exec("CREATE TABLE course_enrollments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            student_id INT NOT NULL,
            enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('active', 'inactive', 'completed', 'dropped') DEFAULT 'active',
            progress DECIMAL(5,2) DEFAULT 0.00,
            completion_date TIMESTAMP NULL,
            grade DECIMAL(5,2) NULL,
            notes TEXT NULL,
            UNIQUE KEY unique_enrollment (course_id, student_id),
            INDEX idx_course_id (course_id),
            INDEX idx_student_id (student_id),
            INDEX idx_status (status),
            FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✅ تم إنشاء جدول course_enrollments</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ جدول course_enrollments موجود بالفعل</p>";
    }
    
    // التحقق من وجود طلبات انضمام معلقة
    $stmt = $conn->query("SELECT COUNT(*) FROM join_requests WHERE status = 'pending'");
    $pending_count = $stmt->fetchColumn();
    
    echo "<h3>📊 حالة النظام:</h3>";
    echo "<ul>";
    echo "<li><strong>طلبات الانضمام المعلقة:</strong> $pending_count</li>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM course_enrollments");
    $enrollment_count = $stmt->fetchColumn();
    echo "<li><strong>التسجيلات الحالية:</strong> $enrollment_count</li>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'student'");
    $student_count = $stmt->fetchColumn();
    echo "<li><strong>عدد الطلاب:</strong> $student_count</li>";
    
    $stmt = $conn->query("SELECT COUNT(*) FROM courses WHERE status = 'active'");
    $course_count = $stmt->fetchColumn();
    echo "<li><strong>عدد الكورسات النشطة:</strong> $course_count</li>";
    echo "</ul>";
    
    // عرض طلبات الانضمام المعلقة
    if ($pending_count > 0) {
        echo "<h3>طلبات الانضمام المعلقة:</h3>";
        $stmt = $conn->query("
            SELECT jr.id, jr.student_id, jr.course_id, jr.message, jr.created_at,
                   u.username as student_name, u.email as student_email,
                   c.title as course_title,
                   i.username as instructor_name
            FROM join_requests jr
            INNER JOIN users u ON jr.student_id = u.id
            INNER JOIN courses c ON jr.course_id = c.id
            INNER JOIN users i ON c.instructor_id = i.id
            WHERE jr.status = 'pending'
            ORDER BY jr.created_at DESC
        ");
        $pending_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>الطالب</th><th>الكورس</th><th>المدرب</th><th>التاريخ</th><th>الإجراءات</th>";
        echo "</tr>";
        
        foreach ($pending_requests as $request) {
            echo "<tr>";
            echo "<td>" . $request['id'] . "</td>";
            echo "<td>" . htmlspecialchars($request['student_name']) . "<br><small>" . htmlspecialchars($request['student_email']) . "</small></td>";
            echo "<td>" . htmlspecialchars($request['course_title']) . "</td>";
            echo "<td>" . htmlspecialchars($request['instructor_name']) . "</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($request['created_at'])) . "</td>";
            echo "<td>";
            echo "<a href='instructor/course-join-requests.php?course_id=" . $request['course_id'] . "' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin: 2px;'>مراجعة</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // إنشاء طلب انضمام تجريبي إذا لم توجد طلبات
    if ($pending_count == 0) {
        echo "<h3>إنشاء طلب انضمام تجريبي:</h3>";
        
        // جلب طالب وكورس
        $stmt = $conn->query("SELECT id, username FROM users WHERE role = 'student' LIMIT 1");
        $student = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $conn->query("SELECT id, title FROM courses WHERE status = 'active' LIMIT 1");
        $course = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($student && $course) {
            // التحقق من عدم وجود طلب سابق
            $stmt = $conn->prepare("SELECT id FROM join_requests WHERE student_id = ? AND course_id = ?");
            $stmt->execute([$student['id'], $course['id']]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO join_requests (student_id, course_id, message, status) 
                    VALUES (?, ?, ?, 'pending')
                ");
                $stmt->execute([
                    $student['id'], 
                    $course['id'], 
                    'أرغب في الانضمام لهذا الكورس لتطوير مهاراتي في البرمجة. لدي اهتمام كبير بالموضوع وأريد تعلم المزيد من خلال هذا الكورس المتميز.'
                ]);
                
                $request_id = $conn->lastInsertId();
                echo "<p style='color: green;'>✅ تم إنشاء طلب انضمام تجريبي برقم: $request_id</p>";
                echo "<p><strong>الطالب:</strong> " . htmlspecialchars($student['username']) . "</p>";
                echo "<p><strong>الكورس:</strong> " . htmlspecialchars($course['title']) . "</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ يوجد طلب انضمام سابق لهذا الطالب</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ لا يوجد طلاب أو كورسات للاختبار</p>";
            if (!$student) echo "<p><a href='create_sample_students.php'>إنشاء طلاب تجريبيين</a></p>";
        }
    }
    
    // اختبار نظام الموافقة
    echo "<h3>🧪 اختبار نظام الموافقة:</h3>";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>";
    echo "<h4>خطوات الاختبار:</h4>";
    echo "<ol>";
    echo "<li>تسجيل الدخول كمدرب</li>";
    echo "<li>الذهاب إلى صفحة طلبات الانضمام</li>";
    echo "<li>النقر على زر 'قبول' أو 'رفض'</li>";
    echo "<li>ملء النموذج والموافقة</li>";
    echo "<li>التحقق من إضافة الطالب للكورس</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>🔗 روابط مفيدة:</h3>";
    echo "<ul>";
    echo "<li><a href='instructor/course-join-requests.php?course_id=1' target='_blank'>صفحة طلبات الانضمام للكورس الأول</a></li>";
    echo "<li><a href='test-join-course.php' target='_blank'>إرسال طلب انضمام جديد</a></li>";
    echo "<li><a href='instructor/login.php' target='_blank'>تسجيل دخول المدرب</a></li>";
    echo "<li><a href='debug-join-requests.php' target='_blank'>عرض جميع الطلبات</a></li>";
    echo "</ul>";
    
    // معلومات تسجيل الدخول
    echo "<h3>🔑 معلومات تسجيل الدخول:</h3>";
    $stmt = $conn->query("SELECT username, role FROM users WHERE role IN ('instructor', 'admin') LIMIT 3");
    $instructors = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($instructors)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>اسم المستخدم</th><th>الدور</th><th>كلمة المرور</th></tr>";
        foreach ($instructors as $instructor) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($instructor['username']) . "</td>";
            echo "<td>" . htmlspecialchars($instructor['role']) . "</td>";
            echo "<td>123456</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ: " . $e->getMessage() . "</h3>";
}
?>
