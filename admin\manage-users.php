<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// تم نقل دالة logUserActivity إلى ملف functions.php

// التحقق من صلاحيات المدير
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة المستخدمين';
$breadcrumbs = [
    ['title' => 'إدارة المستخدمين']
];

$success = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];

        try {
            if ($action === 'add_user') {
                // إضافة مستخدم جديد
                $name = trim($_POST['name']);
                $email = trim($_POST['email']);
                $phone = trim($_POST['phone']);
                $role = $_POST['role'];
                $password = $_POST['password'];
                $status = $_POST['status'] ?? 'active';

                // التحقق من صحة البيانات
                if (empty($name) || empty($email) || empty($password)) {
                    throw new Exception('جميع الحقول المطلوبة يجب ملؤها');
                }

                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('البريد الإلكتروني غير صحيح');
                }

                // التحقق من عدم وجود البريد الإلكتروني مسبقاً
                $check_stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
                $check_stmt->execute([$email]);
                if ($check_stmt->fetch()) {
                    throw new Exception('البريد الإلكتروني موجود مسبقاً');
                }

                // تشفير كلمة المرور
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);

                // إدراج المستخدم الجديد
                $insert_stmt = $conn->prepare("
                    INSERT INTO users (name, email, phone, password, role, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                $insert_stmt->execute([$name, $email, $phone, $hashed_password, $role, $status]);

                $success = 'تم إضافة المستخدم بنجاح';
                logUserActivity($_SESSION['user_id'], 'add_user', "تم إضافة مستخدم جديد: $name ($email)");

            } else {
                $user_id = (int)$_POST['user_id'];

                switch ($action) {
                case 'activate':
                    $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE id = ?");
                    $stmt->execute([$user_id]);
                    $success = 'تم تفعيل المستخدم بنجاح';
                    break;
                    
                case 'suspend':
                    $stmt = $conn->prepare("UPDATE users SET status = 'suspended' WHERE id = ?");
                    $stmt->execute([$user_id]);
                    $success = 'تم تعليق المستخدم بنجاح';
                    break;
                    
                case 'delete':
                    // حذف آمن - تعطيل بدلاً من الحذف
                    $stmt = $conn->prepare("UPDATE users SET status = 'inactive', email = CONCAT(email, '_deleted_', UNIX_TIMESTAMP()) WHERE id = ?");
                    $stmt->execute([$user_id]);
                    $success = 'تم حذف المستخدم بنجاح';
                    break;
                    
                case 'reset_password':
                    $new_password = 'temp123456';
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $stmt->execute([$hashed_password, $user_id]);
                    $success = "تم إعادة تعيين كلمة المرور إلى: $new_password";
                    break;
                    
                case 'bulk_action':
                    $bulk_action = $_POST['bulk_action'];
                    $selected_users = $_POST['selected_users'] ?? [];
                    
                    if (!empty($selected_users)) {
                        $placeholders = str_repeat('?,', count($selected_users) - 1) . '?';
                        
                        switch ($bulk_action) {
                            case 'activate':
                                $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE id IN ($placeholders)");
                                $stmt->execute($selected_users);
                                $success = 'تم تفعيل المستخدمين المحددين';
                                break;
                                
                            case 'suspend':
                                $stmt = $conn->prepare("UPDATE users SET status = 'suspended' WHERE id IN ($placeholders)");
                                $stmt->execute($selected_users);
                                $success = 'تم تعليق المستخدمين المحددين';
                                break;
                                
                            case 'delete':
                                $stmt = $conn->prepare("UPDATE users SET status = 'inactive' WHERE id IN ($placeholders)");
                                $stmt->execute($selected_users);
                                $success = 'تم حذف المستخدمين المحددين';
                                break;
                        }
                    }
                    break;
                }
            }

            // تسجيل النشاط (إذا لم يكن إضافة مستخدم)
            if ($action !== 'add_user') {
                logUserActivity($_SESSION['user_id'], 'user_management', "تم تنفيذ إجراء: $action");
            }

        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء تنفيذ العملية: ' . $e->getMessage();
        }
    }
}

// معاملات البحث والفلترة
$search = $_GET['search'] ?? '';
$role_filter = $_GET['role'] ?? '';
$status_filter = $_GET['status'] ?? '';
$sort = $_GET['sort'] ?? 'created_at';
$order = $_GET['order'] ?? 'DESC';
$page = (int)($_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = ["role != 'admin'"];
$params = [];

if ($search) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if ($role_filter) {
    $where_conditions[] = "role = ?";
    $params[] = $role_filter;
}

if ($status_filter) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// تهيئة المتغيرات بقيم افتراضية
$total_users = 0;
$total_pages = 1;
$users = [];
$stats = [
    'total_users' => 0,
    'total_students' => 0,
    'total_instructors' => 0,
    'active_users' => 0,
    'pending_users' => 0,
    'suspended_users' => 0,
    'new_users_month' => 0,
    'active_last_week' => 0
];

try {
    // جلب إجمالي عدد المستخدمين
    $count_stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE $where_clause");
    $count_stmt->execute($params);
    $total_users = $count_stmt->fetchColumn();
    $total_pages = ceil($total_users / $per_page);

    // جلب المستخدمين
    $stmt = $conn->prepare("
        SELECT u.*,
               COALESCE((SELECT COUNT(*) FROM courses WHERE instructor_id = u.id), 0) as courses_count,
               COALESCE((SELECT COUNT(*) FROM course_enrollments WHERE student_id = u.id), 0) as enrollments_count,
               COALESCE((SELECT SUM(payment_amount) FROM course_enrollments WHERE student_id = u.id AND payment_status = 'completed'), 0) as total_spent,
               COALESCE((SELECT SUM(ce.payment_amount * 0.7) FROM course_enrollments ce JOIN courses c ON ce.course_id = c.id WHERE c.instructor_id = u.id AND ce.payment_status = 'completed'), 0) as total_earned
        FROM users u
        WHERE $where_clause
        ORDER BY $sort $order
        LIMIT $per_page OFFSET $offset
    ");
    $stmt->execute($params);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إحصائيات سريعة
    $stats_stmt = $conn->query("
        SELECT
            COUNT(*) as total_users,
            COUNT(CASE WHEN role = 'student' THEN 1 END) as total_students,
            COUNT(CASE WHEN role = 'instructor' THEN 1 END) as total_instructors,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_users,
            COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_users,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_month,
            COALESCE(COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END), 0) as active_last_week
        FROM users
        WHERE role != 'admin'
    ");
    $stats_result = $stats_stmt->fetch(PDO::FETCH_ASSOC);
    if ($stats_result) {
        $stats = $stats_result;
    }

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
}

include 'includes/header.php';
?>

<!-- الإحصائيات السريعة -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-primary mb-1"><?php echo number_format($stats['total_users']); ?></h3>
                    <p class="text-muted mb-0">إجمالي المستخدمين</p>
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>
                        +<?php echo $stats['new_users_month']; ?> هذا الشهر
                    </small>
                </div>
                <div class="text-primary">
                    <i class="fas fa-users fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-success mb-1"><?php echo number_format($stats['total_students']); ?></h3>
                    <p class="text-muted mb-0">الطلاب</p>
                    <small class="text-muted">
                        <i class="fas fa-user-graduate me-1"></i>
                        متعلمين
                    </small>
                </div>
                <div class="text-success">
                    <i class="fas fa-user-graduate fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-warning mb-1"><?php echo number_format($stats['total_instructors']); ?></h3>
                    <p class="text-muted mb-0">المدربين</p>
                    <small class="text-muted">
                        <i class="fas fa-chalkboard-teacher me-1"></i>
                        خبراء
                    </small>
                </div>
                <div class="text-warning">
                    <i class="fas fa-chalkboard-teacher fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-info mb-1"><?php echo number_format($stats['active_users']); ?></h3>
                    <p class="text-muted mb-0">المستخدمين النشطين</p>
                    <small class="text-success">
                        <i class="fas fa-check-circle me-1"></i>
                        <?php echo $stats['active_last_week']; ?> نشط هذا الأسبوع
                    </small>
                </div>
                <div class="text-info">
                    <i class="fas fa-user-check fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($success): ?>
<div class="alert alert-success alert-dismissible fade show" data-aos="fade-down">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo htmlspecialchars($success); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show" data-aos="fade-down">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?php echo htmlspecialchars($error); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- أدوات البحث والفلترة -->
<div class="card-admin mb-4" data-aos="fade-up">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="ابحث بالاسم أو البريد الإلكتروني..." 
                       value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">الدور</label>
                <select name="role" class="form-select">
                    <option value="">جميع الأدوار</option>
                    <option value="student" <?php echo $role_filter === 'student' ? 'selected' : ''; ?>>طالب</option>
                    <option value="instructor" <?php echo $role_filter === 'instructor' ? 'selected' : ''; ?>>مدرب</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>معلق</option>
                    <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>موقوف</option>
                    <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">ترتيب حسب</label>
                <select name="sort" class="form-select">
                    <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>تاريخ التسجيل</option>
                    <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>الاسم</option>
                    <option value="last_login" <?php echo $sort === 'last_login' ? 'selected' : ''; ?>>آخر دخول</option>
                    <option value="email" <?php echo $sort === 'email' ? 'selected' : ''; ?>>البريد الإلكتروني</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-admin-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="manage-users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-redo"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول المستخدمين -->
<div class="card-admin" data-aos="fade-up">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-users me-2"></i>
                قائمة المستخدمين (<?php echo number_format($total_users); ?>)
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus me-1"></i>
                    إضافة مستخدم
                </button>
                <button class="btn btn-info btn-sm" onclick="exportData('excel', 'export-users.php', 'users.xlsx')">
                    <i class="fas fa-file-excel me-1"></i>
                    تصدير Excel
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($users)): ?>
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مستخدمين</h5>
            <p class="text-muted">لم يتم العثور على مستخدمين يطابقون معايير البحث</p>
        </div>
        <?php else: ?>
        <form id="bulkActionForm" method="POST">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>المستخدم</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>الإحصائيات</th>
                            <th>تاريخ التسجيل</th>
                            <th>آخر دخول</th>
                            <th width="200">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <input type="checkbox" name="selected_users[]" value="<?php echo $user['id']; ?>" class="form-check-input item-checkbox">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-3">
                                        <?php if ($user['profile_image']): ?>
                                        <img src="<?php echo htmlspecialchars($user['profile_image']); ?>" 
                                             class="rounded-circle" width="40" height="40" alt="صورة المستخدم">
                                        <?php else: ?>
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <div class="fw-bold"><?php echo htmlspecialchars($user['name']); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                        <?php if ($user['phone']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                $role_badges = [
                                    'student' => 'bg-success',
                                    'instructor' => 'bg-warning',
                                    'admin' => 'bg-danger'
                                ];
                                $role_names = [
                                    'student' => 'طالب',
                                    'instructor' => 'مدرب',
                                    'admin' => 'مدير'
                                ];
                                ?>
                                <span class="badge <?php echo $role_badges[$user['role']]; ?>">
                                    <?php echo $role_names[$user['role']]; ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $status_badges = [
                                    'active' => 'bg-success',
                                    'pending' => 'bg-warning',
                                    'suspended' => 'bg-danger',
                                    'inactive' => 'bg-secondary'
                                ];
                                $status_names = [
                                    'active' => 'نشط',
                                    'pending' => 'معلق',
                                    'suspended' => 'موقوف',
                                    'inactive' => 'غير نشط'
                                ];
                                ?>
                                <span class="badge <?php echo $status_badges[$user['status']] ?? 'bg-secondary'; ?>">
                                    <?php echo $status_names[$user['status']] ?? $user['status']; ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($user['role'] === 'instructor'): ?>
                                <small class="text-muted">
                                    <i class="fas fa-book me-1"></i><?php echo $user['courses_count']; ?> كورس<br>
                                    <i class="fas fa-money-bill me-1"></i><?php echo number_format($user['total_earned'] ?? 0, 2); ?> ر.س
                                </small>
                                <?php elseif ($user['role'] === 'student'): ?>
                                <small class="text-muted">
                                    <i class="fas fa-graduation-cap me-1"></i><?php echo $user['enrollments_count']; ?> تسجيل<br>
                                    <i class="fas fa-money-bill me-1"></i><?php echo number_format($user['total_spent'] ?? 0, 2); ?> ر.س
                                </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo date('Y-m-d', strtotime($user['created_at'])); ?>
                                </small>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'لم يسجل دخول'; ?>
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" 
                                            data-bs-target="#viewUserModal<?php echo $user['id']; ?>">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" data-bs-toggle="modal" 
                                            data-bs-target="#editUserModal<?php echo $user['id']; ?>">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                data-bs-toggle="dropdown">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <?php if ($user['status'] !== 'active'): ?>
                                            <li>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="action" value="activate">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="dropdown-item text-success">
                                                        <i class="fas fa-check me-2"></i>تفعيل
                                                    </button>
                                                </form>
                                            </li>
                                            <?php endif; ?>
                                            
                                            <?php if ($user['status'] !== 'suspended'): ?>
                                            <li>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="action" value="suspend">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="dropdown-item text-warning"
                                                            onclick="return confirm('هل أنت متأكد من تعليق هذا المستخدم؟')">
                                                        <i class="fas fa-pause me-2"></i>تعليق
                                                    </button>
                                                </form>
                                            </li>
                                            <?php endif; ?>
                                            
                                            <li>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="action" value="reset_password">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="dropdown-item text-info"
                                                            onclick="return confirm('هل أنت متأكد من إعادة تعيين كلمة المرور؟')">
                                                        <i class="fas fa-key me-2"></i>إعادة تعيين كلمة المرور
                                                    </button>
                                                </form>
                                            </li>
                                            
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="dropdown-item text-danger"
                                                            onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                        <i class="fas fa-trash me-2"></i>حذف
                                                    </button>
                                                </form>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- الإجراءات المجمعة -->
            <div class="card-footer" id="bulkActionBtn" style="display: none;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <select name="bulk_action" class="form-select d-inline-block w-auto">
                            <option value="">اختر إجراء...</option>
                            <option value="activate">تفعيل المحدد</option>
                            <option value="suspend">تعليق المحدد</option>
                            <option value="delete">حذف المحدد</option>
                        </select>
                    </div>
                    <button type="submit" name="action" value="bulk_action" class="btn btn-primary"
                            onclick="return confirm('هل أنت متأكد من تنفيذ هذا الإجراء على المستخدمين المحددين؟')">
                        تنفيذ الإجراء
                    </button>
                </div>
            </div>
        </form>
        <?php endif; ?>
    </div>
</div>

<!-- التصفح -->
<?php if ($total_pages > 1): ?>
<nav class="mt-4" data-aos="fade-up">
    <ul class="pagination justify-content-center">
        <?php if ($page > 1): ?>
        <li class="page-item">
            <a class="page-link" href="?page=<?php echo $page-1; ?>&<?php echo http_build_query($_GET); ?>">السابق</a>
        </li>
        <?php endif; ?>
        
        <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
            <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
        </li>
        <?php endfor; ?>
        
        <?php if ($page < $total_pages): ?>
        <li class="page-item">
            <a class="page-link" href="?page=<?php echo $page+1; ?>&<?php echo http_build_query($_GET); ?>">التالي</a>
        </li>
        <?php endif; ?>
    </ul>
</nav>
<?php endif; ?>

<!-- نموذج إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="addUserForm">
                <input type="hidden" name="action" value="add_user">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="col-md-12 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="col-md-12 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">الدور <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="student">طالب</option>
                                <option value="instructor">مدرب</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active">نشط</option>
                                <option value="pending">معلق</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>

                        <div class="col-md-12 mb-3">
                            <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="password-icon"></i>
                                </button>
                            </div>
                            <small class="text-muted">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                        </div>

                        <div class="col-md-12 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>إضافة المستخدم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تبديل إظهار/إخفاء كلمة المرور
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '-icon');

    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// التحقق من صحة النموذج
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;

    if (password.length < 6) {
        e.preventDefault();
        alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return false;
    }

    if (password !== confirmPassword) {
        e.preventDefault();
        alert('كلمة المرور وتأكيد كلمة المرور غير متطابقين');
        return false;
    }
});

// تحديد/إلغاء تحديد جميع العناصر
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    toggleBulkActions();
});

// إظهار/إخفاء أزرار الإجراءات المجمعة
function toggleBulkActions() {
    const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
    const bulkActionBtn = document.getElementById('bulkActionBtn');

    if (checkedBoxes.length > 0) {
        bulkActionBtn.style.display = 'block';
    } else {
        bulkActionBtn.style.display = 'none';
    }
}

// إضافة مستمع للأحداث على جميع checkboxes
document.querySelectorAll('.item-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', toggleBulkActions);
});

// دالة تصدير البيانات
function exportData(format, url, filename) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = url;
    form.style.display = 'none';

    const formatInput = document.createElement('input');
    formatInput.name = 'format';
    formatInput.value = format;
    form.appendChild(formatInput);

    const filenameInput = document.createElement('input');
    filenameInput.name = 'filename';
    filenameInput.value = filename;
    form.appendChild(filenameInput);

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
</script>

<?php include 'includes/footer.php'; ?>
