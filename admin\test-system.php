<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-check-circle me-2"></i>اختبار النظام</h3>
                <p class="mb-0">اختبار جميع مكونات النظام للتأكد من عملها بشكل صحيح</p>
            </div>
            <div class="card-body">
                
                <!-- اختبار قاعدة البيانات -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5><i class="fas fa-database text-primary me-2"></i>اختبار قاعدة البيانات</h5>
                        <div class="row">
                            <?php
                            // اختبار الاتصال
                            if (testConnection()) {
                                echo '<div class="col-md-3"><div class="alert alert-success text-center"><i class="fas fa-check"></i><br>الاتصال سليم</div></div>';
                            } else {
                                echo '<div class="col-md-3"><div class="alert alert-danger text-center"><i class="fas fa-times"></i><br>فشل الاتصال</div></div>';
                            }
                            
                            // اختبار الجداول
                            $tables = ['users', 'courses', 'course_enrollments', 'system_settings'];
                            $tables_ok = 0;
                            foreach ($tables as $table) {
                                if (tableExists($table)) {
                                    $tables_ok++;
                                }
                            }
                            echo '<div class="col-md-3"><div class="alert alert-' . ($tables_ok == 4 ? 'success' : 'warning') . ' text-center"><i class="fas fa-table"></i><br>' . $tables_ok . '/4 جداول</div></div>';
                            
                            // اختبار البيانات
                            $users_count = countRecords('users');
                            $courses_count = countRecords('courses');
                            echo '<div class="col-md-3"><div class="alert alert-info text-center"><i class="fas fa-users"></i><br>' . $users_count . ' مستخدم</div></div>';
                            echo '<div class="col-md-3"><div class="alert alert-info text-center"><i class="fas fa-book"></i><br>' . $courses_count . ' كورس</div></div>';
                            ?>
                        </div>
                    </div>
                </div>
                
                <!-- اختبار التقارير المالية -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5><i class="fas fa-chart-line text-success me-2"></i>اختبار التقارير المالية</h5>
                        <?php
                        try {
                            $financial_test = fetchOne("
                                SELECT 
                                    COUNT(*) as total_enrollments,
                                    COALESCE(SUM(payment_amount), 0) as total_revenue,
                                    COUNT(CASE WHEN payment_status = 'completed' THEN 1 END) as completed_payments
                                FROM course_enrollments
                            ");
                            
                            if ($financial_test) {
                                echo '<div class="alert alert-success">';
                                echo '<i class="fas fa-check me-2"></i>اختبار التقارير المالية نجح<br>';
                                echo '<strong>التسجيلات:</strong> ' . $financial_test['total_enrollments'] . '<br>';
                                echo '<strong>الإيرادات:</strong> ' . number_format($financial_test['total_revenue'], 2) . ' ر.س<br>';
                                echo '<strong>المدفوعات المكتملة:</strong> ' . $financial_test['completed_payments'];
                                echo '</div>';
                            } else {
                                echo '<div class="alert alert-danger"><i class="fas fa-times me-2"></i>فشل في اختبار التقارير المالية</div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger"><i class="fas fa-times me-2"></i>خطأ: ' . $e->getMessage() . '</div>';
                        }
                        ?>
                    </div>
                </div>
                
                <!-- اختبار DataTables -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5><i class="fas fa-table text-warning me-2"></i>اختبار DataTables</h5>
                        <div class="alert alert-info">
                            <p>جدول اختبار DataTables:</p>
                            <table id="testTable" class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // جلب بعض البيانات الحقيقية للاختبار
                                    $test_users = fetchAll("SELECT id, name, email, created_at, status FROM users LIMIT 10");
                                    foreach ($test_users as $user) {
                                        echo "<tr>";
                                        echo "<td>{$user['id']}</td>";
                                        echo "<td>" . htmlspecialchars($user['name']) . "</td>";
                                        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                                        echo "<td>" . date('Y-m-d', strtotime($user['created_at'])) . "</td>";
                                        echo "<td><span class='badge bg-" . ($user['status'] == 'active' ? 'success' : 'secondary') . "'>{$user['status']}</span></td>";
                                        echo "</tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                            <div id="datatableStatus" class="mt-3"></div>
                        </div>
                    </div>
                </div>
                
                <!-- روابط الاختبار -->
                <div class="row">
                    <div class="col-12">
                        <h5><i class="fas fa-link text-info me-2"></i>روابط الاختبار</h5>
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="financial-reports.php" class="btn btn-primary w-100" target="_blank">
                                    <i class="fas fa-chart-line me-2"></i>التقارير المالية
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="manage-users.php" class="btn btn-info w-100" target="_blank">
                                    <i class="fas fa-users me-2"></i>إدارة المستخدمين
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="manage-instructors.php" class="btn btn-warning w-100" target="_blank">
                                    <i class="fas fa-chalkboard-teacher me-2"></i>إدارة المدربين
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="manage-courses.php" class="btn btn-success w-100" target="_blank">
                                    <i class="fas fa-book me-2"></i>إدارة الكورسات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- ملخص الحالة -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-success text-center">
                            <h4><i class="fas fa-check-circle me-2"></i>النظام جاهز للاستخدام!</h4>
                            <p class="mb-0">تم إصلاح جميع المشاكل وإعداد النظام بنجاح</p>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="assets/js/datatables-final-fix.js"></script>
    
    <script>
    $(document).ready(function() {
        // اختبار DataTables
        setTimeout(function() {
            try {
                if ($('#testTable').length) {
                    const table = initSafeDataTable('#testTable', {
                        pageLength: 5,
                        searching: true,
                        ordering: true,
                        info: true,
                        paging: true
                    });
                    
                    if (table) {
                        $('#datatableStatus').html('<div class="alert alert-success"><i class="fas fa-check me-2"></i>تم تهيئة DataTable بنجاح! الجدول يعمل بشكل صحيح.</div>');
                        console.log('✅ DataTable test successful');
                    } else {
                        $('#datatableStatus').html('<div class="alert alert-danger"><i class="fas fa-times me-2"></i>فشل في تهيئة DataTable</div>');
                        console.error('❌ DataTable test failed');
                    }
                }
            } catch (error) {
                $('#datatableStatus').html('<div class="alert alert-danger"><i class="fas fa-times me-2"></i>خطأ في DataTable: ' + error.message + '</div>');
                console.error('❌ DataTable error:', error);
            }
        }, 1000);
        
        // اختبار الروابط
        $('a[target="_blank"]').on('click', function(e) {
            const link = $(this);
            const icon = link.find('i');
            const originalClass = icon.attr('class');
            
            // تغيير الأيقونة لإظهار التحميل
            icon.attr('class', 'fas fa-spinner fa-spin me-2');
            
            // إعادة الأيقونة الأصلية بعد ثانيتين
            setTimeout(function() {
                icon.attr('class', originalClass);
            }, 2000);
        });
    });
    </script>
</body>
</html>
