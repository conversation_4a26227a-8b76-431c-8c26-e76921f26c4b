
// إصلاح نهائي لمشاكل DataTables
$.fn.dataTable.ext.errMode = "none";

function initSafeDataTable(selector, options = {}) {
    try {
        if (!$(selector).length) {
            console.warn("Element not found: " + selector);
            return null;
        }
        
        if ($.fn.DataTable.isDataTable(selector)) {
            $(selector).DataTable().destroy();
            console.log("Destroyed existing DataTable: " + selector);
        }
        
        const defaultOptions = {
            responsive: true,
            pageLength: 25,
            autoWidth: false,
            processing: false,
            serverSide: false,
            stateSave: false,
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json",
                emptyTable: "لا توجد بيانات متاحة في الجدول",
                info: "عرض _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                infoEmpty: "عرض 0 إلى 0 من أصل 0 مدخل",
                lengthMenu: "عرض _MENU_ مدخلات",
                search: "البحث:",
                zeroRecords: "لم يتم العثور على نتائج مطابقة",
                paginate: {
                    first: "الأول",
                    last: "الأخير", 
                    next: "التالي",
                    previous: "السابق"
                }
            }
        };
        
        const finalOptions = $.extend(true, {}, defaultOptions, options);
        const table = $(selector).DataTable(finalOptions);
        
        console.log("DataTable initialized successfully: " + selector);
        return table;
        
    } catch (error) {
        console.error("DataTable Error:", error);
        return null;
    }
}

$(document).ready(function() {
    setTimeout(function() {
        if ($("#usersTable").length) initSafeDataTable("#usersTable");
        if ($("#instructorsTable").length) initSafeDataTable("#instructorsTable");
        if ($("#studentsTable").length) initSafeDataTable("#studentsTable");
        if ($("#coursesTable").length) initSafeDataTable("#coursesTable");
        if ($("#requestsTable").length) initSafeDataTable("#requestsTable");
    }, 500);
});

$(window).on("beforeunload", function() {
    $(".dataTable").each(function() {
        if ($.fn.DataTable.isDataTable(this)) {
            try {
                $(this).DataTable().destroy();
            } catch (e) {
                console.warn("Error destroying DataTable:", e);
            }
        }
    });
});

console.log("Final DataTables Fix Loaded Successfully");
