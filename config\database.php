<?php
// Database Configuration
// التحقق من عدم تعريف الثوابت مسبقاً لتجنب التعارض
if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASS')) define('DB_PASS', '');
if (!defined('DB_NAME')) define('DB_NAME', 'zoom_learning_system');

// Error reporting for development (disable in production)
if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
}

try {
    $conn = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USER, DB_PASS);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $conn->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);

    // Create database if not exists
    $conn->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $conn->exec("USE " . DB_NAME);
    
    // Create users table
    $conn->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        username VARCHAR(50) NULL,
        phone VARCHAR(20) NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'instructor', 'student') NOT NULL DEFAULT 'student',
        status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
        failed_attempts INT DEFAULT 0,
        lock_expires TIMESTAMP NULL,
        email_verified BOOLEAN DEFAULT FALSE,
        verification_token VARCHAR(255) NULL,
        reset_token VARCHAR(255) NULL,
        reset_expires TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        INDEX idx_email (email),
        INDEX idx_username (username),
        INDEX idx_role (role),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Add missing columns to users table if they don't exist
    try {
        $conn->exec("ALTER TABLE users ADD COLUMN phone VARCHAR(20) NULL AFTER email");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $conn->exec("ALTER TABLE users ADD COLUMN failed_attempts INT DEFAULT 0 AFTER status");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $conn->exec("ALTER TABLE users ADD COLUMN lock_expires TIMESTAMP NULL AFTER failed_attempts");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $conn->exec("ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE AFTER lock_expires");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $conn->exec("ALTER TABLE users ADD COLUMN verification_token VARCHAR(255) NULL AFTER email_verified");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $conn->exec("ALTER TABLE users ADD COLUMN reset_token VARCHAR(255) NULL AFTER verification_token");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $conn->exec("ALTER TABLE users ADD COLUMN reset_expires TIMESTAMP NULL AFTER reset_token");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $conn->exec("ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    // Update status enum to include 'pending'
    try {
        $conn->exec("ALTER TABLE users MODIFY COLUMN status ENUM('active', 'inactive', 'pending') DEFAULT 'pending'");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    // Create courses table
    $conn->exec("CREATE TABLE IF NOT EXISTS courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructor_id INT,
        max_students INT DEFAULT 50,
        price DECIMAL(10,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'inactive') DEFAULT 'active',
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Add missing columns if they don't exist (using different approach for compatibility)
    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN instructor_id INT AFTER description");
        $conn->exec("ALTER TABLE courses ADD FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN max_students INT DEFAULT 50 AFTER instructor_id");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $conn->exec("ALTER TABLE courses ADD COLUMN price DECIMAL(10,2) DEFAULT 0 AFTER max_students");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    // Create sessions table
    $conn->exec("CREATE TABLE IF NOT EXISTS sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        start_time DATETIME NOT NULL,
        duration INT NOT NULL,
        zoom_meeting_id VARCHAR(255),
        zoom_meeting_password VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('scheduled', 'completed', 'cancelled') DEFAULT 'scheduled',
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create course enrollments table
    $conn->exec("CREATE TABLE IF NOT EXISTS course_enrollments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT,
        student_id INT,
        enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'completed', 'dropped') DEFAULT 'active',
        progress_percentage DECIMAL(5,2) DEFAULT 0,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Add progress_percentage column if it doesn't exist
    $conn->exec("ALTER TABLE course_enrollments ADD COLUMN IF NOT EXISTS progress_percentage DECIMAL(5,2) DEFAULT 0");

    // Create session attendees table
    $conn->exec("CREATE TABLE IF NOT EXISTS session_attendees (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id INT,
        user_id INT,
        join_time TIMESTAMP NULL,
        leave_time TIMESTAMP NULL,
        FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create activity logs table
    $conn->exec("CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(255) NOT NULL,
        details TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create notifications table
    $conn->exec("CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        type ENUM('email', 'whatsapp') NOT NULL,
        content TEXT NOT NULL,
        status ENUM('sent', 'failed') DEFAULT 'sent',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create join_requests table (for general registration requests)
    $conn->exec("CREATE TABLE IF NOT EXISTS join_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed_at TIMESTAMP NULL,
        processed_by INT NULL,
        rejection_reason TEXT NULL,
        INDEX idx_email (email),
        INDEX idx_status (status),
        FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create course_join_requests table (for course-specific join requests)
    $conn->exec("CREATE TABLE IF NOT EXISTS course_join_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        course_id INT NOT NULL,
        message TEXT,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed_at TIMESTAMP NULL,
        processed_by INT NULL,
        notes TEXT NULL,
        INDEX idx_course_id (course_id),
        INDEX idx_student_id (student_id),
        INDEX idx_status (status),
        UNIQUE KEY unique_request (course_id, student_id),
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create assignments table
    $conn->exec("CREATE TABLE IF NOT EXISTS assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        instructor_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        due_date DATETIME NOT NULL,
        max_score INT DEFAULT 100,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create assignment_submissions table
    $conn->exec("CREATE TABLE IF NOT EXISTS assignment_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        assignment_id INT NOT NULL,
        student_id INT NOT NULL,
        submission_text TEXT,
        file_path VARCHAR(500),
        score INT NULL,
        feedback TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        graded_at TIMESTAMP NULL,
        status ENUM('submitted', 'graded', 'late') DEFAULT 'submitted',
        FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create quizzes table
    $conn->exec("CREATE TABLE IF NOT EXISTS quizzes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        instructions TEXT,
        time_limit INT DEFAULT NULL,
        max_attempts INT DEFAULT 1,
        passing_grade DECIMAL(5,2) DEFAULT 60.00,
        total_marks DECIMAL(5,2) DEFAULT 100.00,
        available_from DATETIME DEFAULT NULL,
        available_until DATETIME DEFAULT NULL,
        is_randomized BOOLEAN DEFAULT FALSE,
        show_results BOOLEAN DEFAULT TRUE,
        status ENUM('draft', 'published', 'closed') DEFAULT 'draft',
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_course (course_id),
        INDEX idx_status (status),
        INDEX idx_available (available_from, available_until)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create quiz_questions table
    $conn->exec("CREATE TABLE IF NOT EXISTS quiz_questions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        quiz_id INT NOT NULL,
        question_text TEXT NOT NULL,
        question_type ENUM('multiple_choice', 'true_false', 'short_answer', 'essay') DEFAULT 'multiple_choice',
        points DECIMAL(5,2) DEFAULT 1.00,
        correct_answer TEXT DEFAULT NULL,
        explanation TEXT DEFAULT NULL,
        question_order INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
        INDEX idx_quiz (quiz_id),
        INDEX idx_order (question_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create quiz_question_options table
    $conn->exec("CREATE TABLE IF NOT EXISTS quiz_question_options (
        id INT AUTO_INCREMENT PRIMARY KEY,
        question_id INT NOT NULL,
        option_text TEXT NOT NULL,
        is_correct BOOLEAN DEFAULT FALSE,
        option_order INT DEFAULT 1,
        FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
        INDEX idx_question (question_id),
        INDEX idx_order (option_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create quiz_attempts table
    $conn->exec("CREATE TABLE IF NOT EXISTS quiz_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        quiz_id INT NOT NULL,
        student_id INT NOT NULL,
        attempt_number INT DEFAULT 1,
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        score DECIMAL(5,2) DEFAULT NULL,
        total_marks DECIMAL(5,2) DEFAULT NULL,
        time_taken INT DEFAULT NULL,
        status ENUM('in_progress', 'completed', 'abandoned') DEFAULT 'in_progress',
        FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_quiz (quiz_id),
        INDEX idx_student (student_id),
        INDEX idx_status (status),
        UNIQUE KEY unique_attempt (quiz_id, student_id, attempt_number)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create quiz_answers table
    $conn->exec("CREATE TABLE IF NOT EXISTS quiz_answers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        attempt_id INT NOT NULL,
        question_id INT NOT NULL,
        answer_text TEXT DEFAULT NULL,
        selected_option_id INT DEFAULT NULL,
        is_correct BOOLEAN DEFAULT NULL,
        points_earned DECIMAL(5,2) DEFAULT 0.00,
        answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (attempt_id) REFERENCES quiz_attempts(id) ON DELETE CASCADE,
        FOREIGN KEY (question_id) REFERENCES quiz_questions(id) ON DELETE CASCADE,
        FOREIGN KEY (selected_option_id) REFERENCES quiz_question_options(id) ON DELETE SET NULL,
        UNIQUE KEY unique_answer (attempt_id, question_id),
        INDEX idx_attempt (attempt_id),
        INDEX idx_question (question_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // لا نضيف بيانات تجريبية هنا لتجنب الأخطاء
    // سيتم إضافة البيانات من خلال setup_database.php

} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?>
