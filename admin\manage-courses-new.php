<?php
session_start();
require_once 'includes/simple_db.php';

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'إدارة الكورسات';
$breadcrumbs = [
    ['title' => 'إدارة الكورسات']
];

// معالجة إضافة كورس جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_course'])) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $instructor_id = (int)$_POST['instructor_id'];
    $course_type = $_POST['course_type'];
    $price = $course_type === 'paid' ? (float)$_POST['price'] : 0;
    $category_id = (int)$_POST['category_id'];
    $duration_hours = (int)$_POST['duration_hours'];
    $max_students = (int)$_POST['max_students'];
    $status = $_POST['status'];
    
    // معالجة رفع الصورة
    $image_path = null;
    if (isset($_FILES['course_image']) && $_FILES['course_image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/courses/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $file_extension = pathinfo($_FILES['course_image']['name'], PATHINFO_EXTENSION);
        $file_name = 'course_' . time() . '.' . $file_extension;
        $file_path = $upload_dir . $file_name;
        
        if (move_uploaded_file($_FILES['course_image']['tmp_name'], $file_path)) {
            $image_path = 'uploads/courses/' . $file_name;
        }
    }
    
    // إدراج الكورس الجديد
    $course_data = [
        'title' => $title,
        'description' => $description,
        'instructor_id' => $instructor_id,
        'course_type' => $course_type,
        'price' => $price,
        'category_id' => $category_id,
        'duration_hours' => $duration_hours,
        'max_students' => $max_students,
        'status' => $status,
        'image' => $image_path,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $course_id = insertRecord('courses', $course_data);
    
    if ($course_id) {
        $success = 'تم إضافة الكورس بنجاح';
        logUserActivity($_SESSION['user_id'], 'إضافة كورس', "تم إضافة كورس جديد: $title");
    } else {
        $error = 'حدث خطأ أثناء إضافة الكورس';
    }
}

// معاملات التصفية
$status_filter = $_GET['status'] ?? '';
$type_filter = $_GET['type'] ?? '';
$instructor_filter = $_GET['instructor'] ?? '';
$category_filter = $_GET['category'] ?? '';
$search = $_GET['search'] ?? '';

// بناء استعلام جلب الكورسات
$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "c.status = ?";
    $params[] = $status_filter;
}

if ($type_filter) {
    $where_conditions[] = "c.course_type = ?";
    $params[] = $type_filter;
}

if ($instructor_filter) {
    $where_conditions[] = "c.instructor_id = ?";
    $params[] = $instructor_filter;
}

if ($category_filter) {
    $where_conditions[] = "c.category_id = ?";
    $params[] = $category_filter;
}

if ($search) {
    $where_conditions[] = "(c.title LIKE ? OR c.description LIKE ? OR u.name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب الكورسات مع الإحصائيات
$courses_sql = "
    SELECT 
        c.*,
        u.name as instructor_name,
        u.email as instructor_email,
        cat.name as category_name,
        COUNT(DISTINCT ce.id) as student_count,
        COUNT(DISTINCT s.id) as session_count,
        COALESCE(SUM(CASE WHEN ce.payment_status = 'completed' THEN ce.payment_amount END), 0) as total_revenue,
        COALESCE(AVG(cr.rating), 0) as avg_rating,
        COUNT(DISTINCT cr.id) as review_count
    FROM courses c
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN categories cat ON c.category_id = cat.id
    LEFT JOIN course_enrollments ce ON c.id = ce.course_id
    LEFT JOIN sessions s ON c.id = s.course_id
    LEFT JOIN course_reviews cr ON c.id = cr.course_id
    $where_clause
    GROUP BY c.id
    ORDER BY c.created_at DESC
";

$courses = fetchAll($courses_sql, $params);

// جلب قوائم للفلترة
$instructors = fetchAll("SELECT id, name FROM users WHERE role = 'instructor' ORDER BY name");
$categories = fetchAll("SELECT id, name FROM categories ORDER BY name");

// إحصائيات عامة
$total_courses = countRecords('courses');
$active_courses = countRecords('courses', "status = 'active'");
$paid_courses = countRecords('courses', "course_type = 'paid'");
$total_revenue = fetchOne("SELECT COALESCE(SUM(ce.payment_amount), 0) as total FROM course_enrollments ce WHERE ce.payment_status = 'completed'")['total'] ?? 0;

include 'includes/header.php';
?>

<!-- الإحصائيات العامة -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-primary mb-1"><?php echo number_format($total_courses); ?></h3>
                    <p class="text-muted mb-0">إجمالي الكورسات</p>
                    <small class="text-muted">
                        <i class="fas fa-book me-1"></i>
                        جميع الكورسات في النظام
                    </small>
                </div>
                <div class="text-primary">
                    <i class="fas fa-graduation-cap fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-success mb-1"><?php echo number_format($active_courses); ?></h3>
                    <p class="text-muted mb-0">الكورسات النشطة</p>
                    <small class="text-muted">
                        <i class="fas fa-check-circle me-1"></i>
                        متاحة للتسجيل
                    </small>
                </div>
                <div class="text-success">
                    <i class="fas fa-play-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-warning mb-1"><?php echo number_format($paid_courses); ?></h3>
                    <p class="text-muted mb-0">الكورسات المدفوعة</p>
                    <small class="text-muted">
                        <i class="fas fa-dollar-sign me-1"></i>
                        تتطلب دفع رسوم
                    </small>
                </div>
                <div class="text-warning">
                    <i class="fas fa-credit-card fa-2x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="text-info mb-1"><?php echo number_format($total_revenue, 2); ?> ر.س</h3>
                    <p class="text-muted mb-0">إجمالي الإيرادات</p>
                    <small class="text-muted">
                        <i class="fas fa-chart-line me-1"></i>
                        من جميع الكورسات
                    </small>
                </div>
                <div class="text-info">
                    <i class="fas fa-money-bill-wave fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أدوات التحكم والفلترة -->
<div class="card-admin mb-4" data-aos="fade-up">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>
                أدوات التحكم والفلترة
            </h5>
            <div class="d-flex gap-2">
                <a href="add-course.php" class="btn btn-success btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    إضافة كورس جديد
                </a>
                <button class="btn btn-info btn-sm" onclick="exportCourses()">
                    <i class="fas fa-download me-1"></i>
                    تصدير البيانات
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" placeholder="ابحث في الكورسات..." 
                       value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                    <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                    <option value="draft" <?php echo $status_filter === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">النوع</label>
                <select name="type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="free" <?php echo $type_filter === 'free' ? 'selected' : ''; ?>>مجاني</option>
                    <option value="paid" <?php echo $type_filter === 'paid' ? 'selected' : ''; ?>>مدفوع</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">المدرب</label>
                <select name="instructor" class="form-select">
                    <option value="">جميع المدربين</option>
                    <?php foreach ($instructors as $instructor): ?>
                    <option value="<?php echo $instructor['id']; ?>" 
                            <?php echo $instructor_filter == $instructor['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($instructor['name']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">التصنيف</label>
                <select name="category" class="form-select">
                    <option value="">جميع التصنيفات</option>
                    <?php foreach ($categories as $category): ?>
                    <option value="<?php echo $category['id']; ?>" 
                            <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($category['name']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
        
        <?php if ($status_filter || $type_filter || $instructor_filter || $category_filter || $search): ?>
        <div class="mt-3">
            <a href="manage-courses-new.php" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-times me-1"></i>
                إزالة الفلاتر
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- جدول الكورسات -->
<div class="card-admin" data-aos="fade-up">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة الكورسات (<?php echo number_format(count($courses)); ?>)
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm" onclick="resetFilters()">
                    <i class="fas fa-redo me-1"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($courses)): ?>
        <div class="text-center py-5">
            <i class="fas fa-book fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد كورسات</h5>
            <p class="text-muted">لم يتم العثور على كورسات تطابق معايير البحث</p>
            <a href="add-course.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة كورس جديد
            </a>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="coursesTable">
                <thead class="table-light">
                    <tr>
                        <th>الكورس</th>
                        <th>المدرب</th>
                        <th>النوع والسعر</th>
                        <th>الطلاب</th>
                        <th>الجلسات</th>
                        <th>الإيرادات</th>
                        <th>التقييم</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($courses as $course): ?>
                    <tr data-course-id="<?php echo $course['id']; ?>">
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="course-thumbnail me-3">
                                    <?php if (isset($course['image']) && $course['image']): ?>
                                        <img src="../<?php echo htmlspecialchars($course['image']); ?>"
                                             alt="Course Image" class="rounded" width="50" height="50">
                                    <?php else: ?>
                                        <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center"
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-book"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($course['title']); ?></h6>
                                    <small class="text-muted">
                                        <?php echo mb_substr(strip_tags($course['description'] ?? ''), 0, 50); ?>...
                                    </small>
                                    <?php if ($course['category_name']): ?>
                                    <br><span class="badge bg-light text-dark"><?php echo htmlspecialchars($course['category_name']); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm rounded-circle bg-info text-white me-2 d-flex align-items-center justify-content-center">
                                    <?php echo strtoupper(substr($course['instructor_name'] ?? 'N', 0, 1)); ?>
                                </div>
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($course['instructor_name'] ?? 'غير محدد'); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($course['instructor_email'] ?? ''); ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <?php if (($course['course_type'] ?? 'free') === 'free'): ?>
                                <span class="badge bg-success">مجاني</span>
                            <?php else: ?>
                                <span class="badge bg-warning">مدفوع</span>
                                <br><strong><?php echo number_format($course['price'] ?? 0, 2); ?> ر.س</strong>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-primary rounded-pill"><?php echo $course['student_count']; ?></span>
                        </td>
                        <td>
                            <span class="badge bg-info rounded-pill"><?php echo $course['session_count']; ?></span>
                        </td>
                        <td>
                            <strong class="text-success"><?php echo number_format($course['total_revenue'], 2); ?> ر.س</strong>
                        </td>
                        <td>
                            <?php if ($course['avg_rating'] > 0): ?>
                                <div class="d-flex align-items-center">
                                    <span class="me-1"><?php echo number_format($course['avg_rating'], 1); ?></span>
                                    <div class="text-warning">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo $i <= $course['avg_rating'] ? '' : '-o'; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                                <small class="text-muted">(<?php echo $course['review_count']; ?> تقييم)</small>
                            <?php else: ?>
                                <span class="text-muted">لا يوجد تقييم</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $status_badges = [
                                'active' => 'bg-success',
                                'inactive' => 'bg-secondary',
                                'draft' => 'bg-warning'
                            ];
                            $status_names = [
                                'active' => 'نشط',
                                'inactive' => 'غير نشط',
                                'draft' => 'مسودة'
                            ];
                            $status = $course['status'] ?? 'inactive';
                            ?>
                            <span class="badge <?php echo $status_badges[$status] ?? 'bg-secondary'; ?>">
                                <?php echo $status_names[$status] ?? $status; ?>
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo date('Y-m-d', strtotime($course['created_at'])); ?>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="course-details.php?id=<?php echo $course['id']; ?>"
                                   class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="edit-course.php?id=<?php echo $course['id']; ?>"
                                   class="btn btn-sm btn-outline-warning" data-bs-toggle="tooltip" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="manage-sessions.php?course_id=<?php echo $course['id']; ?>"
                                   class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="إدارة الجلسات">
                                    <i class="fas fa-video"></i>
                                </a>
                                <button class="btn btn-sm btn-outline-danger"
                                        onclick="deleteCourse(<?php echo $course['id']; ?>, '<?php echo htmlspecialchars($course['title']); ?>')"
                                        data-bs-toggle="tooltip" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- نموذج إضافة كورس جديد -->
<div class="modal fade" id="addCourseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة كورس جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">عنوان الكورس <span class="text-danger">*</span></label>
                                <input type="text" name="title" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الحالة <span class="text-danger">*</span></label>
                                <select name="status" class="form-select" required>
                                    <option value="draft">مسودة</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">وصف الكورس</label>
                        <textarea name="description" class="form-control" rows="4" placeholder="اكتب وصفاً مفصلاً للكورس..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المدرب <span class="text-danger">*</span></label>
                                <select name="instructor_id" class="form-select" required>
                                    <option value="">اختر المدرب</option>
                                    <?php foreach ($instructors as $instructor): ?>
                                    <option value="<?php echo $instructor['id']; ?>">
                                        <?php echo htmlspecialchars($instructor['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">التصنيف</label>
                                <select name="category_id" class="form-select">
                                    <option value="">اختر التصنيف</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">نوع الكورس <span class="text-danger">*</span></label>
                                <select name="course_type" class="form-select" id="courseType" required>
                                    <option value="free">مجاني</option>
                                    <option value="paid">مدفوع</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">السعر (ر.س)</label>
                                <input type="number" name="price" class="form-control" min="0" step="0.01"
                                       id="coursePrice" disabled>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">مدة الكورس (ساعة)</label>
                                <input type="number" name="duration_hours" class="form-control" min="1" value="10">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الحد الأقصى للطلاب</label>
                                <input type="number" name="max_students" class="form-control" min="1" value="50">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">صورة الكورس</label>
                                <input type="file" name="course_image" class="form-control" accept="image/*">
                                <small class="text-muted">اختياري - يفضل 800x600 بكسل</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="add_course" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        حفظ الكورس
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.course-thumbnail img {
    object-fit: cover;
}

.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 16px;
    font-weight: 600;
}

.table th {
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 6px !important;
    margin: 0 1px;
}

.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card.success {
    border-left: 4px solid #28a745;
}

.stats-card.warning {
    border-left: 4px solid #ffc107;
}

.stats-card.info {
    border-left: 4px solid #17a2b8;
}
</style>

<script>
$(document).ready(function() {
    // تهيئة DataTables مع فحص عدم إعادة التهيئة
    if (!$.fn.DataTable.isDataTable('#coursesTable')) {
        const table = $('#coursesTable').DataTable({
            order: [[8, 'desc']], // ترتيب حسب تاريخ الإنشاء
            pageLength: 25,
            responsive: true,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
            },
            columnDefs: [
                { orderable: false, targets: [9] } // عمود الإجراءات غير قابل للترتيب
            ],
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip'
        });
    }

    // تفعيل/تعطيل حقل السعر حسب نوع الكورس
    $('#courseType').on('change', function() {
        const priceField = $('#coursePrice');
        if (this.value === 'paid') {
            priceField.prop('disabled', false).prop('required', true);
        } else {
            priceField.prop('disabled', true).prop('required', false).val('');
        }
    });

    // تهيئة التلميحات
    $('[data-bs-toggle="tooltip"]').tooltip();
});

// إعادة تعيين الفلاتر
function resetFilters() {
    window.location.href = 'manage-courses-new.php';
}

// تصدير البيانات
function exportCourses() {
    Swal.fire({
        title: 'تصدير بيانات الكورسات',
        html: `
            <div class="d-grid gap-2">
                <button class="btn btn-success" onclick="exportData('excel', 'export-courses.php')">
                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                </button>
                <button class="btn btn-danger" onclick="exportData('pdf', 'export-courses.php')">
                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                </button>
                <button class="btn btn-info" onclick="exportData('csv', 'export-courses.php')">
                    <i class="fas fa-file-csv me-2"></i>تصدير CSV
                </button>
            </div>
        `,
        showConfirmButton: false,
        showCloseButton: true,
        width: 300
    });
}

// حذف كورس
function deleteCourse(courseId, courseTitle) {
    Swal.fire({
        title: 'حذف الكورس',
        html: `
            <p>هل أنت متأكد من حذف الكورس <strong>${courseTitle}</strong>؟</p>
            <div class="alert alert-warning text-start">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> سيتم حذف جميع البيانات المرتبطة بهذا الكورس:
                <ul class="mt-2 mb-0">
                    <li>جميع الجلسات</li>
                    <li>جميع التسجيلات</li>
                    <li>جميع التقييمات</li>
                    <li>جميع المواد التعليمية</li>
                </ul>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#dc3545',
        input: 'checkbox',
        inputPlaceholder: 'أؤكد أنني أريد حذف هذا الكورس نهائياً'
    }).then((result) => {
        if (result.isConfirmed && result.value) {
            // هنا يمكن إضافة كود الحذف عبر AJAX
            Swal.fire('تم الحذف!', 'تم حذف الكورس بنجاح', 'success').then(() => {
                location.reload();
            });
        } else if (result.isConfirmed && !result.value) {
            Swal.fire({
                title: 'مطلوب تأكيد',
                text: 'يجب تأكيد الحذف بوضع علامة في المربع',
                icon: 'info'
            });
        }
    });
}

// دالة مساعدة لتصدير البيانات
function exportData(format, url) {
    window.open(`${url}?format=${format}`, '_blank');
    Swal.close();
}
</script>

<?php
// عرض رسائل النجاح أو الخطأ
if (isset($success)): ?>
<script>
$(document).ready(function() {
    Swal.fire({
        title: 'تم بنجاح!',
        text: '<?php echo $success; ?>',
        icon: 'success',
        confirmButtonText: 'موافق'
    });
});
</script>
<?php endif; ?>

<?php if (isset($error)): ?>
<script>
$(document).ready(function() {
    Swal.fire({
        title: 'خطأ!',
        text: '<?php echo $error; ?>',
        icon: 'error',
        confirmButtonText: 'موافق'
    });
});
</script>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
