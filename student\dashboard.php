<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من صلاحيات الطالب
if (!isLoggedIn() || !isStudent()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'لوحة تحكم الطالب';
$breadcrumbs = [
    ['title' => 'لوحة التحكم']
];

// جلب إحصائيات الطالب
try {
    // إحصائيات الكورسات
    $stmt = $conn->prepare("
        SELECT
            COUNT(DISTINCT e.id) as enrolled_courses,
            COUNT(DISTINCT CASE WHEN e.status = 'completed' THEN e.id END) as completed_courses,
            COUNT(DISTINCT CASE WHEN e.status = 'active' THEN e.id END) as active_courses,
            AVG(e.progress) as avg_progress,
            AVG(CASE WHEN e.grade IS NOT NULL THEN e.grade END) as avg_grade
        FROM course_enrollments e
        WHERE e.student_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // إحصائيات الواجبات (إذا كان الجدول موجود)
    try {
        $stmt = $conn->prepare("
            SELECT
                COUNT(DISTINCT a.id) as total_assignments,
                COUNT(DISTINCT asub.id) as submitted_assignments
            FROM assignments a
            INNER JOIN courses co ON a.course_id = co.id
            INNER JOIN course_enrollments e ON co.id = e.course_id
            LEFT JOIN assignment_submissions asub ON a.id = asub.assignment_id AND asub.student_id = ?
            WHERE e.student_id = ?
        ");
        $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
        $assignment_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats = array_merge($stats, $assignment_stats);
    } catch (PDOException $e) {
        $stats['total_assignments'] = 0;
        $stats['submitted_assignments'] = 0;
    }

    // إحصائيات طلبات الانضمام
    $stmt = $conn->prepare("
        SELECT
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
            COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_requests
        FROM join_requests
        WHERE student_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $request_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats = array_merge($stats, $request_stats);

    // جلب الكورسات الحالية
    $stmt = $conn->prepare("
        SELECT
            c.*,
            COALESCE(u.full_name, u.username) as instructor_name,
            cat.name as category_name,
            e.enrolled_at as enrollment_date,
            e.status as enrollment_status,
            e.progress,
            e.grade,
            (SELECT COUNT(*) FROM sessions s WHERE s.course_id = c.id) as total_sessions,
            (SELECT COUNT(*) FROM course_videos cv WHERE cv.course_id = c.id) as total_videos,
            (SELECT COUNT(*) FROM video_watches vw
             INNER JOIN course_videos cv2 ON vw.video_id = cv2.id
             WHERE vw.user_id = ? AND cv2.course_id = c.id) as watched_videos
        FROM course_enrollments e
        INNER JOIN courses c ON e.course_id = c.id
        INNER JOIN users u ON c.instructor_id = u.id
        LEFT JOIN categories cat ON c.category_id = cat.id
        WHERE e.student_id = ? AND e.status = 'active'
        ORDER BY e.enrolled_at DESC
        LIMIT 6
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
    $current_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب الواجبات القادمة (إذا كان الجدول موجود)
    try {
        $stmt = $conn->prepare("
            SELECT
                a.*,
                c.title as course_title,
                c.id as course_id,
                asub.id as submission_id,
                asub.grade,
                asub.submitted_at
            FROM assignments a
            INNER JOIN courses c ON a.course_id = c.id
            INNER JOIN course_enrollments e ON c.id = e.course_id
            LEFT JOIN assignment_submissions asub ON a.id = asub.assignment_id AND asub.student_id = ?
            WHERE e.student_id = ? AND e.status = 'active'
            AND (a.due_date >= NOW() OR asub.id IS NULL)
            ORDER BY a.due_date ASC
            LIMIT 5
        ");
        $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
        $upcoming_assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $upcoming_assignments = [];
    }

    // جلب الإشعارات الأخيرة (إذا كان الجدول موجود)
    try {
        $stmt = $conn->prepare("
            SELECT * FROM notifications
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $recent_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $recent_notifications = [];
    }

    // جلب الأنشطة الأخيرة
    $recent_activities = [];

    // جلب أنشطة التسجيل في الكورسات
    try {
        $stmt = $conn->prepare("
            SELECT
                'course_enrollment' as type,
                c.title as title,
                c.title as course_title,
                e.enrolled_at as activity_date,
                'تم التسجيل في الكورس' as description
            FROM course_enrollments e
            INNER JOIN courses c ON e.course_id = c.id
            WHERE e.student_id = ?
            ORDER BY e.enrolled_at DESC
            LIMIT 5
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $enrollment_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $recent_activities = array_merge($recent_activities, $enrollment_activities);
    } catch (PDOException $e) {
        // تجاهل الخطأ
    }

    // جلب أنشطة طلبات الانضمام
    try {
        $stmt = $conn->prepare("
            SELECT
                'join_request' as type,
                c.title as title,
                c.title as course_title,
                jr.created_at as activity_date,
                CASE
                    WHEN jr.status = 'pending' THEN 'تم إرسال طلب انضمام'
                    WHEN jr.status = 'approved' THEN 'تم قبول طلب الانضمام'
                    WHEN jr.status = 'rejected' THEN 'تم رفض طلب الانضمام'
                END as description
            FROM join_requests jr
            INNER JOIN courses c ON jr.course_id = c.id
            WHERE jr.student_id = ?
            ORDER BY jr.created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $request_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $recent_activities = array_merge($recent_activities, $request_activities);
    } catch (PDOException $e) {
        // تجاهل الخطأ
    }

    // ترتيب الأنشطة حسب التاريخ
    usort($recent_activities, function($a, $b) {
        return strtotime($b['activity_date']) - strtotime($a['activity_date']);
    });

    // أخذ أحدث 10 أنشطة
    $recent_activities = array_slice($recent_activities, 0, 10);

} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
    $stats = [
        'enrolled_courses' => 0,
        'completed_courses' => 0,
        'active_courses' => 0,
        'total_assignments' => 0,
        'submitted_assignments' => 0,
        'avg_grade' => 0,
        'avg_progress' => 0,
        'total_requests' => 0,
        'pending_requests' => 0,
        'approved_requests' => 0
    ];
    $current_courses = [];
    $upcoming_assignments = [];
    $recent_notifications = [];
    $recent_activities = [];
}

include 'includes/header.php';
?>

<!-- رأس الصفحة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card-student">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">
                            <i class="fas fa-home text-primary me-2"></i>
                            مرحباً بك، <?php echo htmlspecialchars($_SESSION['name']); ?>!
                        </h2>
                        <p class="text-muted mb-0">
                            استمر في رحلتك التعليمية واكتشف المزيد من المعرفة
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="d-flex gap-2 justify-content-md-end">
                            <a href="browse-courses.php" class="btn btn-student-primary">
                                <i class="fas fa-search me-1"></i>تصفح الكورسات
                            </a>
                            <a href="schedule.php" class="btn btn-outline-primary">
                                <i class="fas fa-calendar me-1"></i>الجدول
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- الإحصائيات -->
<div class="row g-4 mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card-student">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-graduation-cap text-primary fs-2"></i>
                    </div>
                </div>
                <h3 class="mb-1"><?php echo $stats['enrolled_courses']; ?></h3>
                <p class="text-muted mb-0">الكورسات المسجلة</p>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card-student">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-success bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-play-circle text-success fs-2"></i>
                    </div>
                </div>
                <h3 class="mb-1"><?php echo $stats['active_courses']; ?></h3>
                <p class="text-muted mb-0">الكورسات النشطة</p>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card-student">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-info bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-check-circle text-info fs-2"></i>
                    </div>
                </div>
                <h3 class="mb-1"><?php echo $stats['completed_courses']; ?></h3>
                <p class="text-muted mb-0">الكورسات المكتملة</p>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card-student">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-chart-line text-warning fs-2"></i>
                    </div>
                </div>
                <h3 class="mb-1"><?php echo number_format($stats['avg_progress'] ?? 0, 1); ?>%</h3>
                <p class="text-muted mb-0">متوسط التقدم</p>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card-student">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-danger bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-clock text-danger fs-2"></i>
                    </div>
                </div>
                <h3 class="mb-1"><?php echo $stats['pending_requests']; ?></h3>
                <p class="text-muted mb-0">طلبات معلقة</p>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card-student">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-secondary bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-tasks text-secondary fs-2"></i>
                    </div>
                </div>
                <h3 class="mb-1"><?php echo $stats['submitted_assignments']; ?>/<?php echo $stats['total_assignments']; ?></h3>
                <p class="text-muted mb-0">الواجبات</p>
            </div>
        </div>
    </div>
</div>

<!-- المحتوى الرئيسي -->
<div class="row">
    <!-- الكورسات الحالية -->
    <div class="col-lg-8">
        <div class="card-student mb-4">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-book-open me-2"></i>
                        كورساتي الحالية
                    </h5>
                    <a href="courses.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($current_courses)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-book text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">لا توجد كورسات مسجلة</h5>
                    <p class="text-muted">ابدأ رحلتك التعليمية بتصفح الكورسات المتاحة</p>
                    <a href="browse-courses.php" class="btn btn-student-primary">
                        <i class="fas fa-search me-1"></i>تصفح الكورسات
                    </a>
                </div>
                <?php else: ?>
                <div class="row g-3">
                    <?php foreach ($current_courses as $course): ?>
                    <div class="col-md-6">
                        <div class="card border h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0"><?php echo htmlspecialchars($course['title']); ?></h6>
                                    <?php if ($course['category_name']): ?>
                                    <span class="badge bg-primary"><?php echo htmlspecialchars($course['category_name']); ?></span>
                                    <?php endif; ?>
                                </div>

                                <p class="text-muted small mb-2">
                                    <i class="fas fa-user me-1"></i>
                                    <?php echo htmlspecialchars($course['instructor_name']); ?>
                                </p>

                                <p class="text-muted small mb-2">
                                    <i class="fas fa-calendar me-1"></i>
                                    تاريخ التسجيل: <?php echo date('Y-m-d', strtotime($course['enrollment_date'])); ?>
                                </p>

                                <?php
                                // استخدام التقدم من قاعدة البيانات أو حساب من الفيديوهات
                                $progress = $course['progress'] ?? 0;
                                if ($progress == 0 && $course['total_videos'] > 0) {
                                    $progress = round(($course['watched_videos'] / $course['total_videos']) * 100);
                                }
                                ?>
                                <div class="progress-student mb-2">
                                    <div class="progress-bar-student" style="width: <?php echo $progress; ?>%"></div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted"><?php echo $progress; ?>% مكتمل</small>
                                    <?php if ($course['grade']): ?>
                                    <small class="text-success">الدرجة: <?php echo $course['grade']; ?>%</small>
                                    <?php endif; ?>
                                </div>

                                <div class="d-flex gap-2">
                                    <a href="course-view.php?id=<?php echo $course['id']; ?>" class="btn btn-sm btn-student-primary flex-grow-1">
                                        <i class="fas fa-play me-1"></i>متابعة
                                    </a>
                                    <a href="course-details.php?id=<?php echo $course['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-info-circle"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- الأنشطة الأخيرة -->
        <div class="card-student">
            <div class="card-header bg-white border-bottom">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    الأنشطة الأخيرة
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recent_activities)): ?>
                <p class="text-muted mb-0">لا توجد أنشطة حديثة</p>
                <?php else: ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($recent_activities as $activity): ?>
                    <div class="list-group-item px-0 border-0">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <?php if ($activity['type'] === 'course_enrollment'): ?>
                                <div class="bg-success bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-graduation-cap text-success"></i>
                                </div>
                                <?php elseif ($activity['type'] === 'join_request'): ?>
                                <div class="bg-warning bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-paper-plane text-warning"></i>
                                </div>
                                <?php elseif ($activity['type'] === 'video_watch'): ?>
                                <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-play text-primary"></i>
                                </div>
                                <?php else: ?>
                                <div class="bg-info bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-check text-info"></i>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?php echo htmlspecialchars($activity['title']); ?></h6>
                                <p class="text-muted small mb-0">
                                    <?php echo htmlspecialchars($activity['description'] ?? $activity['course_title']); ?> •
                                    <?php echo date('Y-m-d H:i', strtotime($activity['activity_date'])); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- طلبات الانضمام المعلقة -->
        <?php if ($stats['pending_requests'] > 0): ?>
        <div class="card-student mb-4">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        طلبات الانضمام المعلقة
                    </h6>
                    <span class="badge bg-warning"><?php echo $stats['pending_requests']; ?></span>
                </div>
            </div>
            <div class="card-body">
                <?php
                // جلب طلبات الانضمام المعلقة
                try {
                    $stmt = $conn->prepare("
                        SELECT jr.*, c.title as course_title, u.username as instructor_name
                        FROM join_requests jr
                        INNER JOIN courses c ON jr.course_id = c.id
                        INNER JOIN users u ON c.instructor_id = u.id
                        WHERE jr.student_id = ? AND jr.status = 'pending'
                        ORDER BY jr.created_at DESC
                        LIMIT 5
                    ");
                    $stmt->execute([$_SESSION['user_id']]);
                    $pending_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    foreach ($pending_requests as $request): ?>
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-1"><?php echo htmlspecialchars($request['course_title']); ?></h6>
                            <p class="text-muted small mb-1">المدرب: <?php echo htmlspecialchars($request['instructor_name']); ?></p>
                            <small class="text-warning">
                                <i class="fas fa-clock me-1"></i>
                                تم الإرسال: <?php echo date('Y-m-d', strtotime($request['created_at'])); ?>
                            </small>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge bg-warning">معلق</span>
                        </div>
                    </div>
                    <?php endforeach;
                } catch (PDOException $e) {
                    echo "<p class='text-muted small mb-0'>خطأ في جلب الطلبات</p>";
                }
                ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- الواجبات القادمة -->
        <div class="card-student mb-4">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>
                        الواجبات القادمة
                    </h6>
                    <a href="assignments.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($upcoming_assignments)): ?>
                <p class="text-muted small mb-0">لا توجد واجبات قادمة</p>
                <?php else: ?>
                <?php foreach ($upcoming_assignments as $assignment): ?>
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div class="flex-grow-1">
                        <h6 class="mb-1"><?php echo htmlspecialchars($assignment['title']); ?></h6>
                        <p class="text-muted small mb-1"><?php echo htmlspecialchars($assignment['course_title']); ?></p>
                        <small class="text-danger">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo date('Y-m-d', strtotime($assignment['due_date'])); ?>
                        </small>
                    </div>
                    <div class="flex-shrink-0">
                        <?php if ($assignment['submission_id']): ?>
                        <span class="badge bg-success">مسلم</span>
                        <?php else: ?>
                        <a href="assignment-view.php?id=<?php echo $assignment['id']; ?>" class="btn btn-sm btn-outline-primary">
                            عرض
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- الإشعارات الأخيرة -->
        <div class="card-student">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        الإشعارات
                    </h6>
                    <a href="notifications.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($recent_notifications)): ?>
                <p class="text-muted small mb-0">لا توجد إشعارات جديدة</p>
                <?php else: ?>
                <?php foreach ($recent_notifications as $notification): ?>
                <div class="d-flex align-items-start mb-3">
                    <div class="flex-shrink-0 me-2">
                        <div class="bg-info bg-opacity-10 rounded-circle p-1">
                            <i class="fas fa-bell text-info small"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <p class="mb-1 small"><?php echo htmlspecialchars($notification['message']); ?></p>
                        <small class="text-muted">
                            <?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?>
                        </small>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    </div>
</div>

<style>
/* تحسينات لوحة التحكم */
.hero-card {
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    position: relative;
    overflow: hidden;
}

.hero-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
    animation: particleFloat 8s ease-in-out infinite;
}

.welcome-content {
    position: relative;
    z-index: 2;
}

.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.hero-icon-container {
    position: relative;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-icon {
    font-size: 6rem;
    color: rgba(255, 255, 255, 0.8);
    position: relative;
    z-index: 2;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-element {
    position: absolute;
    color: rgba(255, 255, 255, 0.4);
    font-size: 1.5rem;
    animation: float 4s ease-in-out infinite;
}

.floating-element:nth-child(1) { animation-delay: 0s; }
.floating-element:nth-child(2) { animation-delay: 1s; }
.floating-element:nth-child(3) { animation-delay: 2s; }

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: var(--border-width-1) solid var(--gray-100);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.stat-icon-container {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-4);
    position: relative;
}

.stat-icon {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-extrabold);
    color: var(--gray-900);
    display: block;
    line-height: 1;
}

.stat-unit {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.stat-label {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
    margin: var(--spacing-2) 0;
}

.stat-description {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--spacing-3);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.course-card-enhanced {
    background: var(--white);
    border-radius: var(--border-radius-2xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    overflow: hidden;
    height: 100%;
    border: var(--border-width-1) solid var(--gray-100);
}

.course-card-enhanced:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.course-image-enhanced {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.course-image-enhanced img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.course-card-enhanced:hover .course-image-enhanced img {
    transform: scale(1.1);
}

.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
    opacity: 0;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-card-enhanced:hover .course-overlay {
    opacity: 1;
}

.play-button {
    width: 60px;
    height: 60px;
    background: var(--white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
    transform: scale(0);
    transition: var(--transition-bounce);
}

.course-card-enhanced:hover .play-button {
    transform: scale(1);
}

.progress-enhanced {
    height: 8px;
    border-radius: var(--border-radius-full);
    background: var(--gray-200);
    overflow: hidden;
}

.progress-bar-enhanced {
    height: 100%;
    background: var(--gradient-success);
    border-radius: var(--border-radius-full);
    transition: var(--transition-normal);
}

.sidebar-card {
    background: var(--white);
    border-radius: var(--border-radius-2xl);
    box-shadow: var(--shadow-sm);
    border: var(--border-width-1) solid var(--gray-100);
    margin-bottom: var(--spacing-6);
}

.sidebar-card .card-header {
    background: var(--gray-50);
    border-bottom: var(--border-width-1) solid var(--gray-200);
    border-radius: var(--border-radius-2xl) var(--border-radius-2xl) 0 0;
    padding: var(--spacing-4) var(--spacing-6);
}

.sidebar-card .card-body {
    padding: var(--spacing-6);
}

.request-item {
    padding: var(--spacing-4);
    border-radius: var(--border-radius-lg);
    background: var(--gray-50);
    margin-bottom: var(--spacing-3);
    border-right: 4px solid var(--warning-color);
}

.request-item:last-child {
    margin-bottom: 0;
}

.account-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3) 0;
    border-bottom: var(--border-width-1) solid var(--gray-100);
}

.account-info-item:last-child {
    border-bottom: none;
}

.account-info-label {
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
}

.account-info-value {
    color: var(--gray-600);
    text-align: left;
}

@media (max-width: 768px) {
    .hero-icon-container {
        height: 120px;
    }

    .hero-icon {
        font-size: 4rem;
    }

    .stat-card {
        margin-bottom: var(--spacing-4);
    }

    .course-image-enhanced {
        height: 150px;
    }
}
</style>

<!-- إضافة AOS و Enhanced JS -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="../assets/js/enhanced.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة AOS
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });

    // تحريك العدادات
    const counters = document.querySelectorAll('.counter');
    const animateCounters = () => {
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += step;
                if (current >= target) {
                    counter.textContent = target;
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 16);
        });
    };

    // تشغيل العداد عند الوصول للقسم
    const statsSection = document.querySelector('.row.g-4');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounters();
                observer.unobserve(entry.target);
            }
        });
    });
    if (statsSection) observer.observe(statsSection);
});
</script>

<?php require_once '../includes/footer.php'; ?>
