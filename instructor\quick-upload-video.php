<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'رفع فيديو سريع';

// معالجة رفع الفيديو
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $course_id = $_POST['course_id'] ?? 0;
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $video_url = trim($_POST['video_url'] ?? '');
    $duration = $_POST['duration'] ?? 10;
    $is_free = isset($_POST['is_free']) ? 1 : 0;
    
    $errors = [];
    
    if (empty($title)) {
        $errors[] = 'عنوان الفيديو مطلوب';
    }
    
    if (empty($course_id)) {
        $errors[] = 'يجب اختيار كورس';
    }
    
    if (empty($video_url)) {
        $errors[] = 'رابط الفيديو مطلوب';
    }
    
    if (empty($errors)) {
        try {
            // التحقق من أن الكورس ينتمي للمدرب
            $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $_SESSION['user_id']]);
            
            if ($stmt->fetch()) {
                // التحقق من وجود جدول course_videos
                $stmt = $conn->query("SHOW TABLES LIKE 'course_videos'");
                if ($stmt->rowCount() == 0) {
                    // إنشاء الجدول إذا لم يكن موجود
                    $conn->exec("CREATE TABLE course_videos (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        course_id INT NOT NULL,
                        chapter_id INT NULL,
                        title VARCHAR(255) NOT NULL,
                        description TEXT,
                        video_url VARCHAR(500) NOT NULL,
                        duration INT DEFAULT 0,
                        is_free TINYINT(1) DEFAULT 0,
                        order_number INT DEFAULT 1,
                        uploaded_by INT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_course_id (course_id),
                        INDEX idx_uploaded_by (uploaded_by)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
                }
                
                // التحقق من وجود عمود order_number
                $stmt = $conn->query("DESCRIBE course_videos");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $has_order = in_array('order_number', $columns);
                
                if ($has_order) {
                    $stmt = $conn->prepare("
                        INSERT INTO course_videos 
                        (course_id, title, description, video_url, duration, is_free, order_number, uploaded_by) 
                        VALUES (?, ?, ?, ?, ?, ?, 1, ?)
                    ");
                    $stmt->execute([$course_id, $title, $description, $video_url, $duration, $is_free, $_SESSION['user_id']]);
                } else {
                    $stmt = $conn->prepare("
                        INSERT INTO course_videos 
                        (course_id, title, description, video_url, duration, is_free, uploaded_by) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$course_id, $title, $description, $video_url, $duration, $is_free, $_SESSION['user_id']]);
                }
                
                $success_message = 'تم رفع الفيديو بنجاح!';
                
                // إعادة توجيه لصفحة الفيديوهات
                header('Location: videos.php?success=1');
                exit;
            } else {
                $error_message = 'الكورس غير موجود أو لا تملك صلاحية للوصول إليه';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ أثناء رفع الفيديو: ' . $e->getMessage();
        }
    }
}

// جلب الكورسات الخاصة بالمدرب
try {
    $stmt = $conn->prepare("
        SELECT id, title 
        FROM courses 
        WHERE instructor_id = ? AND status = 'active'
        ORDER BY title
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $instructor_courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $instructor_courses = [];
}

include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- رأس الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="h3 mb-2">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        رفع فيديو سريع
                    </h2>
                    <p class="text-muted mb-0">رفع فيديو جديد للكورسات بسرعة</p>
                </div>
                <div>
                    <a href="videos.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للفيديوهات
                    </a>
                </div>
            </div>

            <!-- رسائل النجاح والخطأ -->
            <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <!-- نموذج رفع الفيديو -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>
                        معلومات الفيديو
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="course_id" class="form-label">الكورس <span class="text-danger">*</span></label>
                                <select name="course_id" id="course_id" class="form-select" required>
                                    <option value="">اختر الكورس</option>
                                    <?php foreach ($instructor_courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>" 
                                            <?php echo (isset($_POST['course_id']) && $_POST['course_id'] == $course['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="duration" class="form-label">المدة (بالدقائق)</label>
                                <input type="number" name="duration" id="duration" class="form-control" 
                                       min="1" value="<?php echo $_POST['duration'] ?? 10; ?>">
                            </div>
                            <div class="col-12">
                                <label for="title" class="form-label">عنوان الفيديو <span class="text-danger">*</span></label>
                                <input type="text" name="title" id="title" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" required>
                            </div>
                            <div class="col-12">
                                <label for="description" class="form-label">وصف الفيديو</label>
                                <textarea name="description" id="description" class="form-control" rows="3"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                            </div>
                            <div class="col-12">
                                <label for="video_url" class="form-label">رابط الفيديو <span class="text-danger">*</span></label>
                                <input type="url" name="video_url" id="video_url" class="form-control" 
                                       value="<?php echo htmlspecialchars($_POST['video_url'] ?? ''); ?>" required
                                       placeholder="https://www.youtube.com/watch?v=... أو أي رابط فيديو آخر">
                                <div class="form-text">
                                    <i class="fas fa-info-circle text-info me-1"></i>
                                    يمكنك استخدام روابط من YouTube, Vimeo, أو أي منصة أخرى
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" name="is_free" id="is_free" class="form-check-input"
                                           <?php echo (isset($_POST['is_free'])) ? 'checked' : ''; ?>>
                                    <label for="is_free" class="form-check-label">
                                        <i class="fas fa-unlock text-success me-1"></i>
                                        فيديو مجاني (يمكن للجميع مشاهدته)
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="videos.php" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-bolt me-1"></i>رفع الفيديو سريعاً
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
