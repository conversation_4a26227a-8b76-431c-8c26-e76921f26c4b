<?php
require_once 'includes/simple_db.php';
session_start();

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('HTTP/1.0 403 Forbidden');
    exit('غير مسموح');
}

$backup_id = (int)($_GET['id'] ?? 0);

if (!$backup_id) {
    header('HTTP/1.0 400 Bad Request');
    exit('معرف النسخة الاحتياطية مطلوب');
}

try {
    // جلب معلومات النسخة الاحتياطية
    $stmt = $conn->prepare("
        SELECT * FROM backups 
        WHERE id = ? AND status = 'completed'
    ");
    $stmt->execute([$backup_id]);
    $backup = $stmt->fetch();
    
    if (!$backup) {
        header('HTTP/1.0 404 Not Found');
        exit('النسخة الاحتياطية غير موجودة أو غير مكتملة');
    }
    
    $file_path = $backup['file_path'];
    
    // التحقق من وجود الملف
    if (!file_exists($file_path)) {
        header('HTTP/1.0 404 Not Found');
        exit('ملف النسخة الاحتياطية غير موجود');
    }
    
    // تسجيل عملية التحميل
    logUserActivity($_SESSION['user_id'], 'backup_download', "تم تحميل نسخة احتياطية: " . $backup['filename']);
    
    // إعداد headers للتحميل
    $filename = $backup['filename'];
    $file_size = filesize($file_path);
    
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . $file_size);
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // قراءة وإرسال الملف
    $handle = fopen($file_path, 'rb');
    if ($handle) {
        while (!feof($handle)) {
            echo fread($handle, 8192);
            flush();
        }
        fclose($handle);
    } else {
        header('HTTP/1.0 500 Internal Server Error');
        exit('خطأ في قراءة الملف');
    }
    
} catch (PDOException $e) {
    error_log("Error downloading backup: " . $e->getMessage());
    header('HTTP/1.0 500 Internal Server Error');
    exit('حدث خطأ أثناء تحميل النسخة الاحتياطية');
}
?>
