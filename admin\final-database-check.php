<?php
require_once 'includes/simple_db.php';

echo "<h2>فحص نهائي لقاعدة البيانات</h2>";

try {
    // قائمة الجداول المطلوبة
    $required_tables = [
        'users' => 'جدول المستخدمين',
        'courses' => 'جدول الكورسات', 
        'categories' => 'جدول التصنيفات',
        'user_activities' => 'جدول أنشطة المستخدمين',
        'activity_logs' => 'جدول سجل الأنشطة',
        'enrollments' => 'جدول التسجيلات',
        'join_requests' => 'جدول طلبات الانضمام',
        'session_attendees' => 'جدول حضور الجلسات'
    ];
    
    echo "<h3>فحص الجداول:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم الجدول</th><th>الوصف</th><th>الحالة</th><th>عدد السجلات</th></tr>";
    
    foreach ($required_tables as $table => $description) {
        $exists = tableExists($table);
        $count = $exists ? countRecords($table) : 0;
        $status = $exists ? "✓ موجود" : "✗ غير موجود";
        $color = $exists ? "green" : "red";
        
        echo "<tr>";
        echo "<td>$table</td>";
        echo "<td>$description</td>";
        echo "<td style='color: $color;'>$status</td>";
        echo "<td>$count</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // فحص الأعمدة المهمة في جدول users
    echo "<h3>فحص أعمدة جدول users:</h3>";
    $user_columns = [
        'id', 'name', 'email', 'password', 'role', 'status', 'phone', 'bio',
        'specialization', 'experience_years', 'education_level', 'university', 
        'major', 'certifications', 'skills', 'languages', 'hourly_rate', 
        'availability', 'teaching_style', 'preferred_subjects'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>الحالة</th></tr>";
    
    foreach ($user_columns as $column) {
        $exists = columnExists('users', $column);
        $status = $exists ? "✓ موجود" : "✗ غير موجود";
        $color = $exists ? "green" : "red";
        
        echo "<tr>";
        echo "<td>$column</td>";
        echo "<td style='color: $color;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // فحص الأعمدة المهمة في جدول courses
    echo "<h3>فحص أعمدة جدول courses:</h3>";
    $course_columns = [
        'id', 'title', 'description', 'instructor_id', 'category_id', 
        'price', 'duration_hours', 'max_students', 'start_date', 'end_date', 
        'status', 'image', 'created_at', 'updated_at'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>الحالة</th></tr>";
    
    foreach ($course_columns as $column) {
        $exists = columnExists('courses', $column);
        $status = $exists ? "✓ موجود" : "✗ غير موجود";
        $color = $exists ? "green" : "red";
        
        echo "<tr>";
        echo "<td>$column</td>";
        echo "<td style='color: $color;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض المدربين والتصنيفات المتاحة
    echo "<h3>البيانات المتاحة:</h3>";
    
    $instructors = fetchAll("SELECT id, name, email FROM users WHERE role = 'instructor' LIMIT 5");
    echo "<h4>المدربين (أول 5):</h4>";
    if (empty($instructors)) {
        echo "<p style='color: orange;'>لا يوجد مدربين</p>";
    } else {
        echo "<ul>";
        foreach ($instructors as $instructor) {
            echo "<li>ID: {$instructor['id']} - {$instructor['name']} ({$instructor['email']})</li>";
        }
        echo "</ul>";
    }
    
    $categories = fetchAll("SELECT id, name FROM categories LIMIT 5");
    echo "<h4>التصنيفات:</h4>";
    if (empty($categories)) {
        echo "<p style='color: orange;'>لا توجد تصنيفات</p>";
    } else {
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li>ID: {$category['id']} - {$category['name']}</li>";
        }
        echo "</ul>";
    }
    
    $courses = fetchAll("SELECT id, title, instructor_id FROM courses LIMIT 5");
    echo "<h4>الكورسات (أول 5):</h4>";
    if (empty($courses)) {
        echo "<p style='color: orange;'>لا توجد كورسات</p>";
    } else {
        echo "<ul>";
        foreach ($courses as $course) {
            echo "<li>ID: {$course['id']} - {$course['title']} (مدرب: {$course['instructor_id']})</li>";
        }
        echo "</ul>";
    }
    
    echo "<h3 style='color: green;'>✓ فحص قاعدة البيانات مكتمل</h3>";
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='add-instructor.php' style='margin-right: 10px; padding: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>اختبار إضافة مدرب</a>";
    echo "<a href='add-course.php' style='margin-right: 10px; padding: 10px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>اختبار إضافة كورس</a>";
    echo "<a href='dashboard.php' style='padding: 10px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px;'>لوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>خطأ: " . $e->getMessage() . "</h3>";
}
?>
