<?php
require_once '../includes/session_config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once '../includes/activity_logger.php';

// التحقق من صلاحيات المدرب
if (!isLoggedIn() || !isInstructor()) {
    header('Location: ../login.php');
    exit;
}

$course_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$instructor_id = $_SESSION['user_id'];

// جلب بيانات الكورس
try {
    $stmt = $conn->prepare("SELECT * FROM courses WHERE id = ? AND instructor_id = ?");
    $stmt->execute([$course_id, $instructor_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        header('Location: courses.php');
        exit;
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ في جلب بيانات الكورس';
}

// جلب إحصائيات الكورس
$stats = [];
try {
    // عدد الطلاب المسجلين
    $stmt = $conn->prepare("SELECT COUNT(*) FROM course_enrollments WHERE course_id = ? AND status = 'active'");
    $stmt->execute([$course_id]);
    $stats['enrolled_students'] = $stmt->fetchColumn();

    // عدد الجلسات
    $stmt = $conn->prepare("SELECT COUNT(*) FROM sessions WHERE course_id = ?");
    $stmt->execute([$course_id]);
    $stats['total_sessions'] = $stmt->fetchColumn();

    // عدد طلبات الانضمام المعلقة
    $stmt = $conn->prepare("SELECT COUNT(*) FROM course_join_requests WHERE course_id = ? AND status = 'pending'");
    $stmt->execute([$course_id]);
    $stats['pending_requests'] = $stmt->fetchColumn();
    
    // متوسط الحضور
    $stmt = $conn->prepare("
        SELECT AVG(attendance_count) as avg_attendance 
        FROM (
            SELECT COUNT(*) as attendance_count 
            FROM session_attendance sa 
            JOIN sessions s ON sa.session_id = s.id 
            WHERE s.course_id = ? 
            GROUP BY s.id
        ) as attendance_stats
    ");
    $stmt->execute([$course_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['avg_attendance'] = round($result['avg_attendance'] ?? 0, 1);
    
} catch (PDOException $e) {
    // في حالة الخطأ، استخدم قيم افتراضية
    $stats = [
        'enrolled_students' => 0,
        'total_sessions' => 0,
        'pending_requests' => 0,
        'avg_attendance' => 0
    ];
}

// جلب رابط الدعوة للكورس
$invitation = null;
try {
    $stmt = $conn->prepare("SELECT * FROM course_invitations WHERE course_id = ? AND instructor_id = ? AND status = 'active'");
    $stmt->execute([$course_id, $instructor_id]);
    $invitation = $stmt->fetch(PDO::FETCH_ASSOC);

    // إنشاء رابط دعوة إذا لم يكن موجوداً
    if (!$invitation) {
        $invitation_code = bin2hex(random_bytes(16));
        $invitation_link = "http://localhost/Zoom/join-course.php?code=" . $invitation_code;

        $stmt = $conn->prepare("
            INSERT INTO course_invitations (course_id, instructor_id, invitation_code, invitation_link, status, created_at)
            VALUES (?, ?, ?, ?, 'active', NOW())
        ");
        $stmt->execute([$course_id, $instructor_id, $invitation_code, $invitation_link]);

        // جلب الرابط المنشأ حديثاً
        $stmt = $conn->prepare("SELECT * FROM course_invitations WHERE course_id = ? AND instructor_id = ? AND status = 'active'");
        $stmt->execute([$course_id, $instructor_id]);
        $invitation = $stmt->fetch(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    // في حالة الخطأ، لا يوجد رابط دعوة
}

$pageTitle = 'تفاصيل الكورس: ' . $course['title'];
include 'includes/header.php';

?>

<div class="container-fluid py-4">
    <!-- معلومات الكورس الأساسية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo htmlspecialchars($course['title']); ?></h5>
                    <span class="badge bg-light text-dark"><?php echo $course['status']; ?></span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p class="text-muted"><?php echo htmlspecialchars($course['description']); ?></p>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>تاريخ البدء:</strong>
                                    <?php
                                    if (isset($course['start_date']) && $course['start_date']) {
                                        echo date('Y-m-d', strtotime($course['start_date']));
                                    } else {
                                        echo 'غير محدد';
                                    }
                                    ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>تاريخ الانتهاء:</strong>
                                    <?php
                                    if (isset($course['end_date']) && $course['end_date']) {
                                        echo date('Y-m-d', strtotime($course['end_date']));
                                    } else {
                                        echo 'غير محدد';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <?php if (isset($course['image_path']) && $course['image_path']): ?>
                                <img src="../<?php echo htmlspecialchars($course['image_path']); ?>"
                                     class="img-fluid rounded" alt="صورة الكورس">
                            <?php else: ?>
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 150px;">
                                    <i class="fas fa-graduation-cap fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3><?php echo $stats['enrolled_students']; ?></h3>
                    <p class="mb-0">طالب مسجل</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo $stats['total_sessions']; ?></h3>
                    <p class="mb-0">جلسة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3><?php echo $stats['pending_requests']; ?></h3>
                    <p class="mb-0">طلب انضمام</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3><?php echo $stats['avg_attendance']; ?>%</h3>
                    <p class="mb-0">متوسط الحضور</p>
                </div>
            </div>
        </div>
    </div>

    <!-- رابط دعوة الطلاب -->
    <?php if ($invitation): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-link me-2"></i>
                        رابط دعوة الطلاب للكورس
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>كيفية الاستخدام:</strong> أرسل هذا الرابط للطلاب المهتمين بالانضمام للكورس. سيتمكنون من إنشاء حساب وإرسال طلب انضمام.
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label"><strong>رابط الدعوة:</strong></label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="invitationLink"
                                       value="<?php echo htmlspecialchars($invitation['invitation_link']); ?>"
                                       readonly onclick="this.select()">
                                <button class="btn btn-outline-primary" onclick="copyInvitationLink()">
                                    <i class="fas fa-copy"></i> نسخ
                                </button>
                                <button class="btn btn-outline-success" onclick="shareViaWhatsApp()">
                                    <i class="fab fa-whatsapp"></i> واتساب
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label"><strong>إحصائيات الرابط:</strong></label>
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <h5 class="text-primary"><?php echo $invitation['current_uses']; ?></h5>
                                        <small class="text-muted">مرات الاستخدام</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <h5 class="text-success"><?php echo $stats['pending_requests']; ?></h5>
                                        <small class="text-muted">طلبات معلقة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                تم إنشاء الرابط: <?php echo date('Y-m-d H:i', strtotime($invitation['created_at'])); ?>
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-sm btn-warning" onclick="regenerateInvitationLink()">
                                <i class="fas fa-sync-alt"></i> إنشاء رابط جديد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- أزرار الإجراءات السريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="btn-group-vertical d-grid gap-2 d-md-flex">
                        <a href="add-session.php?course_id=<?php echo $course_id; ?>" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة جلسة جديدة
                        </a>
                        <a href="course-students.php?course_id=<?php echo $course_id; ?>" class="btn btn-primary">
                            <i class="fas fa-users"></i> إدارة الطلاب
                        </a>
                        <a href="course-grades.php?course_id=<?php echo $course_id; ?>" class="btn btn-warning">
                            <i class="fas fa-star"></i> إدارة الدرجات
                        </a>
                        <a href="course-join-requests.php?course_id=<?php echo $course_id; ?>" class="btn btn-info">
                            <i class="fas fa-user-plus"></i> طلبات الانضمام (<?php echo $stats['pending_requests']; ?>)
                        </a>
                        <a href="course-sessions.php?course_id=<?php echo $course_id; ?>" class="btn btn-secondary">
                            <i class="fas fa-video"></i> إدارة الجلسات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- علامات التبويب -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="courseDetailsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="students-tab" data-bs-toggle="tab" data-bs-target="#students" type="button" role="tab">
                                الطلاب المسجلين
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="sessions-tab" data-bs-toggle="tab" data-bs-target="#sessions" type="button" role="tab">
                                الجلسات
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="requests-tab" data-bs-toggle="tab" data-bs-target="#requests" type="button" role="tab">
                                طلبات الانضمام
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="grades-tab" data-bs-toggle="tab" data-bs-target="#grades" type="button" role="tab">
                                الدرجات
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="courseDetailsTabsContent">
                        <!-- تبويب الطلاب -->
                        <div class="tab-pane fade show active" id="students" role="tabpanel">
                            <div id="students-content">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب الجلسات -->
                        <div class="tab-pane fade" id="sessions" role="tabpanel">
                            <div id="sessions-content">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب طلبات الانضمام -->
                        <div class="tab-pane fade" id="requests" role="tabpanel">
                            <div id="requests-content">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب الدرجات -->
                        <div class="tab-pane fade" id="grades" role="tabpanel">
                            <div id="grades-content">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const courseId = <?php echo $course_id; ?>;
    
    // تحميل محتوى الطلاب عند تحميل الصفحة
    loadStudents();
    
    // تحميل المحتوى عند تغيير التبويب
    document.getElementById('students-tab').addEventListener('click', loadStudents);
    document.getElementById('sessions-tab').addEventListener('click', loadSessions);
    document.getElementById('requests-tab').addEventListener('click', loadRequests);
    document.getElementById('grades-tab').addEventListener('click', loadGrades);
    
    function loadStudents() {
        fetch(`ajax/load-course-students.php?course_id=${courseId}`)
            .then(response => response.text())
            .then(data => {
                document.getElementById('students-content').innerHTML = data;
            })
            .catch(error => {
                document.getElementById('students-content').innerHTML = 
                    '<div class="alert alert-danger">حدث خطأ في تحميل بيانات الطلاب</div>';
            });
    }
    
    function loadSessions() {
        fetch(`ajax/load-course-sessions.php?course_id=${courseId}`)
            .then(response => response.text())
            .then(data => {
                document.getElementById('sessions-content').innerHTML = data;
            })
            .catch(error => {
                document.getElementById('sessions-content').innerHTML = 
                    '<div class="alert alert-danger">حدث خطأ في تحميل بيانات الجلسات</div>';
            });
    }
    
    function loadRequests() {
        fetch(`ajax/load-join-requests.php?course_id=${courseId}`)
            .then(response => response.text())
            .then(data => {
                document.getElementById('requests-content').innerHTML = data;
            })
            .catch(error => {
                document.getElementById('requests-content').innerHTML = 
                    '<div class="alert alert-danger">حدث خطأ في تحميل طلبات الانضمام</div>';
            });
    }
    
    function loadGrades() {
        fetch(`ajax/load-course-grades.php?course_id=${courseId}`)
            .then(response => response.text())
            .then(data => {
                document.getElementById('grades-content').innerHTML = data;
            })
            .catch(error => {
                document.getElementById('grades-content').innerHTML =
                    '<div class="alert alert-danger">حدث خطأ في تحميل بيانات الدرجات</div>';
            });
    }
});

// وظائف رابط الدعوة
function copyInvitationLink() {
    const linkInput = document.getElementById('invitationLink');
    linkInput.select();
    linkInput.setSelectionRange(0, 99999); // للهواتف المحمولة

    navigator.clipboard.writeText(linkInput.value).then(function() {
        // إظهار رسالة نجاح
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> تم النسخ!';
        button.classList.remove('btn-outline-primary');
        button.classList.add('btn-success');

        setTimeout(function() {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(function() {
        alert('تم نسخ الرابط!');
    });
}

function shareViaWhatsApp() {
    const link = document.getElementById('invitationLink').value;
    const courseTitle = '<?php echo addslashes($course['title']); ?>';
    const message = `مرحباً! أدعوك للانضمام إلى كورس "${courseTitle}" من خلال هذا الرابط: ${link}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function regenerateInvitationLink() {
    if (confirm('هل أنت متأكد من إنشاء رابط دعوة جديد؟ سيصبح الرابط الحالي غير صالح.')) {
        const courseId = <?php echo $course_id; ?>;

        fetch('ajax/regenerate-invitation.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                course_id: courseId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('invitationLink').value = data.new_link;
                alert('تم إنشاء رابط دعوة جديد بنجاح!');
            } else {
                alert('حدث خطأ في إنشاء رابط جديد: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
