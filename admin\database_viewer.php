<?php
session_start();
require_once '../includes/database_manager.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// الحصول على الجدول المطلوب
$table = $_GET['table'] ?? 'users';
$action = $_GET['action'] ?? 'view';
$id = $_GET['id'] ?? null;

// قائمة الجداول المتاحة
$availableTables = [
    'users' => 'المستخدمين',
    'categories' => 'الفئات',
    'courses' => 'الكورسات',
    'course_sections' => 'أقسام الكورسات',
    'course_lessons' => 'دروس الكورسات',
    'course_enrollments' => 'التسجيلات',
    'lesson_progress' => 'تقدم الدروس',
    'quizzes' => 'الاختبارات',
    'quiz_questions' => 'أسئلة الاختبارات',
    'quiz_attempts' => 'محاولات الاختبارات',
    'assignments' => 'الواجبات',
    'assignment_submissions' => 'تسليمات الواجبات',
    'file_uploads' => 'الملفات المرفوعة',
    'payments' => 'المدفوعات',
    'meals' => 'الوجبات',
    'meal_categories' => 'فئات الوجبات',
    'system_settings' => 'إعدادات النظام',
    'activity_logs' => 'سجل الأنشطة'
];

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($action) {
        case 'create':
            $data = $_POST;
            unset($data['action']);
            $result = $db->create($table, $data);
            $message = $result ? 'تم إنشاء السجل بنجاح' : 'فشل في إنشاء السجل';
            break;
            
        case 'update':
            $data = $_POST;
            unset($data['action'], $data['id']);
            $result = $db->update($table, $id, $data);
            $message = $result ? 'تم تحديث السجل بنجاح' : 'فشل في تحديث السجل';
            break;
            
        case 'delete':
            $result = $db->delete($table, $id);
            $message = $result ? 'تم حذف السجل بنجاح' : 'فشل في حذف السجل';
            break;
    }
}

// الحصول على البيانات
$data = [];
$record = null;
$totalRecords = 0;

if ($action === 'edit' && $id) {
    $record = $db->find($table, $id);
}

if ($action === 'view') {
    $page = $_GET['page'] ?? 1;
    $limit = 20;
    $offset = ($page - 1) * $limit;
    
    $data = $db->getAll($table, '*', 'id DESC', $limit);
    $totalRecords = $db->count($table);
}

// الحصول على هيكل الجدول
function getTableStructure($table) {
    global $conn;
    try {
        $stmt = $conn->query("DESCRIBE {$table}");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return [];
    }
}

$tableStructure = getTableStructure($table);
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض قاعدة البيانات - لوحة التحكم</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .table-selector {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .data-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem 0.75rem;
        }
        
        .table td {
            padding: 0.75rem;
            vertical-align: middle;
            border-color: #e9ecef;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .btn-action {
            padding: 0.25rem 0.5rem;
            margin: 0 0.125rem;
            border-radius: 5px;
            font-size: 0.875rem;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 0.75rem 1rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .text-truncate-custom {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .badge-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .pagination {
            justify-content: center;
            margin-top: 2rem;
        }
        
        .page-link {
            border-radius: 10px;
            margin: 0 0.25rem;
            border: 2px solid #e2e8f0;
            color: #667eea;
        }
        
        .page-link:hover, .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-2">
                        <i class="fas fa-database me-3"></i>
                        عارض قاعدة البيانات
                    </h1>
                    <p class="lead mb-0">إدارة شاملة لجميع بيانات النظام مع دعم عمليات CRUD</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="stats-card">
                        <div class="h4 mb-1"><?php echo number_format($totalRecords); ?></div>
                        <small>إجمالي السجلات</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Table Selector -->
        <div class="table-selector">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-3">اختيار الجدول:</h5>
                    <select class="form-select" onchange="changeTable(this.value)">
                        <?php foreach ($availableTables as $tableName => $tableLabel): ?>
                            <option value="<?php echo $tableName; ?>" <?php echo $table === $tableName ? 'selected' : ''; ?>>
                                <?php echo $tableLabel; ?> (<?php echo $tableName; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-primary" onclick="showCreateModal()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة سجل جديد
                    </button>
                    <button class="btn btn-success" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if (isset($message)): ?>
            <div class="alert alert-<?php echo $result ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo $result ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Data Table -->
        <div class="data-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    بيانات جدول: <?php echo $availableTables[$table]; ?>
                </h5>
                <span class="badge bg-primary"><?php echo count($data); ?> سجل</span>
            </div>

            <?php if (!empty($data)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <?php 
                                $firstRow = $data[0];
                                foreach (array_keys($firstRow) as $column): 
                                ?>
                                    <th><?php echo ucfirst(str_replace('_', ' ', $column)); ?></th>
                                <?php endforeach; ?>
                                <th width="150">العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data as $row): ?>
                                <tr>
                                    <?php foreach ($row as $key => $value): ?>
                                        <td>
                                            <?php 
                                            if (is_null($value)) {
                                                echo '<span class="text-muted">NULL</span>';
                                            } elseif (strlen($value) > 50) {
                                                echo '<span class="text-truncate-custom" title="' . htmlspecialchars($value) . '">' . 
                                                     htmlspecialchars(substr($value, 0, 50)) . '...</span>';
                                            } elseif (in_array($key, ['status', 'role', 'level'])) {
                                                $badgeClass = match($value) {
                                                    'active', 'published', 'completed' => 'bg-success',
                                                    'pending', 'draft' => 'bg-warning',
                                                    'inactive', 'suspended', 'failed' => 'bg-danger',
                                                    default => 'bg-secondary'
                                                };
                                                echo '<span class="badge ' . $badgeClass . '">' . htmlspecialchars($value) . '</span>';
                                            } elseif (strpos($key, 'date') !== false || strpos($key, '_at') !== false) {
                                                echo $value ? date('Y-m-d H:i', strtotime($value)) : '-';
                                            } elseif (is_numeric($value) && strpos($key, 'price') !== false) {
                                                echo number_format($value, 2) . ' ريال';
                                            } else {
                                                echo htmlspecialchars($value);
                                            }
                                            ?>
                                        </td>
                                    <?php endforeach; ?>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary btn-action" 
                                                onclick="showEditModal(<?php echo $row['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info btn-action" 
                                                onclick="showViewModal(<?php echo $row['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger btn-action" 
                                                onclick="confirmDelete(<?php echo $row['id']; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalRecords > 20): ?>
                    <nav>
                        <ul class="pagination">
                            <?php
                            $totalPages = ceil($totalRecords / 20);
                            $currentPage = $_GET['page'] ?? 1;
                            
                            for ($i = 1; $i <= $totalPages; $i++):
                            ?>
                                <li class="page-item <?php echo $i == $currentPage ? 'active' : ''; ?>">
                                    <a class="page-link" href="?table=<?php echo $table; ?>&page=<?php echo $i; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>

            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات في هذا الجدول</h5>
                    <p class="text-muted">ابدأ بإضافة سجل جديد</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Create/Edit Modal -->
    <div class="modal fade" id="dataModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">إضافة سجل جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="dataForm" method="POST">
                    <div class="modal-body">
                        <div id="formFields"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function changeTable(tableName) {
            window.location.href = `?table=${tableName}`;
        }
        
        function refreshData() {
            window.location.reload();
        }
        
        function showCreateModal() {
            document.getElementById('modalTitle').textContent = 'إضافة سجل جديد';
            document.getElementById('dataForm').action = `?table=<?php echo $table; ?>&action=create`;
            generateFormFields();
            new bootstrap.Modal(document.getElementById('dataModal')).show();
        }
        
        function showEditModal(id) {
            document.getElementById('modalTitle').textContent = 'تعديل السجل';
            document.getElementById('dataForm').action = `?table=<?php echo $table; ?>&action=update&id=${id}`;
            generateFormFields(id);
            new bootstrap.Modal(document.getElementById('dataModal')).show();
        }
        
        function generateFormFields(recordId = null) {
            const fields = <?php echo json_encode($tableStructure); ?>;
            const data = <?php echo json_encode($data); ?>;
            let html = '';
            
            fields.forEach(field => {
                if (field.Field === 'id' || field.Field.includes('_at')) return;
                
                let value = '';
                if (recordId && data.length > 0) {
                    const record = data.find(r => r.id == recordId);
                    value = record ? record[field.Field] || '' : '';
                }
                
                html += `
                    <div class="mb-3">
                        <label class="form-label">${field.Field}</label>
                        ${generateFieldInput(field, value)}
                        <small class="form-text text-muted">${field.Type} ${field.Null === 'NO' ? '(مطلوب)' : ''}</small>
                    </div>
                `;
            });
            
            document.getElementById('formFields').innerHTML = html;
        }
        
        function generateFieldInput(field, value) {
            const name = field.Field;
            const type = field.Type.toLowerCase();
            const required = field.Null === 'NO' ? 'required' : '';
            
            if (type.includes('text') || type.includes('longtext')) {
                return `<textarea class="form-control" name="${name}" ${required}>${value}</textarea>`;
            } else if (type.includes('enum')) {
                const options = type.match(/enum\((.*)\)/)[1].split(',').map(opt => opt.replace(/'/g, ''));
                let select = `<select class="form-select" name="${name}" ${required}>`;
                select += '<option value="">اختر...</option>';
                options.forEach(opt => {
                    select += `<option value="${opt}" ${value === opt ? 'selected' : ''}>${opt}</option>`;
                });
                select += '</select>';
                return select;
            } else if (type.includes('date') || type.includes('timestamp')) {
                const inputType = type.includes('timestamp') ? 'datetime-local' : 'date';
                return `<input type="${inputType}" class="form-control" name="${name}" value="${value}" ${required}>`;
            } else if (type.includes('decimal') || type.includes('float')) {
                return `<input type="number" step="0.01" class="form-control" name="${name}" value="${value}" ${required}>`;
            } else if (type.includes('int')) {
                return `<input type="number" class="form-control" name="${name}" value="${value}" ${required}>`;
            } else {
                return `<input type="text" class="form-control" name="${name}" value="${value}" ${required}>`;
            }
        }
        
        function confirmDelete(id) {
            if (confirm('هل أنت متأكد من حذف هذا السجل؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                window.location.href = `?table=<?php echo $table; ?>&action=delete&id=${id}`;
            }
        }
    </script>
</body>
</html>
