/**
 * ملف JavaScript الرئيسي المحسن للمنصة
 * Enhanced Main JavaScript File for Platform
 * ==========================================
 */

// تهيئة المتغيرات العامة المحسنة
class PlatformApp {
    constructor() {
        this.currentUser = null;
        this.notifications = [];
        this.isLoading = false;
        this.config = {
            apiUrl: '/api',
            version: '2.0.0',
            debug: window.location.hostname === 'localhost',
            features: {
                notifications: true,
                analytics: true,
                offline: true,
                pwa: true,
                darkMode: true,
                rtl: true
            },
            timeouts: {
                api: 30000,
                notification: 5000,
                toast: 3000
            }
        };
        this.cache = new Map();
        this.eventListeners = new Map();
        this.performance = {
            startTime: performance.now(),
            metrics: {}
        };
        this.init();
    }

    // تهيئة التطبيق الرئيسية
    init() {
        this.log('🚀 بدء تهيئة التطبيق المحسن...');
        
        this.setupErrorHandling();
        this.initializeComponents();
        this.loadUserData();
        this.setupServiceWorker();
        this.initializeAnalytics();
        this.setupPerformanceMonitoring();
        
        this.performance.metrics.initTime = performance.now() - this.performance.startTime;
        this.log(`✅ تم تهيئة التطبيق المحسن بنجاح في ${this.performance.metrics.initTime.toFixed(2)}ms`);
    }

    // إعداد معالجة الأخطاء العامة
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            this.logError('JavaScript Error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.logError('Unhandled Promise Rejection', event.reason);
            event.preventDefault();
        });

        // معالجة أخطاء الشبكة
        window.addEventListener('offline', () => {
            this.showNotification('تم قطع الاتصال بالإنترنت', 'warning');
        });

        window.addEventListener('online', () => {
            this.showNotification('تم استعادة الاتصال بالإنترنت', 'success');
        });
    }

    // تهيئة المكونات الأساسية
    initializeComponents() {
        this.initializeNavigation();
        this.initializeForms();
        this.initializeModals();
        this.initializeTooltips();
        this.initializeCharts();
        this.initializeSearch();
        this.initializeNotifications();
        this.initializeTheme();
        this.initializeAccessibility();
        this.initializeKeyboardShortcuts();
    }

    // تهيئة التنقل المحسن
    initializeNavigation() {
        // تفعيل التنقل السلس
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // تفعيل القائمة الجانبية
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        const sidebar = document.querySelector('.sidebar-responsive');
        const overlay = document.querySelector('.sidebar-overlay');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('active');
                overlay?.classList.toggle('active');
                document.body.style.overflow = sidebar.classList.contains('active') ? 'hidden' : '';
            });

            overlay?.addEventListener('click', () => {
                sidebar.classList.remove('active');
                overlay.classList.remove('active');
                document.body.style.overflow = '';
            });
        }

        // تفعيل التنقل بالكيبورد
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                sidebar?.classList.remove('active');
                overlay?.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }

    // تهيئة النماذج المحسنة
    initializeForms() {
        document.querySelectorAll('form').forEach(form => {
            this.enhanceForm(form);
        });
    }

    // تحسين النموذج
    enhanceForm(form) {
        // إضافة التحقق من صحة البيانات
        form.addEventListener('submit', (e) => {
            if (!this.validateForm(form)) {
                e.preventDefault();
                return false;
            }
            this.handleFormSubmission(form);
        });

        // تحسين حقول الإدخال
        form.querySelectorAll('input, textarea, select').forEach(input => {
            this.enhanceInput(input);
        });
    }

    // تحسين حقل الإدخال
    enhanceInput(input) {
        // إضافة تأثيرات بصرية
        input.addEventListener('focus', () => {
            input.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', () => {
            input.parentElement.classList.remove('focused');
            if (input.value) {
                input.parentElement.classList.add('has-value');
            } else {
                input.parentElement.classList.remove('has-value');
            }
        });

        // تحسين لوحة المفاتيح للجوال
        if (this.isMobile()) {
            if (input.type === 'email') {
                input.setAttribute('inputmode', 'email');
            } else if (input.type === 'tel') {
                input.setAttribute('inputmode', 'tel');
            } else if (input.type === 'number') {
                input.setAttribute('inputmode', 'numeric');
            }
        }

        // إضافة التحقق الفوري
        input.addEventListener('input', () => {
            this.validateField(input);
        });
    }

    // التحقق من صحة النموذج
    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    // التحقق من صحة الحقل
    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';

        // التحقق من الحقول المطلوبة
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'هذا الحقل مطلوب';
        }

        // التحقق من البريد الإلكتروني
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'يرجى إدخال بريد إلكتروني صحيح';
            }
        }

        // التحقق من كلمة المرور
        if (field.type === 'password' && value) {
            if (value.length < 8) {
                isValid = false;
                message = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
            }
        }

        // التحقق من رقم الهاتف
        if (field.type === 'tel' && value) {
            const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                message = 'يرجى إدخال رقم هاتف صحيح';
            }
        }

        // عرض رسالة الخطأ
        if (isValid) {
            this.clearFieldError(field);
        } else {
            this.showFieldError(field, message);
        }

        return isValid;
    }

    // عرض خطأ الحقل
    showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        
        field.parentElement.appendChild(errorDiv);
    }

    // مسح خطأ الحقل
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        
        const errorDiv = field.parentElement.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    // معالجة إرسال النموذج
    handleFormSubmission(form) {
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        
        if (submitButton) {
            const originalText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            
            // استعادة الزر بعد 5 ثوان (في حالة عدم وجود استجابة)
            setTimeout(() => {
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            }, 5000);
        }
    }

    // تهيئة النوافذ المنبثقة
    initializeModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('show.bs.modal', () => {
                document.body.classList.add('modal-open');
            });

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.classList.remove('modal-open');
            });
        });
    }

    // تهيئة التلميحات
    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(tooltipTriggerEl => {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                boundary: 'viewport',
                placement: 'auto'
            });
        });
    }

    // تهيئة الرسوم البيانية
    initializeCharts() {
        // سيتم تنفيذ هذا لاحقاً مع Chart.js
        this.log('📊 تهيئة الرسوم البيانية...');
    }

    // تهيئة البحث المحسن
    initializeSearch() {
        const searchInputs = document.querySelectorAll('.search-input');
        
        searchInputs.forEach(input => {
            let searchTimeout;
            
            input.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                const query = e.target.value.trim();
                
                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        this.performSearch(query);
                    }, 300);
                }
            });
        });
    }

    // تنفيذ البحث
    async performSearch(query) {
        try {
            this.log(`🔍 البحث عن: ${query}`);
            
            const response = await this.apiCall('/search', {
                method: 'POST',
                body: JSON.stringify({ query }),
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.success) {
                this.displaySearchResults(response.results);
            }
        } catch (error) {
            this.logError('Search Error', error);
        }
    }

    // عرض نتائج البحث
    displaySearchResults(results) {
        const resultsContainer = document.querySelector('.search-results');
        if (!resultsContainer) return;
        
        resultsContainer.innerHTML = '';
        
        if (results.length === 0) {
            resultsContainer.innerHTML = '<p class="text-muted">لا توجد نتائج</p>';
            return;
        }
        
        results.forEach(result => {
            const resultElement = this.createSearchResultElement(result);
            resultsContainer.appendChild(resultElement);
        });
    }

    // إنشاء عنصر نتيجة البحث
    createSearchResultElement(result) {
        const div = document.createElement('div');
        div.className = 'search-result-item';
        div.innerHTML = `
            <h6><a href="${result.url}">${result.title}</a></h6>
            <p class="text-muted">${result.description}</p>
            <small class="text-info">${result.type}</small>
        `;
        return div;
    }

    // تهيئة الإشعارات
    initializeNotifications() {
        this.createNotificationContainer();
        this.loadNotifications();
    }

    // إنشاء حاوية الإشعارات
    createNotificationContainer() {
        if (!document.querySelector('.notifications-container')) {
            const container = document.createElement('div');
            container.className = 'notifications-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
    }

    // عرض إشعار
    showNotification(message, type = 'info', duration = null) {
        const container = document.querySelector('.notifications-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show notification-item`;
        notification.style.cssText = `
            margin-bottom: 10px;
            animation: slideInRight 0.3s ease-out;
        `;
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        container.appendChild(notification);

        // إزالة تلقائية
        const timeout = duration || this.config.timeouts.notification;
        setTimeout(() => {
            if (notification.parentElement) {
                notification.classList.add('fade');
                setTimeout(() => notification.remove(), 300);
            }
        }, timeout);
    }

    // تحميل الإشعارات
    async loadNotifications() {
        try {
            const response = await this.apiCall('/notifications');
            if (response.success) {
                this.notifications = response.notifications;
                this.updateNotificationBadge();
            }
        } catch (error) {
            this.logError('Load Notifications Error', error);
        }
    }

    // تحديث شارة الإشعارات
    updateNotificationBadge() {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            const unreadCount = this.notifications.filter(n => !n.read).length;
            badge.textContent = unreadCount;
            badge.style.display = unreadCount > 0 ? 'inline' : 'none';
        }
    }

    // تهيئة السمة (فاتح/مظلم)
    initializeTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.setTheme(savedTheme);

        const themeToggle = document.querySelector('.theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.body.getAttribute('data-theme') || 'light';
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                this.setTheme(newTheme);
            });
        }
    }

    // تعيين السمة
    setTheme(theme) {
        document.body.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        const themeIcon = document.querySelector('.theme-toggle i');
        if (themeIcon) {
            themeIcon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // تهيئة إمكانية الوصول
    initializeAccessibility() {
        // إضافة دعم التنقل بالكيبورد
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });

        // تحسين التباين للمستخدمين ضعاف البصر
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            document.body.classList.add('high-contrast');
        }

        // تقليل الحركة للمستخدمين الذين يفضلون ذلك
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.body.classList.add('reduced-motion');
        }
    }

    // تهيئة اختصارات لوحة المفاتيح
    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K للبحث
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('.search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Escape لإغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    bootstrap.Modal.getInstance(openModal)?.hide();
                }
            }
        });
    }

    // دوال مساعدة
    isMobile() {
        return window.innerWidth <= 768;
    }

    isTablet() {
        return window.innerWidth > 768 && window.innerWidth <= 1024;
    }

    isDesktop() {
        return window.innerWidth > 1024;
    }

    log(message) {
        if (this.config.debug) {
            console.log(`[PlatformApp] ${message}`);
        }
    }

    logError(title, error) {
        console.error(`[PlatformApp Error] ${title}:`, error);
        
        // إرسال الخطأ للخادم (اختياري)
        if (this.config.features.analytics) {
            this.sendErrorToServer(title, error);
        }
    }

    // إرسال الخطأ للخادم
    async sendErrorToServer(title, error) {
        try {
            await this.apiCall('/errors', {
                method: 'POST',
                body: JSON.stringify({
                    title,
                    error: error.toString(),
                    stack: error.stack,
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    timestamp: new Date().toISOString()
                }),
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        } catch (e) {
            console.error('Failed to send error to server:', e);
        }
    }

    // استدعاء API محسن
    async apiCall(endpoint, options = {}) {
        const url = this.config.apiUrl + endpoint;
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: this.config.timeouts.api
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), finalOptions.timeout);

            const response = await fetch(url, {
                ...finalOptions,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Request timeout');
            }
            throw error;
        }
    }

    // تحميل بيانات المستخدم
    async loadUserData() {
        try {
            const response = await this.apiCall('/user/profile');
            if (response.success) {
                this.currentUser = response.user;
                this.updateUserInterface();
            }
        } catch (error) {
            this.logError('Load User Data Error', error);
        }
    }

    // تحديث واجهة المستخدم
    updateUserInterface() {
        if (!this.currentUser) return;

        // تحديث اسم المستخدم
        const userNameElements = document.querySelectorAll('.user-name');
        userNameElements.forEach(element => {
            element.textContent = this.currentUser.name;
        });

        // تحديث صورة المستخدم
        const userAvatarElements = document.querySelectorAll('.user-avatar');
        userAvatarElements.forEach(element => {
            if (this.currentUser.avatar) {
                element.src = this.currentUser.avatar;
            }
        });
    }

    // إعداد Service Worker
    async setupServiceWorker() {
        if ('serviceWorker' in navigator && this.config.features.pwa) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                this.log('Service Worker registered successfully');
                
                // التحقق من التحديثات
                registration.addEventListener('updatefound', () => {
                    this.showNotification('تحديث جديد متاح للتطبيق', 'info');
                });
            } catch (error) {
                this.logError('Service Worker Registration Error', error);
            }
        }
    }

    // تهيئة التحليلات
    initializeAnalytics() {
        if (!this.config.features.analytics) return;

        // تتبع مشاهدات الصفحة
        this.trackPageView();

        // تتبع النقرات
        document.addEventListener('click', (e) => {
            if (e.target.matches('a, button')) {
                this.trackEvent('click', {
                    element: e.target.tagName,
                    text: e.target.textContent.trim(),
                    href: e.target.href || null
                });
            }
        });
    }

    // تتبع مشاهدة الصفحة
    trackPageView() {
        this.trackEvent('page_view', {
            page: window.location.pathname,
            title: document.title,
            referrer: document.referrer
        });
    }

    // تتبع حدث
    trackEvent(eventName, data = {}) {
        if (this.config.debug) {
            console.log(`[Analytics] ${eventName}:`, data);
        }

        // إرسال للخادم (اختياري)
        this.apiCall('/analytics/track', {
            method: 'POST',
            body: JSON.stringify({
                event: eventName,
                data,
                timestamp: new Date().toISOString(),
                user_id: this.currentUser?.id || null
            })
        }).catch(error => {
            this.logError('Analytics Track Error', error);
        });
    }

    // إعداد مراقبة الأداء
    setupPerformanceMonitoring() {
        if ('PerformanceObserver' in window) {
            // مراقبة أداء التحميل
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    this.performance.metrics[entry.name] = entry.duration;
                });
            });

            observer.observe({ entryTypes: ['measure', 'navigation'] });
        }

        // مراقبة استخدام الذاكرة
        if ('memory' in performance) {
            setInterval(() => {
                this.performance.metrics.memory = {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                };
            }, 30000);
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.platformApp = new PlatformApp();
});

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PlatformApp;
}
