-- ===================================================================
-- قاعدة بيانات منصة التعلم الإلكتروني المحسنة والشاملة
-- Enhanced Zoom Learning System Database - Complete CRUD Support
-- يدعم جميع عمليات CRUD والعمليات المتقدمة
-- ===================================================================

-- إعدادات أولية
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- إنشاء قاعدة البيانات
DROP DATABASE IF EXISTS zoom_learning_system;
CREATE DATABASE zoom_learning_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE zoom_learning_system;

-- ===================================================================
-- 1. جدول المستخدمين الموحد (Users)
-- ===================================================================
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NULL,
    date_of_birth DATE NULL,
    gender ENUM('male', 'female', 'other') NULL,
    
    -- معلومات الملف الشخصي
    profile_picture VARCHAR(255) NULL,
    bio TEXT NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    country VARCHAR(50) DEFAULT 'Saudi Arabia',
    
    -- معلومات الدور والحالة
    role ENUM('admin', 'instructor', 'student') NOT NULL DEFAULT 'student',
    status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'pending',
    
    -- معلومات التحقق
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255) NULL,
    phone_verified BOOLEAN DEFAULT FALSE,
    
    -- معلومات إضافية للمدربين
    specialization VARCHAR(100) NULL,
    experience_years INT DEFAULT 0,
    education TEXT NULL,
    certifications TEXT NULL,
    hourly_rate DECIMAL(10,2) DEFAULT 0,
    
    -- معلومات إضافية للطلاب
    student_id VARCHAR(20) NULL UNIQUE,
    enrollment_date DATE NULL,
    graduation_date DATE NULL,
    
    -- إعدادات الحساب
    language VARCHAR(10) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
    notification_preferences JSON NULL,
    privacy_settings JSON NULL,
    
    -- معلومات الأمان
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    password_reset_token VARCHAR(255) NULL,
    password_reset_expires TIMESTAMP NULL,
    
    -- معلومات التتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,
    updated_by INT NULL,
    
    -- فهارس محسنة
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_specialization (specialization),
    INDEX idx_student_id (student_id),
    FULLTEXT idx_search (name, bio, specialization)
) ENGINE=InnoDB;

-- ===================================================================
-- 2. جدول الفئات (Categories)
-- ===================================================================
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT NULL,
    icon VARCHAR(50) NULL,
    color VARCHAR(7) DEFAULT '#667eea',
    image VARCHAR(255) NULL,
    
    -- هيكل هرمي
    parent_id INT NULL,
    level INT DEFAULT 0,
    sort_order INT DEFAULT 0,
    
    -- SEO
    meta_title VARCHAR(200) NULL,
    meta_description TEXT NULL,
    meta_keywords TEXT NULL,
    
    -- حالة
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    
    -- إحصائيات
    courses_count INT DEFAULT 0,
    students_count INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_parent (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_active (is_active),
    INDEX idx_featured (is_featured),
    INDEX idx_sort (sort_order),
    FULLTEXT idx_search (name, description)
) ENGINE=InnoDB;

-- ===================================================================
-- 3. جدول الكورسات المحسن (Courses)
-- ===================================================================
CREATE TABLE courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL UNIQUE,
    short_description TEXT NULL,
    description LONGTEXT NULL,
    
    -- معلومات أساسية
    category_id INT NOT NULL,
    instructor_id INT NOT NULL,
    level ENUM('beginner', 'intermediate', 'advanced', 'expert') DEFAULT 'beginner',
    language VARCHAR(10) DEFAULT 'ar',
    
    -- محتوى الكورس
    thumbnail VARCHAR(255) NULL,
    intro_video VARCHAR(255) NULL,
    preview_video VARCHAR(255) NULL,
    
    -- معلومات التسعير
    price DECIMAL(10,2) DEFAULT 0,
    original_price DECIMAL(10,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'SAR',
    is_free BOOLEAN DEFAULT TRUE,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    discount_expires_at TIMESTAMP NULL,
    
    -- معلومات الكورس
    duration_hours DECIMAL(5,2) DEFAULT 0,
    total_lessons INT DEFAULT 0,
    total_quizzes INT DEFAULT 0,
    total_assignments INT DEFAULT 0,
    
    -- متطلبات
    prerequisites TEXT NULL,
    requirements TEXT NULL,
    target_audience TEXT NULL,
    learning_objectives JSON NULL,
    
    -- إعدادات الكورس
    max_students INT DEFAULT 0, -- 0 = unlimited
    enrollment_start DATE NULL,
    enrollment_end DATE NULL,
    course_start DATE NULL,
    course_end DATE NULL,
    
    -- حالة ومراجعة
    status ENUM('draft', 'pending', 'published', 'archived', 'suspended') DEFAULT 'draft',
    is_featured BOOLEAN DEFAULT FALSE,
    is_certified BOOLEAN DEFAULT FALSE,
    
    -- تقييم وإحصائيات
    rating DECIMAL(3,2) DEFAULT 0,
    total_reviews INT DEFAULT 0,
    total_students INT DEFAULT 0,
    total_graduates INT DEFAULT 0,
    total_revenue DECIMAL(12,2) DEFAULT 0,
    
    -- SEO
    meta_title VARCHAR(200) NULL,
    meta_description TEXT NULL,
    meta_keywords TEXT NULL,
    
    -- تتبع
    views_count INT DEFAULT 0,
    last_updated TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,
    updated_by INT NULL,
    
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_category (category_id),
    INDEX idx_instructor (instructor_id),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_level (level),
    INDEX idx_price (price),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_search (title, short_description, description)
) ENGINE=InnoDB;

-- ===================================================================
-- 4. جدول أقسام الكورس (Course Sections)
-- ===================================================================
CREATE TABLE course_sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NULL,
    sort_order INT DEFAULT 0,
    
    -- إعدادات القسم
    is_free BOOLEAN DEFAULT FALSE,
    is_locked BOOLEAN DEFAULT FALSE,
    unlock_date TIMESTAMP NULL,
    
    -- إحصائيات
    total_lessons INT DEFAULT 0,
    total_duration DECIMAL(5,2) DEFAULT 0,
    
    status ENUM('active', 'inactive') DEFAULT 'active',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    
    INDEX idx_course (course_id),
    INDEX idx_sort (sort_order),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- ===================================================================
-- 5. جدول دروس الكورس المحسن (Course Lessons)
-- ===================================================================
CREATE TABLE course_lessons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    section_id INT NULL,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL,
    description TEXT NULL,
    content LONGTEXT NULL,
    
    -- نوع الدرس
    lesson_type ENUM('video', 'text', 'quiz', 'assignment', 'live', 'document') DEFAULT 'video',
    
    -- محتوى الفيديو
    video_url VARCHAR(500) NULL,
    video_file VARCHAR(255) NULL,
    video_duration DECIMAL(8,2) DEFAULT 0, -- بالثواني
    video_size BIGINT DEFAULT 0, -- بالبايت
    video_quality VARCHAR(10) DEFAULT '720p',
    video_thumbnail VARCHAR(255) NULL,
    
    -- محتوى إضافي
    attachments JSON NULL,
    resources JSON NULL,
    notes TEXT NULL,
    
    -- إعدادات الدرس
    is_free BOOLEAN DEFAULT FALSE,
    is_downloadable BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    
    -- متطلبات
    prerequisites JSON NULL,
    
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    
    -- إحصائيات
    views_count INT DEFAULT 0,
    completion_rate DECIMAL(5,2) DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (section_id) REFERENCES course_sections(id) ON DELETE SET NULL,
    
    INDEX idx_course (course_id),
    INDEX idx_section (section_id),
    INDEX idx_type (lesson_type),
    INDEX idx_status (status),
    INDEX idx_sort (sort_order),
    FULLTEXT idx_search (title, description, content)
) ENGINE=InnoDB;

-- ===================================================================
-- 6. جدول تسجيلات الكورس المحسن (Course Enrollments)
-- ===================================================================
CREATE TABLE course_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    student_id INT NOT NULL,
    
    -- معلومات التسجيل
    enrollment_type ENUM('free', 'paid', 'scholarship', 'invitation') DEFAULT 'free',
    enrollment_source VARCHAR(50) NULL, -- website, mobile, admin, etc.
    
    -- حالة التسجيل
    status ENUM('pending', 'active', 'completed', 'suspended', 'cancelled', 'expired') DEFAULT 'pending',
    
    -- تواريخ مهمة
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    last_accessed TIMESTAMP NULL,
    
    -- تقدم الطالب
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    lessons_completed INT DEFAULT 0,
    quizzes_completed INT DEFAULT 0,
    assignments_completed INT DEFAULT 0,
    total_study_time DECIMAL(10,2) DEFAULT 0, -- بالساعات
    
    -- درجات وتقييم
    current_grade DECIMAL(5,2) DEFAULT 0,
    final_grade DECIMAL(5,2) NULL,
    certificate_issued BOOLEAN DEFAULT FALSE,
    certificate_number VARCHAR(50) NULL,
    certificate_issued_at TIMESTAMP NULL,
    
    -- معلومات الدفع
    payment_id INT NULL,
    amount_paid DECIMAL(10,2) DEFAULT 0,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') NULL,
    
    -- ملاحظات
    notes TEXT NULL,
    instructor_notes TEXT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_enrollment (course_id, student_id),
    INDEX idx_course (course_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status),
    INDEX idx_enrolled_at (enrolled_at),
    INDEX idx_progress (progress_percentage)
) ENGINE=InnoDB;

-- ===================================================================
-- 7. جدول تقدم الدروس (Lesson Progress)
-- ===================================================================
CREATE TABLE lesson_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    enrollment_id INT NOT NULL,
    lesson_id INT NOT NULL,
    student_id INT NOT NULL,
    
    -- حالة التقدم
    status ENUM('not_started', 'in_progress', 'completed', 'skipped') DEFAULT 'not_started',
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- أوقات مهمة
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    last_accessed TIMESTAMP NULL,
    
    -- تفاصيل المشاهدة (للفيديو)
    watch_time DECIMAL(8,2) DEFAULT 0, -- بالثواني
    total_watch_time DECIMAL(8,2) DEFAULT 0,
    watch_count INT DEFAULT 0,
    last_position DECIMAL(8,2) DEFAULT 0, -- آخر موضع في الفيديو
    
    -- ملاحظات الطالب
    notes TEXT NULL,
    bookmarks JSON NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (enrollment_id) REFERENCES course_enrollments(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_progress (enrollment_id, lesson_id),
    INDEX idx_enrollment (enrollment_id),
    INDEX idx_lesson (lesson_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- ===================================================================
-- 8. جدول الاختبارات المحسن (Quizzes)
-- ===================================================================
CREATE TABLE quizzes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    lesson_id INT NULL,
    section_id INT NULL,

    title VARCHAR(200) NOT NULL,
    description TEXT NULL,
    instructions TEXT NULL,

    -- نوع الاختبار
    quiz_type ENUM('practice', 'graded', 'final', 'certification') DEFAULT 'practice',

    -- إعدادات الاختبار
    time_limit INT NULL, -- بالدقائق
    attempts_allowed INT DEFAULT 1,
    passing_score DECIMAL(5,2) DEFAULT 70,

    -- إعدادات العرض
    randomize_questions BOOLEAN DEFAULT FALSE,
    randomize_answers BOOLEAN DEFAULT FALSE,
    show_results BOOLEAN DEFAULT TRUE,
    show_correct_answers BOOLEAN DEFAULT TRUE,
    show_score BOOLEAN DEFAULT TRUE,

    -- إعدادات التوقيت
    available_from TIMESTAMP NULL,
    available_until TIMESTAMP NULL,

    -- وزن الدرجات
    total_points DECIMAL(8,2) DEFAULT 0,
    weight_percentage DECIMAL(5,2) DEFAULT 0,

    -- حالة
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',

    -- إحصائيات
    total_questions INT DEFAULT 0,
    average_score DECIMAL(5,2) DEFAULT 0,
    completion_rate DECIMAL(5,2) DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NOT NULL,

    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE SET NULL,
    FOREIGN KEY (section_id) REFERENCES course_sections(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_course (course_id),
    INDEX idx_lesson (lesson_id),
    INDEX idx_section (section_id),
    INDEX idx_type (quiz_type),
    INDEX idx_status (status),
    FULLTEXT idx_search (title, description)
) ENGINE=InnoDB;

-- ===================================================================
-- 9. جدول أسئلة الاختبارات (Quiz Questions)
-- ===================================================================
CREATE TABLE quiz_questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quiz_id INT NOT NULL,

    question_text TEXT NOT NULL,
    question_type ENUM('multiple_choice', 'true_false', 'short_answer', 'essay', 'matching', 'fill_blank') DEFAULT 'multiple_choice',

    -- خيارات الإجابة (JSON)
    options JSON NULL,
    correct_answer TEXT NULL,
    correct_answers JSON NULL, -- للأسئلة متعددة الإجابات

    -- إعدادات السؤال
    points DECIMAL(5,2) DEFAULT 1,
    difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',
    sort_order INT DEFAULT 0,

    -- شرح الإجابة
    explanation TEXT NULL,
    reference_material TEXT NULL,

    -- ملفات مرفقة
    image VARCHAR(255) NULL,
    audio VARCHAR(255) NULL,
    video VARCHAR(255) NULL,

    -- إحصائيات
    times_answered INT DEFAULT 0,
    correct_percentage DECIMAL(5,2) DEFAULT 0,

    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,

    INDEX idx_quiz (quiz_id),
    INDEX idx_type (question_type),
    INDEX idx_difficulty (difficulty),
    INDEX idx_sort (sort_order),
    INDEX idx_active (is_active)
) ENGINE=InnoDB;

-- ===================================================================
-- 10. جدول محاولات الاختبارات (Quiz Attempts)
-- ===================================================================
CREATE TABLE quiz_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quiz_id INT NOT NULL,
    student_id INT NOT NULL,
    enrollment_id INT NOT NULL,

    attempt_number INT NOT NULL,

    -- حالة المحاولة
    status ENUM('in_progress', 'submitted', 'graded', 'expired') DEFAULT 'in_progress',

    -- إجابات الطالب
    answers JSON NULL,

    -- نتائج
    score DECIMAL(8,2) DEFAULT 0,
    total_points DECIMAL(8,2) DEFAULT 0,
    percentage DECIMAL(5,2) DEFAULT 0,
    is_passed BOOLEAN DEFAULT FALSE,

    -- أوقات
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP NULL,
    graded_at TIMESTAMP NULL,
    time_taken INT NULL, -- بالثواني

    -- ملاحظات
    feedback TEXT NULL,
    instructor_notes TEXT NULL,

    -- معلومات إضافية
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,

    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (enrollment_id) REFERENCES course_enrollments(id) ON DELETE CASCADE,

    INDEX idx_quiz (quiz_id),
    INDEX idx_student (student_id),
    INDEX idx_enrollment (enrollment_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at)
) ENGINE=InnoDB;

-- ===================================================================
-- 11. جدول الواجبات المحسن (Assignments)
-- ===================================================================
CREATE TABLE assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    lesson_id INT NULL,
    section_id INT NULL,

    title VARCHAR(200) NOT NULL,
    description TEXT NULL,
    instructions LONGTEXT NULL,

    -- نوع الواجب
    assignment_type ENUM('individual', 'group', 'project', 'presentation') DEFAULT 'individual',

    -- إعدادات التسليم
    submission_format ENUM('text', 'file', 'both', 'link') DEFAULT 'both',
    allowed_file_types JSON NULL,
    max_file_size BIGINT DEFAULT 10485760, -- 10MB
    max_files INT DEFAULT 5,

    -- درجات
    total_points DECIMAL(8,2) DEFAULT 100,
    weight_percentage DECIMAL(5,2) DEFAULT 0,

    -- مواعيد
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    due_date TIMESTAMP NULL,
    late_submission_allowed BOOLEAN DEFAULT TRUE,
    late_penalty_percentage DECIMAL(5,2) DEFAULT 10,

    -- ملفات مرفقة من المدرب
    attachment_files JSON NULL,
    reference_materials JSON NULL,

    -- إعدادات متقدمة
    group_size_min INT DEFAULT 1,
    group_size_max INT DEFAULT 1,
    peer_review_enabled BOOLEAN DEFAULT FALSE,
    auto_grading_enabled BOOLEAN DEFAULT FALSE,

    -- حالة
    status ENUM('draft', 'published', 'closed', 'archived') DEFAULT 'draft',

    -- إحصائيات
    total_submissions INT DEFAULT 0,
    graded_submissions INT DEFAULT 0,
    average_score DECIMAL(5,2) DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NOT NULL,

    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE SET NULL,
    FOREIGN KEY (section_id) REFERENCES course_sections(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_course (course_id),
    INDEX idx_lesson (lesson_id),
    INDEX idx_section (section_id),
    INDEX idx_type (assignment_type),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date),
    FULLTEXT idx_search (title, description)
) ENGINE=InnoDB;

-- ===================================================================
-- 12. جدول تسليمات الواجبات (Assignment Submissions)
-- ===================================================================
CREATE TABLE assignment_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    student_id INT NOT NULL,
    enrollment_id INT NOT NULL,

    -- محتوى التسليم
    submission_text LONGTEXT NULL,
    submitted_files JSON NULL,
    submission_links JSON NULL,

    -- حالة التسليم
    status ENUM('draft', 'submitted', 'graded', 'returned', 'resubmitted') DEFAULT 'draft',
    submission_number INT DEFAULT 1,

    -- معلومات التسليم
    submitted_at TIMESTAMP NULL,
    is_late BOOLEAN DEFAULT FALSE,
    late_hours INT DEFAULT 0,

    -- تقييم
    grade DECIMAL(8,2) NULL,
    points_earned DECIMAL(8,2) NULL,
    feedback TEXT NULL,
    graded_by INT NULL,
    graded_at TIMESTAMP NULL,

    -- مراجعة الأقران
    peer_reviews JSON NULL,
    peer_review_score DECIMAL(5,2) NULL,

    -- ملاحظات
    student_notes TEXT NULL,
    instructor_notes TEXT NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (enrollment_id) REFERENCES course_enrollments(id) ON DELETE CASCADE,
    FOREIGN KEY (graded_by) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_assignment (assignment_id),
    INDEX idx_student (student_id),
    INDEX idx_enrollment (enrollment_id),
    INDEX idx_status (status),
    INDEX idx_submitted_at (submitted_at)
) ENGINE=InnoDB;

-- ===================================================================
-- 13. جدول نظام الملفات المحسن (File Uploads)
-- ===================================================================
CREATE TABLE file_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,

    -- معلومات الملف
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_url VARCHAR(500) NULL,

    -- خصائص الملف
    file_type VARCHAR(100) NOT NULL,
    file_extension VARCHAR(10) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,

    -- منع التكرار
    file_hash VARCHAR(64) NOT NULL,

    -- تصنيف الملف
    category ENUM('profile', 'course', 'lesson', 'assignment', 'quiz', 'meal', 'document', 'video', 'audio', 'image', 'other') NOT NULL,
    subcategory VARCHAR(50) NULL,

    -- ربط الملف
    related_id INT NULL,
    related_type VARCHAR(50) NULL,

    -- معلومات إضافية للوسائط
    width INT NULL,
    height INT NULL,
    duration DECIMAL(8,2) NULL,
    thumbnail VARCHAR(255) NULL,

    -- معلومات الرفع
    uploaded_by INT NOT NULL,
    upload_source VARCHAR(50) DEFAULT 'web',

    -- حالة الملف
    status ENUM('uploading', 'processing', 'completed', 'failed', 'deleted') DEFAULT 'uploading',
    is_public BOOLEAN DEFAULT FALSE,
    is_downloadable BOOLEAN DEFAULT TRUE,

    -- أمان
    access_level ENUM('public', 'registered', 'enrolled', 'private') DEFAULT 'private',
    download_count INT DEFAULT 0,

    -- معلومات إضافية
    metadata JSON NULL,
    alt_text VARCHAR(255) NULL,
    description TEXT NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,

    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,

    UNIQUE KEY unique_hash (file_hash),
    INDEX idx_file_name (file_name),
    INDEX idx_file_type (file_type),
    INDEX idx_category (category),
    INDEX idx_related (related_id, related_type),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB;

-- ===================================================================
-- 14. جدول المدفوعات المحسن (Payments)
-- ===================================================================
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,

    -- معلومات الدفع
    payment_id VARCHAR(100) NOT NULL UNIQUE,
    transaction_id VARCHAR(100) NULL,

    -- معلومات المستخدم
    user_id INT NOT NULL,
    course_id INT NULL,
    enrollment_id INT NULL,

    -- تفاصيل المبلغ
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'SAR',
    original_amount DECIMAL(10,2) NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    commission_amount DECIMAL(10,2) DEFAULT 0,
    net_amount DECIMAL(10,2) NOT NULL,

    -- طريقة الدفع
    payment_method ENUM('credit_card', 'debit_card', 'paypal', 'bank_transfer', 'wallet', 'cash', 'other') NOT NULL,
    payment_gateway VARCHAR(50) NULL,

    -- حالة الدفع
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'partially_refunded') DEFAULT 'pending',

    -- تواريخ مهمة
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    failed_at TIMESTAMP NULL,
    refunded_at TIMESTAMP NULL,

    -- معلومات إضافية
    description TEXT NULL,
    notes TEXT NULL,
    failure_reason TEXT NULL,

    -- معلومات الاسترداد
    refund_amount DECIMAL(10,2) DEFAULT 0,
    refund_reason TEXT NULL,
    refunded_by INT NULL,

    -- معلومات الفاتورة
    invoice_number VARCHAR(50) NULL,
    receipt_url VARCHAR(500) NULL,

    -- بيانات خارجية
    gateway_response JSON NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
    FOREIGN KEY (enrollment_id) REFERENCES course_enrollments(id) ON DELETE SET NULL,
    FOREIGN KEY (refunded_by) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_payment_id (payment_id),
    INDEX idx_user (user_id),
    INDEX idx_course (course_id),
    INDEX idx_enrollment (enrollment_id),
    INDEX idx_status (status),
    INDEX idx_payment_date (payment_date),
    INDEX idx_amount (amount)
) ENGINE=InnoDB;

-- ===================================================================
-- 15. جدول إعدادات النظام (System Settings)
-- ===================================================================
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value LONGTEXT NULL,
    setting_type ENUM('string', 'number', 'boolean', 'json', 'text') DEFAULT 'string',
    category VARCHAR(50) DEFAULT 'general',
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT NULL,

    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_key (setting_key),
    INDEX idx_category (category),
    INDEX idx_public (is_public)
) ENGINE=InnoDB;

-- ===================================================================
-- 16. جدول سجل الأنشطة (Activity Logs)
-- ===================================================================
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NULL,
    entity_id INT NULL,
    details TEXT NULL,

    -- معلومات الطلب
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    request_url VARCHAR(500) NULL,
    request_method VARCHAR(10) NULL,

    -- معلومات إضافية
    old_values JSON NULL,
    new_values JSON NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB;

-- ===================================================================
-- 17. جدول الجلسات (User Sessions)
-- ===================================================================
CREATE TABLE user_sessions (
    session_id VARCHAR(128) PRIMARY KEY,
    user_id INT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NOT NULL,
    session_data LONGTEXT NOT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_user_id (user_id),
    INDEX idx_expires (expires_at),
    INDEX idx_active (is_active),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB;

-- ===================================================================
-- 18. جداول نظام الوجبات (Meals System)
-- ===================================================================

-- جدول فئات الوجبات
CREATE TABLE meal_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    icon VARCHAR(50) NULL,
    color VARCHAR(7) DEFAULT '#667eea',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
) ENGINE=InnoDB;

-- جدول الوجبات
CREATE TABLE meals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT NULL,
    category_id INT NOT NULL,
    image VARCHAR(255) NULL,

    -- معلومات غذائية
    calories DECIMAL(8,2) DEFAULT 0,
    protein DECIMAL(8,2) DEFAULT 0,
    carbs DECIMAL(8,2) DEFAULT 0,
    fat DECIMAL(8,2) DEFAULT 0,
    fiber DECIMAL(8,2) DEFAULT 0,

    -- معلومات الوجبة
    prep_time INT DEFAULT 0,
    cook_time INT DEFAULT 0,
    total_time INT DEFAULT 0,
    servings INT DEFAULT 1,
    difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',

    -- تصنيفات
    meal_type ENUM('breakfast', 'lunch', 'dinner', 'snack', 'dessert') NOT NULL,
    diet_type SET('vegetarian', 'vegan', 'gluten_free', 'dairy_free', 'keto', 'low_carb', 'high_protein') NULL,

    -- تقييم
    rating DECIMAL(3,2) DEFAULT 0,
    total_reviews INT DEFAULT 0,

    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    created_by INT NOT NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (category_id) REFERENCES meal_categories(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_category (category_id),
    INDEX idx_meal_type (meal_type),
    INDEX idx_status (status),
    INDEX idx_rating (rating),
    FULLTEXT idx_search (name, description)
) ENGINE=InnoDB;

-- ===================================================================
-- 19. إدراج البيانات الأساسية (Initial Data)
-- ===================================================================

-- إدراج فئات الكورسات الأساسية
INSERT INTO categories (name, slug, description, icon, color, sort_order) VALUES
('البرمجة وتطوير المواقع', 'programming-web-development', 'تعلم لغات البرمجة وتطوير المواقع والتطبيقات', 'fas fa-code', '#667eea', 1),
('التصميم الجرافيكي', 'graphic-design', 'تصميم الشعارات والهويات البصرية والمطبوعات', 'fas fa-paint-brush', '#f093fb', 2),
('التسويق الرقمي', 'digital-marketing', 'استراتيجيات التسويق الإلكتروني ووسائل التواصل الاجتماعي', 'fas fa-bullhorn', '#4facfe', 3),
('إدارة الأعمال', 'business-management', 'مهارات الإدارة والقيادة وريادة الأعمال', 'fas fa-briefcase', '#43e97b', 4),
('اللغات', 'languages', 'تعلم اللغات المختلفة والترجمة', 'fas fa-language', '#38f9d7', 5),
('التصوير والفيديو', 'photography-video', 'فنون التصوير وإنتاج الفيديو والمونتاج', 'fas fa-camera', '#764ba2', 6);

-- إدراج فئات الوجبات الأساسية
INSERT INTO meal_categories (name, description, icon, color, sort_order) VALUES
('الإفطار', 'وجبات الإفطار الصحية والمغذية', 'fas fa-coffee', '#f093fb', 1),
('الغداء', 'وجبات الغداء المتوازنة', 'fas fa-utensils', '#4facfe', 2),
('العشاء', 'وجبات العشاء الخفيفة والصحية', 'fas fa-moon', '#43e97b', 3),
('الوجبات الخفيفة', 'وجبات خفيفة صحية بين الوجبات', 'fas fa-cookie-bite', '#667eea', 4),
('الحلويات', 'حلويات صحية ولذيذة', 'fas fa-birthday-cake', '#764ba2', 5),
('المشروبات', 'مشروبات صحية ومنعشة', 'fas fa-glass-whiskey', '#38f9d7', 6);

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, category, description, is_public) VALUES
('site_name', 'منصة التعلم الإلكتروني', 'string', 'general', 'اسم الموقع', TRUE),
('site_description', 'منصة تعليمية متطورة لتعلم مهارات المستقبل', 'string', 'general', 'وصف الموقع', TRUE),
('site_logo', '/assets/images/logo.png', 'string', 'general', 'شعار الموقع', TRUE),
('default_currency', 'SAR', 'string', 'payment', 'العملة الافتراضية', TRUE),
('commission_rate', '30', 'number', 'payment', 'نسبة العمولة من المبيعات', FALSE),
('max_file_size', '52428800', 'number', 'upload', 'الحد الأقصى لحجم الملف (50MB)', FALSE),
('allowed_file_types', '["jpg","jpeg","png","gif","pdf","doc","docx","mp4","avi","mov"]', 'json', 'upload', 'أنواع الملفات المسموحة', FALSE),
('email_verification_required', 'true', 'boolean', 'security', 'تفعيل البريد الإلكتروني مطلوب', FALSE),
('auto_approve_instructors', 'false', 'boolean', 'users', 'الموافقة التلقائية على المدربين', FALSE),
('maintenance_mode', 'false', 'boolean', 'general', 'وضع الصيانة', FALSE);

-- إنشاء مستخدم إداري افتراضي
INSERT INTO users (username, email, password, name, role, status, email_verified, created_at) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المدير العام', 'admin', 'active', TRUE, NOW());

-- تفعيل فحص المفاتيح الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- ===================================================================
-- انتهاء ملف قاعدة البيانات المحسنة
-- ===================================================================
